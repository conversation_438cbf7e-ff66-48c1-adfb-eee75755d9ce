<?php
/**
 * Quick test to verify product SEO is working correctly
 * 
 * Usage: Add this to your product template temporarily to see the SEO data
 * Or add it to your product function in HomeController for debugging
 */

// In your product template (templates/Home/product.php), add this temporarily:
/*
<div style="background: #f0f0f0; padding: 10px; margin: 10px; border: 1px solid #ccc;">
    <h3>DEBUG: SEO Data</h3>
    <?php if (isset($seo)): ?>
        <p><strong>Title:</strong> <?= h($seo['title'] ?? 'Not set') ?></p>
        <p><strong>Description:</strong> <?= h($seo['description'] ?? 'Not set') ?></p>
        <p><strong>Keywords:</strong> <?= h($seo['keywords'] ?? 'Not set') ?></p>
        <p><strong>OG Title:</strong> <?= h($seo['og_title'] ?? 'Not set') ?></p>
        <p><strong>OG Description:</strong> <?= h($seo['og_description'] ?? 'Not set') ?></p>
    <?php else: ?>
        <p style="color: red;">❌ SEO data not found!</p>
    <?php endif; ?>
</div>
*/

// Or add this to your HomeController product() function for debugging:
/*
// After calling getSeoData, add:
debug($this->get('seo')); // This will show the SEO data being passed to the view
*/

echo "This file contains debugging code snippets for testing product SEO.\n";
echo "See the comments in this file for instructions.\n";
?>
