<?php

namespace App\Controller;

use App\Controller\AppController;
use Cake\Http\Exception\NotFoundException;
use Cake\ORM\TableRegistry;
use Cake\Core\Configure;
use Cake\Utility\Text;

class CartController extends AppController
{
    protected $CartItems;
    protected $Cities;
    protected $Categories;
    protected $Users;
    protected $Customers;
    protected $OtpVerifications;
    protected $Banners;
    protected $BannerAds;
    protected $Offers;
    protected $Widgets;
    protected $Products;
    protected $ApiRequestLogs;
    protected $PaymentMethods;
    protected $Carts;
    protected $WidgetCategoryMappings;
    protected $Orders;
    protected $OrderItems;
    protected $SiteSettings;
    protected $Wishlists;
    protected $Reviews;
    protected $Showrooms;
    protected $Brands;
    protected $CustomerAddresses;
    protected $Loyalty;
    protected $ReviewImages;
    protected $ContentPages;
    protected $CustomerCards;
    protected $Invoices;
    protected $DeliveryCharges;
    protected $Transactions;
    protected $FaqCategories;
    protected $Faqs;
    protected $Wallets;
    protected $OrderCancellationCategories;
    protected $OrderCancellations;
    protected $OrderReturnCategories;
    protected $OrderReturns;
    protected $OrderTrackingHistories;
    protected $ProductImages;
    protected $OrderReturnImages;
    protected $ContactQueryTypes;
    protected $CartItemAttributes;
    protected $Countries;
    protected $States;

    public function initialize(): void
    {
        parent::initialize();
        $this->loadComponent('CartManager');
        $this->loadComponent('WebsiteFunction');
        $this->loadComponent('Global');
        $this->loadComponent('Media');
        $this->loadComponent('CustomPaginator');
        $this->loadComponent('Flash');
        $this->loadComponent('QibPayment');
        $this->viewBuilder()->setLayout('website');
        $this->CartItems = $this->fetchTable('CartItems');
        $this->Carts = $this->fetchTable('Carts');
        $this->Users = $this->fetchTable('Users');
        $this->CustomerAddresses = $this->fetchTable('CustomerAddresses');
        $this->Offers = $this->fetchTable('Offers');
        $this->OrderItems = $this->fetchTable('OrderItems');
        $this->Transactions = $this->fetchTable('Transactions');
        $this->Wishlists = $this->fetchTable('Wishlists');
        $this->Customers = $this->fetchTable('Customers');
        $this->Countries = $this->fetchTable('Countries');
        $this->Cities = $this->fetchTable('Cities');
        $this->Orders = $this->fetchTable('Orders');
        $this->ProductImages = TableRegistry::getTableLocator()->get('ProductImages');
    }

    public function beforeFilter(\Cake\Event\EventInterface $event)
    {
        parent::beforeFilter($event);

        // Allow unauthenticated access to cart operations for guest users
        $this->Authentication->addUnauthenticatedActions([
            'orderPlace',
            'paymentResponse',
            'addToCart',
            'cart',
            'updateQuantity',
            'removeCoupon',
            'addToWishlist',
            'removeFromWishlist',
            'wishlist',
            'updateInstallationCharge',
            'address',  // Allow access to address action so we can handle auth manually
            'getCartCount',  // Allow access to cart count for AJAX updates
            'checkCartBeforeCountryChange',
            'clearCartForCountryChange',
        ]);
    }
    

    public function cart()
    {
        $session = $this->request->getSession();
        $guestToken = $session->read('GuestToken') ?? null;
        $identity = $session->read('Auth.User');

        $customerId = null;
        $customerAddress = '';

        if (!$identity && !$guestToken) {
            $guestToken = Text::uuid();
            $session->write('GuestToken', $guestToken);
        }

        if ($identity && isset($identity->id) && !empty($identity->id)) {
            $user = $this->Users->find()
                ->contain(['Customers'])
                ->where(['Users.status' => 'A', 'Users.id' => $identity->id])
                ->first();

            if ($user && $user->customer) {
                $customerId = $user->customer->id;
                $this->handleGuestCartConversion($customerId);
                $customerAddress = $this->CustomerAddresses->listAddress($customerId);
            }
        }

        $cartData = $this->Carts->getCartData($customerId, $guestToken);

        $appliedCoupon = $session->read('applied_coupon');
        $availableCoupons = [];

        if ($cartData['total_items'] > 0) {
            $subtotal = (float)str_replace(',', '', $cartData['totalPrice']);
            $countryId = $session->read('siteSettings.country_id');
            $availableCoupons = $this->Carts->getAvailableCoupons($subtotal, $countryId, $customerId, $guestToken);

            // $availableCoupons = $this->CartManager->getAvailableCoupons($subtotal, $countryId, $customerId, $guestToken);
        }

        $orderSummary = $this->Carts->getOrderSummary(
            $customerId,
            $guestToken,
            0.00,
            $this->deliveryCharge,
            $appliedCoupon['discount_amount'] ?? 0,
            $appliedCoupon['code'] ?? null,
            $appliedCoupon['coupon_type'] ?? null
        );

        $this->set(array_merge(
            $cartData,
            [
                'customerAddress' => $customerAddress,
                'customerId' => $customerId,
                'appliedCoupon' => $appliedCoupon,
                'availableCoupons' => $availableCoupons,
                'orderSummary' => $orderSummary
            ]
        ));

        $this->viewBuilder()->setTemplatePath('Carts');
        $this->render('cart');
    }


    public function cart_old()
    {
      // dd($this->getRequest()->getSession()->read('siteSettings'));

        $session = $this->request->getSession();

        $guestToken = $session->read('GuestToken') ?? null;

        $identity = $session->read('Auth.User');

        $customerId = null;
        $customerAddress = '';
        $loyaltyDetails = '';

        if (!$identity && !$guestToken) {
            $guestToken = Text::uuid();
            $session->write('GuestToken', $guestToken);
        }

        if ($identity && isset($identity->id) && !empty($identity->id)) {
            $user = $this->Users->find()
                ->contain(['Customers'])
                ->where(['Users.status' => 'A', 'Users.id' => $identity->id])
                ->first();

            if ($user && $user->customer) {
                $customerId = $user->customer->id;
                // Convert guest cart to customer cart if needed
                $this->handleGuestCartConversion($customerId);
               $customerAddress = $this->CustomerAddresses->listAddress($customerId);
            }
        }

        $cartData = $this->CartManager->getCartData($customerId, $guestToken);
        // Get applied coupon from session
        $session = $this->request->getSession();
        $appliedCoupon = $session->read('applied_coupon');

        // Get available coupons for the current cart
        $availableCoupons = [];
        if ($cartData['total_items'] > 0) {
            $subtotal = (float) str_replace(',', '', $cartData['totalPrice']);
            $countryId = $this->getRequest()->getSession()->read('siteSettings.country_id');
            $availableCoupons = $this->CartManager->getAvailableCoupons($subtotal, $countryId, $customerId, $guestToken);
        }

        // Get order summary with coupon data
        $orderSummary = $this->CartManager->getOrderSummary(
            $customerId,
            $guestToken,
            0.00,
            $this->deliveryCharge,
            $appliedCoupon['discount_amount'] ?? 0,
            $appliedCoupon['code'] ?? null,
            $appliedCoupon['coupon_type'] ?? null
        );
    
         
        $this->set(array_merge(
            $cartData,
            [
                'customerAddress' => $customerAddress,
                'customerId' => $customerId,
                'appliedCoupon' => $appliedCoupon,
                'availableCoupons' => $availableCoupons,
                'orderSummary' => $orderSummary
            ]
        ));
        
        $this->viewBuilder()->setTemplatePath('Carts');
        $this->render('cart');
    }

    /**
     * Add a product to the cart
     *
     * @param int $productId
     * @return \Cake\Http\Response|null
     */
    public function updateInstallationCharge($cartItemId){
        $this->request->allowMethod(['post', 'ajax']);

        $installationCharge = (int)$this->request->getData('installation_charge');

        // Validate installation charge
        if ($installationCharge < 0) {
            \Cake\Log\Log::error('Invalid installation charge: ' . $installationCharge);
            return $this->response->withType('application/json')->withStatus(400)->withStringBody(json_encode([
                'success' => false,
                'message' => 'Invalid installation charge'
            ]));
        }

        $guestToken = $this->request->getSession()->read('GuestToken') ?? null;
        $identity = $this->request->getSession()->read('Auth.User');

        $customerId = ($identity && isset($identity->id) && !empty($identity->id)) ? $identity->id : null;

        if ($customerId) {
            // Update installation charge for logged-in user
            $result = $this->CartManager->updateInstallationCharge($cartItemId, $installationCharge, $customerId);
        } else {
            // Update installation charge for guest user
            $result = $this->CartManager->updateInstallationCharge($cartItemId, $installationCharge, null, $guestToken);
        }

        if ($result) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'success' => true,
                'message' => 'Installation charge updated successfully'
            ]));
        } else {
            return $this->response->withType('application/json')->withStatus(500)->withStringBody(json_encode([
                'success' => false,
                'message' => 'Failed to update installation charge'
            ]));
        }
    }

    public function addToCart($productId)
    {
        // Handle AJAX requests
        if ($this->request->is('ajax')) {
            // Set JSON response type immediately
            $this->response = $this->response->withType('application/json');

            try {
                $this->request->allowMethod(['post']);

            $quantity = $this->request->getData('quantity', 1);
            $installationCharge = $this->request->getData('installation_charge', 0);
            $result = $this->CartManager->addToCart($productId, null, $quantity, $installationCharge);

                if ($result) {
                    // Get updated cart count
                    $cartCount = $this->getUpdatedCartCount();

                    return $this->response->withStringBody(json_encode([
                        'status' => 'success',
                        'message' => __('Product added to cart successfully.'),
                        'cartCount' => $cartCount
                    ]));
                } else {
                    return $this->response->withStringBody(json_encode([
                        'status' => 'error',
                        'message' => __('Failed to add product to cart.')
                    ]));
                }
            } catch (\Exception $e) {
                \Cake\Log\Log::error('addToCart error: ' . $e->getMessage());
                return $this->response->withStatus(500)->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => __('Server error occurred')
                ]));
            }
        }

        // Handle regular requests (existing functionality)
        $result = $this->CartManager->addToCart($productId);

        if ($result) {
            // Use common toast functionality
            $this->request->getSession()->write('toast_message', [
                'message' => __('Product added to cart successfully.'),
                'type' => 'success'
            ]);
        } else {
            // Use common toast functionality
            $this->request->getSession()->write('toast_message', [
                'message' => __('Failed to add product to cart.'),
                'type' => 'error'
            ]);
        }

        return $this->redirect($this->referer());
    }


    public function updateQuantity()
    {
        // Set JSON response type immediately
        $this->response = $this->response->withType('application/json');

        try {
            $this->request->allowMethod(['post', 'ajax']);

            $cartItemId = $this->request->getData('cart_item_id');
            $productId = $this->request->getData('product_id');
            $newQuantity = (int)$this->request->getData('quantity');

            // Validate quantity
            if ($newQuantity < 1) {
                \Cake\Log\Log::error('Invalid quantity: ' . $newQuantity);
                return $this->response->withStatus(400)->withStringBody(json_encode([
                    'success' => false,
                    'message' => 'Invalid quantity'
                ]));
            }
        } catch (\Exception $e) {
            \Cake\Log\Log::error('updateQuantity method error: ' . $e->getMessage());
            return $this->response->withStatus(500)->withStringBody(json_encode([
                'success' => false,
                'message' => 'Server error occurred'
            ]));
        }

        // Must have either cart_item_id or product_id
        if (empty($cartItemId) && empty($productId)) {
            \Cake\Log\Log::error('Missing cart_item_id or product_id');
            return $this->response->withType('application/json')->withStatus(400)->withStringBody(json_encode([
                'success' => false,
                'message' => 'Cart item ID or Product ID is required'
            ]));
        }

        $guestToken = $this->request->getSession()->read('GuestToken') ?? null;
        $identity = $this->request->getSession()->read('Auth.User');



        $customerId = ($identity && isset($identity->id) && !empty($identity->id)) ? $identity->id : null;
        if (!empty($customerId)) {
            $user = $this->Users->find()
                ->contain(['Customers'])
                ->where(['Users.status' => 'A', 'Users.id' => $customerId])
                ->first();
            $customerId = $user?->customer?->id;
        }


        try {
            if (!$customerId && !$guestToken) {
                \Cake\Log\Log::error('No customer ID or guest token found');
                return $this->response->withStatus(400)->withStringBody(json_encode([
                    'success' => false,
                    'message' => 'Customer ID or Guest Token is required'
                ]));
            }

            $cartConditions = $customerId
                ? ['customer_id' => $customerId]
                : ['guest_token' => $guestToken];

            $cart = $this->Carts->find()->where($cartConditions)->first();
            if (!$cart) {
                \Cake\Log\Log::error('Cart not found with conditions: ' . json_encode($cartConditions));
                return $this->response->withStatus(400)->withStringBody(json_encode([
                    'success' => false,
                    'message' => 'Cart not found'
                ]));
            }

        // Handle different update scenarios
        if (!empty($cartItemId)) {
            // Update existing cart item by cart_item_id
            $cartItem = $this->CartItems->find()
                ->where(['id' => $cartItemId, 'cart_id' => $cart->id])
                ->first();

            if (!$cartItem) {
                return $this->response->withType('application/json')->withStatus(400)->withStringBody(json_encode([
                    'success' => false,
                    'message' => 'Cart item not found or does not belong to your cart'
                ]));
            }
        } elseif (!empty($productId)) {
            // Update existing cart item by product_id or create new one
            $cartItem = $this->CartItems->find()
                ->where(['product_id' => $productId, 'cart_id' => $cart->id])
                ->first();

            if (!$cartItem) {
                // Product not in cart, create new cart item
                $cartItem = $this->CartItems->newEmptyEntity();
                $cartItem->cart_id = $cart->id;
                $cartItem->product_id = $productId;
            }
        }


        $Products = TableRegistry::getTableLocator()->get('Products');
        $product = $Products->get($cartItem->product_id);
        $unitPrice = $product->promotion_price ?? 0;
        $cartItem->quantity = $newQuantity;
        $cartItem->price = $unitPrice * $newQuantity;



        if ($this->CartItems->save($cartItem)) {
            // Get updated cart count
            $cartCount = $this->getUpdatedCartCount();

            return $this->response->withType('application/json')->withStatus(200)->withStringBody(json_encode([
                'success' => true,
                'message' => __('Cart item updated successfully'),
                'cartCount' => $cartCount,
                'data' => [
                    'cart_item_id' => $cartItem->id,
                    'quantity' => $cartItem->quantity,
                    'price' => $cartItem->price,
                    'unit_price' => $unitPrice,
                    'installation_charge' => $cartItem->installation_charge ?? 0,
                ]
            ]));
        }

            \Cake\Log\Log::error('Failed to save cart item');
            return $this->response->withStatus(500)->withStringBody(json_encode([
                'success' => false,
                'message' => 'Failed to update cart item'
            ]));
        } catch (\Exception $e) {
            \Cake\Log\Log::error('updateQuantity database error: ' . $e->getMessage());
            return $this->response->withStatus(500)->withStringBody(json_encode([
                'success' => false,
                'message' => 'Database error occurred'
            ]));
        }
    }

    // remove  item from the cart completely
     public function remove($cartItemId)
    {
        $this->request->allowMethod(['post']);

        $guestToken = $this->request->getSession()->read('GuestToken') ?? null;
        $identity = $this->request->getSession()->read('Auth.User');

        $customerId = ($identity && isset($identity->id) && !empty($identity->id)) ? $identity->id : null;
        if (!empty($customerId)) {
            $users = $this->Users->find()
                ->contain(['Customers'])
                ->where(['Users.status' => 'A', 'Users.id' => $customerId])
                ->first();
            $customerId = $users?->customer?->id;
        }

        if (!$customerId && !$guestToken) {
            // Use common toast functionality
            $this->request->getSession()->write('toast_message', [
                'message' => __('Customer ID or Guest Token is required.'),
                'type' => 'error'
            ]);
            return $this->redirect($this->referer());
        }

        $cartConditions = $customerId
            ? ['customer_id' => $customerId]
            : ['guest_token' => $guestToken];

        $cart = $this->Carts->find()->where($cartConditions)->first();
        if (!$cart) {
            // Use common toast functionality
            $this->request->getSession()->write('toast_message', [
                'message' => __('Cart not found.'),
                'type' => 'error'
            ]);
            return $this->redirect($this->referer());
        }


        $cartItem = $this->CartItems->find()
            ->where([
                'cart_id' => $cart->id,
                'id' => $cartItemId
            ])
            ->first();

        if (!$cartItem) {
            // Use common toast functionality
            $this->request->getSession()->write('toast_message', [
                'message' => __('Cart item not found or invalid.'),
                'type' => 'error'
            ]);
            return $this->redirect($this->referer());
        }

        if ($this->CartItems->delete($cartItem)) {
            // Use common toast functionality
            $this->request->getSession()->write('toast_message', [
                'message' => __('Item removed from cart successfully.'),
                'type' => 'success'
            ]);
        } else {
            // Use common toast functionality
            $this->request->getSession()->write('toast_message', [
                'message' => __('Unable to remove item. Please try again.'),
                'type' => 'error'
            ]);
        }

        return $this->redirect($this->referer());
    }

    /**
     * AJAX endpoint to get current cart count
     */
    public function getCartCount()
    {
        // Set JSON response type immediately
        $this->response = $this->response->withType('application/json');

        try {
            $this->request->allowMethod(['get', 'post']);

            $session = $this->request->getSession();
            $guestToken = $session->read('GuestToken') ?? null;
            $identity = $session->read('Auth.User');
            $customerId = null;

            // Get customer ID if logged in
            if ($identity && isset($identity->id) && !empty($identity->id)) {
                $user = $this->Users->find()
                    ->contain(['Customers'])
                    ->where(['Users.status' => 'A', 'Users.id' => $identity->id])
                    ->first();

                if ($user && $user->customer) {
                    $customerId = $user->customer->id;
                }
            }

            // Build cart conditions
            $cartConditions = [];
            if ($customerId) {
                $cartConditions['customer_id'] = $customerId;
            } elseif ($guestToken !== null) {
                $cartConditions['guest_token'] = $guestToken;
            }

            // Get cart count
            $totalItems = 0;
            if (!empty($cartConditions)) {
                $cart = $this->Carts->find()
                    ->where($cartConditions)
                    ->contain(['CartItems'])
                    ->first();

                // Count total quantity of items, not just number of different products
                if ($cart && !empty($cart->cart_items)) {
                    $totalItems = 0;
                    foreach ($cart->cart_items as $item) {
                        $totalItems += $item->quantity;
                    }
                }
            }

            return $this->response->withStringBody(json_encode([
                'success' => true,
                'count' => $totalItems
            ]));
        } catch (\Exception $e) {
            \Cake\Log\Log::error('getCartCount error: ' . $e->getMessage());
            return $this->response->withStatus(500)->withStringBody(json_encode([
                'success' => false,
                'count' => 0,
                'message' => 'Error fetching cart count'
            ]));
        }
    }

    /**
     * Helper method to get updated cart count
     */
     private function getUpdatedCartCount()
        {
            $session = $this->request->getSession();
            $guestToken = $session->read('GuestToken') ?? null;
            $identity = $session->read('Auth.User');
            $customerId = null;

            // Get customer ID if logged in
            if ($identity && isset($identity->id) && !empty($identity->id)) {
                $user = $this->Users->find()
                    ->contain(['Customers'])
                    ->where(['Users.status' => 'A', 'Users.id' => $identity->id])
                    ->first();

                if ($user && $user->customer) {
                    $customerId = $user->customer->id;
                }
            }

            // Build cart conditions
            $cartConditions = [];
            if ($customerId) {
                $cartConditions['customer_id'] = $customerId;
            } elseif ($guestToken !== null) {
                $cartConditions['guest_token'] = $guestToken;
            }

            // Get cart count
            $totalItems = 0;
            if (!empty($cartConditions)) {
                $cart = $this->Carts->find()
                    ->where($cartConditions)
                    ->contain(['CartItems'])
                    ->first();

                // Count total quantity of items, not just number of different products
                if ($cart && !empty($cart->cart_items)) {
                    $totalItems = 0;
                    foreach ($cart->cart_items as $item) {
                        $totalItems += $item->quantity;
                    }
                }
            }

            return $totalItems;
        }

    public function address()
    {
        $session = $this->request->getSession();
        $identity = $session->read('Auth.User');

        if (empty($identity)) {
            // Set session flash for JavaScript to pick up
            $session->write('toast_message', [
                'message' =>  __('Customer need to login'),
                'type' => 'error'
            ]);
            return $this->redirect($this->referer());
        }
        $customerId = null;
        $customerAddress = '';

        if ($identity && isset($identity->id) && !empty($identity->id)) {
            $user = $this->Users->find()
                ->contain(['Customers'])
                ->where(['Users.status' => 'A', 'Users.id' => $identity->id])
                ->first();
            if ($user && $user->customer) {
                $customerId = $user->customer->id;

                // Convert guest cart to customer cart if needed
                $this->handleGuestCartConversion($customerId);

                $customerAddress = $this->CustomerAddresses->listAddress($customerId);
                $cartData = $this->CartManager->getCartData($customerId, null);
            }
        }

        // Load address form data using reusable model method
        $addressFormData = $this->CustomerAddresses->getAddressFormData();

        // Get applied coupon from session
        $appliedCoupon = $session->read('applied_coupon');
        
        // Get available coupons for the current cart
        $availableCoupons = [];
        if ($cartData['total_items'] > 0) {
            $subtotal = (float) str_replace(',', '', $cartData['totalPrice']);
            // Get country_id from site settings
            $countryId = $this->getRequest()->getSession()->read('siteSettings.country_id');
            $availableCoupons = $this->CartManager->getAvailableCoupons($subtotal, $countryId, $customerId, null);
        }

        // Get order summary with coupon data
        $orderSummary = $this->CartManager->getOrderSummary(
            $customerId,
            null,
             0,
            $this->deliveryCharge,
            $appliedCoupon['discount_amount'] ?? 0,
            $appliedCoupon['code'] ?? null,
            $appliedCoupon['coupon_type'] ?? null
        );

         $this->set(array_merge(
            $cartData,
            [
                'customerAddress' => $customerAddress,
                'customerId' => $customerId,
                'cartData' => $cartData,
                'appliedCoupon' => $appliedCoupon,
                'availableCoupons' => $availableCoupons,
                'orderSummary' => $orderSummary
            ],
            $addressFormData
        ));


        $this->viewBuilder()->setTemplatePath('Carts');
        $this->render('address');
    }

     public function removeAddress($id = null)
    {

        $this->request->allowMethod(['post', 'delete']);
        $auth = $this->request->getSession()->read('Auth.User');
        if (!$auth || !isset($auth->id) || empty($auth->id)) {
            return $this->redirect(['action' => 'login']);
        }

        $address = $this->CustomerAddresses->get($id);

        // Verify if the address belongs to the logged-in user's customer
        $users = $this->Users->find()
            ->contain(['Customers'])
            ->where(['Users.status' => 'A'])
            ->where(['Users.id' => $auth->id])
            ->first();

        if ($address->customer_id !== $users->customer->id) {
            // Use common toast functionality
            $this->request->getSession()->write('toast_message', [
                'message' => __('You are not authorized to delete this address.'),
                'type' => 'error'
            ]);
            return $this->redirect(['action' => 'address']);
        }

        if ($this->CustomerAddresses->delete($address)) {
            // Use common toast functionality
            $this->request->getSession()->write('toast_message', [
                'message' => __('The address has been deleted successfully.'),
                'type' => 'success'
            ]);
        } else {
            // Use common toast functionality
            $this->request->getSession()->write('toast_message', [
                'message' => __('The address could not be deleted. Please try again.'),
                'type' => 'error'
            ]);
        }

        return $this->redirect(['action' => 'address']);
    }

    public function addAddress()
    {
        $this->request->allowMethod(['get', 'post']);

        // Validate authentication and get customer ID
        $customerId = $this->_validateCustomerAuth();
        if (!$customerId) {
            return;
        }

        if ($this->request->is('post')) {
            return $this->_handleAddAddressRequest($customerId);
        }

        return $this->redirect(['action' => 'address']);
    }

    public function editAddress($id = null)
    {
        $this->request->allowMethod(['get', 'post']);

        // Validate authentication and get customer ID
        $customerId = $this->_validateCustomerAuth();
        if (!$customerId) {
            return;
        }

        // Get and validate address ownership
        $address = $this->_validateAddressOwnership($id, $customerId);
        if (!$address) {
            return;
        }

        // Handle GET request - return address data
        if ($this->request->is('get')) {
            return $this->_handleGetAddressRequest($address);
        }

        // Handle POST request - update address
        if ($this->request->is('post')) {
            return $this->_handleUpdateAddressRequest($address, $customerId);
        }

        return $this->redirect(['action' => 'address']);
    }

    /**
     * Validate customer authentication and return customer ID
     * Common method for address operations
     */
    private function _validateCustomerAuth()
    {
        $session = $this->request->getSession();
        $identity = $session->read('Auth.User');

        if (empty($identity)) {
            return $this->_handleAuthError('Login required.');
        }

        if (!isset($identity->id) || empty($identity->id)) {
            return $this->_handleAuthError('Invalid user session.');
        }

        $user = $this->Users->find()
            ->contain(['Customers'])
            ->where(['Users.status' => 'A', 'Users.id' => $identity->id])
            ->first();

        if (!$user || !$user->customer) {
            return $this->_handleAuthError('Customer not found.');
        }

        return $user->customer->id;
    }

    /**
     * Handle authentication errors with consistent response format
     */
    private function _handleAuthError($message)
    {
        if ($this->request->is('ajax')) {
            $this->viewBuilder()->setClassName('Json');
            $this->set(['success' => false, 'message' => $message]);
            $this->viewBuilder()->setOption('serialize', ['success', 'message']);
            return null;
        }

        $this->request->getSession()->write('toast_message', [
            'message' => $message,
            'type' => 'error'
        ]);
        $this->redirect(['action' => 'address']);
        return null;
    }

    /**
     * Validate address ownership
     */
    private function _validateAddressOwnership($id, $customerId)
    {
        try {
            $address = $this->CustomerAddresses->get($id);
        } catch (\Exception $e) {
            return $this->_handleAddressError('Address not found.');
        }

        if ($address->customer_id !== $customerId) {
            return $this->_handleAddressError('You are not authorized to edit this address.');
        }

        return $address;
    }

    /**
     * Handle address-related errors
     */
    private function _handleAddressError($message)
    {
        if ($this->request->is('ajax')) {
            $this->viewBuilder()->setClassName('Json');
            $this->set(['success' => false, 'message' => $message]);
            $this->viewBuilder()->setOption('serialize', ['success', 'message']);
            return null;
        }

        $this->request->getSession()->write('toast_message', [
            'message' => $message,
            'type' => 'error'
        ]);
        $this->redirect(['action' => 'address']);
        return null;
    }

    /**
     * Handle GET request for address data
     */
    private function _handleGetAddressRequest($address)
    {
        if ($this->request->is('ajax')) {
            $this->viewBuilder()->setClassName('Json');
            $this->set(['success' => true, 'address' => $address]);
            $this->viewBuilder()->setOption('serialize', ['success', 'address']);
        }
        return;
    }

    /**
     * Handle POST request for address update
     */
    private function _handleUpdateAddressRequest($address, $customerId)
    {
        $data = $this->request->getData();
        $data['customer_id'] = $customerId;
        $data['status'] = 'A';

        // Set default values for required fields
        $defaults = [
            'country_id' => 1,
            'state_id' => 1,
            'city_id' => 1,
            'municipality_id' => 1
        ];

        foreach ($defaults as $field => $defaultValue) {
            if (empty($data[$field])) {
                $data[$field] = $defaultValue;
            }
        }

        $address = $this->CustomerAddresses->patchEntity($address, $data);

        if ($this->CustomerAddresses->save($address)) {
            return $this->_handleAddressSuccess('Address updated successfully', $address);
        } else {
            return $this->_handleAddressValidationError($address, $data);
        }
    }

    /**
     * Handle successful address operations
     */
    private function _handleAddressSuccess($message, $address = null)
    {
        if ($this->request->is('ajax')) {
            $this->viewBuilder()->setClassName('Json');
            $response = ['success' => true, 'message' => $message];
            if ($address) {
                $response['address'] = $address;
            }
            $this->set($response);
            $this->viewBuilder()->setOption('serialize', array_keys($response));
            return;
        }

        $this->request->getSession()->write('toast_message', [
            'message' => $message,
            'type' => 'success'
        ]);
        return $this->redirect(['action' => 'address']);
    }

    /**
     * Handle address validation errors
     */
    private function _handleAddressValidationError($address, $data)
    {
        $errors = $address->getErrors();
        $errorMessage = __('The address could not be saved. Please check the following errors: ');

        foreach ($errors as $field => $fieldErrors) {
            foreach ($fieldErrors as $error) {
                $errorMessage .= $field . ': ' . $error . '; ';
            }
        }

        if ($this->request->is('ajax')) {
            $this->viewBuilder()->setClassName('Json');
            $this->set([
                'success' => false,
                'message' => $errorMessage,
                'errors' => $errors,
                'data' => $data
            ]);
            $this->viewBuilder()->setOption('serialize', ['success', 'message', 'errors', 'data']);
            return;
        }

        $this->request->getSession()->write('toast_message', [
            'message' => $errorMessage,
            'type' => 'error'
        ]);
        return;
    }

    /**
     * Handle POST request for adding new address
     */
    private function _handleAddAddressRequest($customerId)
    {
        $data = $this->request->getData();
        $data['customer_id'] = $customerId;
        $data['status'] = 'A';

        // Set default values for required fields
        $defaults = [
            'country_id' => 1,
            'state_id' => 1,
            'city_id' => 1,
            'municipality_id' => 1,
            'name' => 'Customer',
            'title' => ' '
        ];

        foreach ($defaults as $field => $defaultValue) {
            if (empty($data[$field])) {
                $data[$field] = $defaultValue;
            }
        }

        $address = $this->CustomerAddresses->newEmptyEntity();
        $address = $this->CustomerAddresses->patchEntity($address, $data);

        if ($this->CustomerAddresses->save($address)) {
            return $this->_handleAddressSuccess('Address has been added successfully.', $address);
        } else {
            return $this->_handleAddressValidationError($address, $data);
        }
    }

    /**
     * Common function to handle guest cart and wishlist conversion to customer cart and wishlist
     * This ensures proper quantity handling and prevents duplication
     */
    private function handleGuestCartConversion($customerId)
    {
        $session = $this->request->getSession();
        $guestToken = $session->read('GuestToken');

        if (empty($guestToken) || empty($customerId)) {
            return false;
        }



        // Find guest cart
        $guestCart = $this->Carts->find()
            ->where(['guest_token' => $guestToken])
            ->contain(['CartItems'])
            ->first();

        if (!$guestCart || empty($guestCart->cart_items)) {

            return false;
        }



        // Find or create customer cart
        $customerCart = $this->Carts->find()
            ->where(['customer_id' => $customerId])
            ->first();

        if (!$customerCart) {
            // Create new customer cart
            $customerCart = $this->Carts->newEntity([
                'customer_id' => $customerId,
                'guest_token' => null,
                'status' => 'A'
            ]);

            if (!$this->Carts->save($customerCart)) {
                \Cake\Log\Log::error('Failed to create customer cart');
                return false;
            }

        } else {
            \Cake\Log\Log::debug('Found existing customer cart with ID: ' . $customerCart->id);
        }

        $conversionSuccess = true;
        $itemsConverted = 0;

        // Process each guest cart item
        foreach ($guestCart->cart_items as $guestItem) {


            // Check if customer already has this product in their cart
            $existingCustomerItem = $this->CartItems->find()
                ->where([
                    'cart_id' => $customerCart->id,
                    'product_id' => $guestItem->product_id,
                    // 'product_variant_id' => $guestItem->product_variant_id
                ])
                ->first();

            if ($existingCustomerItem) {
                // Update existing customer cart item - ADD quantities

                $existingCustomerItem->quantity += $guestItem->quantity;
                $existingCustomerItem->customer_id = $customerId;

                // Recalculate price based on new quantity
                $Products = TableRegistry::getTableLocator()->get('Products');
                $product = $Products->get($guestItem->product_id);
                $unitPrice = $product->promotion_price ?? 0;
                $existingCustomerItem->price = $existingCustomerItem->quantity * $unitPrice;

                if ($this->CartItems->save($existingCustomerItem)) {
                    \Cake\Log\Log::debug('Updated existing customer item - New quantity: ' . $existingCustomerItem->quantity);
                    $itemsConverted++;
                } else {
                    \Cake\Log\Log::error('Failed to update existing customer cart item');
                    $conversionSuccess = false;
                }
            } else {
                // Create new customer cart item

                $newCustomerItem = $this->CartItems->newEntity([
                    'cart_id' => $customerCart->id,
                    'customer_id' => $customerId,
                    'product_id' => $guestItem->product_id,
                    // 'product_variant_id' => $guestItem->product_variant_id,
                    'quantity' => $guestItem->quantity,
                    'price' => $guestItem->price
                ]);

                if ($this->CartItems->save($newCustomerItem)) {

                    $itemsConverted++;
                } else {
                    \Cake\Log\Log::error('Failed to create new customer cart item');
                    $conversionSuccess = false;
                }
            }
        }

        // Handle wishlist conversion
        $wishlistConversionSuccess = $this->convertGuestWishlistToCustomer($guestToken, $customerId);

        if ($conversionSuccess && $itemsConverted > 0) {
            // Delete guest cart and its items
            $this->CartItems->deleteAll(['cart_id' => $guestCart->id]);
            $this->Carts->delete($guestCart);

            \Cake\Log\Log::debug('Guest cart conversion completed successfully - Items converted: ' . $itemsConverted);
        }

        if ($wishlistConversionSuccess || ($conversionSuccess && $itemsConverted > 0)) {
            // Clear guest token only if either cart or wishlist conversion was successful
            $session->delete('GuestToken');

            // Show success message
            $messages = [];
            if ($conversionSuccess && $itemsConverted > 0) {
                $messages[] = 'Your cart items have been saved to your account';
            }
            if ($wishlistConversionSuccess) {
                $messages[] = 'Your wishlist items have been saved to your account';
            }

            $session->write('toast_message', [
                'message' => implode(' and ', $messages),
                'type' => 'success'
            ]);

            return true;
        }

        \Cake\Log\Log::error('Guest cart and wishlist conversion failed or no items to convert');
        return false;
    }


    private function convertGuestWishlistToCustomer($guestToken, $customerId)
    {
        if (empty($guestToken) || empty($customerId)) {
            return false;
        }

        // Find guest wishlist items
        $guestWishlistItems = $this->Wishlists->find()
            ->where(['guest_token' => $guestToken])
            ->contain(['Products'])
            ->all();

        if ($guestWishlistItems->isEmpty()) {
            \Cake\Log\Log::debug('No guest wishlist items found');
            return false;
        }
        $conversionSuccess = true;
        $itemsConverted = 0;

        foreach ($guestWishlistItems as $guestWishlistItem) {
            // Check if customer already has this product in their wishlist
            $existingCustomerWishlistItem = $this->Wishlists->find()
                ->where([
                    'customer_id' => $customerId,
                    'product_id' => $guestWishlistItem->product_id
                ])
                ->first();

            if ($existingCustomerWishlistItem) {
                // Customer already has this item in wishlist, just delete the guest ite
                if ($this->Wishlists->delete($guestWishlistItem)) {
                    $itemsConverted++;
                } else {
                    \Cake\Log\Log::error('Failed to delete duplicate guest wishlist item');
                    $conversionSuccess = false;
                }
            } else {
                // Convert guest wishlist item to customer wishlist item
                $guestWishlistItem->customer_id = $customerId;
                $guestWishlistItem->guest_token = null;

                if ($this->Wishlists->save($guestWishlistItem)) {
                    $itemsConverted++;
                } else {
                    \Cake\Log\Log::error('Failed to convert guest wishlist item to customer wishlist');
                    $conversionSuccess = false;
                }
            }
        }

        if ($conversionSuccess && $itemsConverted > 0) {
            return true;
        }

        \Cake\Log\Log::error('Guest wishlist conversion failed or no items to convert');
        return false;
    }

    public function checkout()
    {
        $this->viewBuilder()->setTemplatePath('Carts');
        $selectedAddress = null;
        $customer = null;
        $addressId = $this->request->getQuery('address_id');
         
        if ($addressId && is_numeric($addressId)) {
            $session = $this->request->getSession();
            $identity = $session->read('Auth.User');

            if (!empty($identity) && isset($identity->id) && !empty($identity->id)) {
                try {
                    
                    $user = $this->Users->find()
                        ->contain(['Customers'])
                        ->where(['Users.status' => 'A', 'Users.id' => $identity->id])
                        ->first();

                    if ($user && $user->customer) {
                        $customerId = $user->customer->id;
                        $customer = $user->customer;
                        $customer->first_name = $user->first_name;
                        $customer->last_name = $user->last_name;
                        $customer->full_name = trim($user->first_name . ' ' . $user->last_name);
                        $customer->email = $user->email;

                        // Get the selected address and verify ownership
                        $selectedAddress = $this->CustomerAddresses->find()
                            ->contain(['Cities', 'States'])
                            ->where([
                                'CustomerAddresses.id' => $addressId,
                                'CustomerAddresses.customer_id' => $customerId,
                                'CustomerAddresses.status' => 'A' 
                            ])
                            ->first();

                        // Log for debugging
                        if (!$selectedAddress) {
                            $this->log("Address not found or access denied. Address ID: {$addressId}, Customer ID: {$customerId}", 'debug');
                        }
                         $authCountryId = $this->getRequest()->getSession()->read('siteSettings.country_id');
                        
                         $addressCountryId = $selectedAddress->country_id ?? null;
                        
                        if (!$selectedAddress || $authCountryId !== $addressCountryId) {

                               $session->write('toast_message', [
                                    'message' => __( 'The selected address is not valid for your country.'),
                                    'type' => 'error'
                                ]);
                               
                           // return $this->redirect($this->referer());
                           return $this->redirect(['controller' => 'Cart', 'action' => 'address']);
                        }
                    }
                } catch (\Exception $e) {
                    $this->log("Error loading address data: " . $e->getMessage(), 'error');
                    $selectedAddress = null;
                    $customer = null;
                }
            }
        }

        // Get order summary data
        $session = $this->request->getSession();
        $guestToken = $session->read('GuestToken') ?? null;

        // Get customer ID for order summary
        $orderCustomerId = null;
        if (!empty($identity) && isset($identity->id) && !empty($identity->id)) {
            $user = $this->Users->find()
                ->contain(['Customers'])
                ->where(['Users.status' => 'A', 'Users.id' => $identity->id])
                ->first();
            if ($user && $user->customer) {
                $orderCustomerId = $user->customer->id;
            }
        }

        // Get applied coupon from session
        $appliedCoupon = $session->read('applied_coupon');
        $cartData = $this->Carts->getCartData($orderCustomerId, $guestToken);

        // Get available coupons for the current cart
        $availableCoupons = [];
        if ($cartData['total_items'] > 0) {
            $subtotal = (float) str_replace(',', '', $cartData['totalPrice']);
            $countryId = $this->getRequest()->getSession()->read('siteSettings.country_id');
            $availableCoupons = $this->Carts->getAvailableCoupons($subtotal, $countryId, $orderCustomerId, $guestToken);
        }

        // Get order summary using CartManager component with coupon data
        $orderSummary = $this->Carts->getOrderSummary(
            $orderCustomerId,
            $guestToken,
            00.00,
             $this->deliveryCharge,
            $appliedCoupon['discount_amount'] ?? 0,
            $appliedCoupon['code'] ?? null,
            $appliedCoupon['coupon_type'] ?? null
        );

        // Set variables for the view
        $this->set(compact('selectedAddress', 'customer', 'orderSummary', 'appliedCoupon', 'availableCoupons'));
        $this->render('checkout');
    }

    public function applyCoupon()
    {
        $this->request->allowMethod(['post']);

        // if (!$this->request->is('ajax')) {
        //     throw new NotFoundException('Invalid request');
        // }

        $data = json_decode($this->request->getBody(), true);
        $couponCode = $data['coupon_code'] ?? '';
       

        if (empty($couponCode)) {
            $response = [
                'success' => false,
                'message' => __('Please enter a coupon code')
               
            ];
            return $this->response->withType('application/json')->withStringBody(json_encode($response));
        }

        // Get customer and cart data
        $session = $this->request->getSession();
        $identity = $session->read('Auth.User');
        $guestToken = $session->read('GuestToken') ?? null;
        $customerId = null;

        if (!empty($identity) && isset($identity->id) && !empty($identity->id)) {
            $user = $this->Users->find()
                ->contain(['Customers'])
                ->where(['Users.status' => 'A', 'Users.id' => $identity->id])
                ->first();
            if ($user && $user->customer) {
                $customerId = $user->customer->id;
            }
        }

        // Get current order summary to get subtotal
        $orderSummary = $this->Carts->getOrderSummary($customerId, $guestToken, 0.00, $this->deliveryCharge, 0.00, null, null);

        if (!$orderSummary['has_items']) {
            $response = [
                'success' => false,
                'message' => __('Your cart is empty')
            ];
            return $this->response->withType('application/json')->withStringBody(json_encode($response));
        }

        // Apply coupon using CartManager component
          $countryId = $this->getRequest()->getSession()->read('siteSettings.country_id');
        $couponResult = $this->CartManager->applyCoupon($couponCode, $orderSummary['subtotal'],$countryId);

        if ($couponResult['is_valid']) {
            // Store enhanced coupon data in session for checkout
            $session->write('applied_coupon', [
                'code' => $couponCode,
                'discount_amount' => $couponResult['discount_amount'],
                'discount_value' => $couponResult['discount_value'],
                'coupon_type' => $couponResult['coupon_type'],
                'coupon_details' => $couponResult['coupon_details'],
                'country-id'=>$countryId,
            ]);

            // Get updated order summary with coupon applied
            $updatedOrderSummary = $this->CartManager->getOrderSummary(
                $customerId,
                $guestToken,
                00.00,
                 $this->deliveryCharge,
                $couponResult['discount_amount'],
                $couponCode,
                $couponResult['coupon_type']
            );

            $response = [
                'success' => true,
                'message' => $couponResult['message'],
                'discount_amount' => $couponResult['discount_amount'],
                'coupon_type' => $couponResult['coupon_type'],
                'order_summary' => $updatedOrderSummary
            ];
        } else {
            $response = [
                'success' => false,
                'message' => $couponResult['message']
            ];
        }

        return $this->response->withType('application/json')->withStringBody(json_encode($response));
    }

    public function removeCoupon()
    {
        $this->request->allowMethod(['post']);

        // if (!$this->request->is('ajax')) {
        //     throw new NotFoundException('Invalid request');
        // }

        $session = $this->request->getSession();
        $appliedCoupon = $session->read('applied_coupon');

        if (!$appliedCoupon) {
            $response = [
                'success' => false,
                'message' => 'No coupon is currently applied'
            ];
            return $this->response->withType('application/json')->withStringBody(json_encode($response));
        }

        // Remove coupon from session
        $session->delete('applied_coupon');

        // Use CartManager component to get removal message
       // $removalResult = $this->CartManager->removeCoupon($appliedCoupon['code']);
        $removalResult = $this->Carts->removeCoupon($appliedCoupon['code']);

        $response = [
            'success' => true,
            'message' => $removalResult['message']
        ];

        return $this->response->withType('application/json')->withStringBody(json_encode($response));
    }

    public function getAvailableCoupons()
    {
        $this->request->allowMethod(['get', 'post']);

        if (!$this->request->is('ajax')) {
            throw new NotFoundException('Invalid request');
        }

        // Get customer and cart data
        $session = $this->request->getSession();
        $identity = $session->read('Auth.User');
        $guestToken = $session->read('GuestToken') ?? null;
        $customerId = null;

        if (!empty($identity) && isset($identity->id) && !empty($identity->id)) {
            $user = $this->Users->find()
                ->contain(['Customers'])
                ->where(['Users.status' => 'A', 'Users.id' => $identity->id])
                ->first();
            if ($user && $user->customer) {
                $customerId = $user->customer->id;
            }
        }

        // Get current order summary to get subtotal
        $orderSummary = $this->Carts->getOrderSummary($customerId, $guestToken, 0.00, $this->deliveryCharge, 0.00, null, null);

        if (!$orderSummary['has_items']) {
            $response = [
                'success' => false,
                'message' => __('Your cart is empty'),
                'coupons' => []
            ];
            return $this->response->withType('application/json')->withStringBody(json_encode($response));
        }

        // Get available coupons with country filtering and cart filtering
        $countryId = $this->getRequest()->getSession()->read('siteSettings.country_id');
        $availableCoupons = $this->Carts->getAvailableCoupons($orderSummary['subtotal'], $countryId, $customerId, $guestToken);

        $response = [
            'success' => true,
            'coupons' => $availableCoupons,
            'current_subtotal' => $orderSummary['subtotal']
        ];

        return $this->response->withType('application/json')->withStringBody(json_encode($response));
    }

    public function getCouponDetails()
    {
        $this->request->allowMethod(['get', 'post']);

        if (!$this->request->is('ajax')) {
            throw new NotFoundException('Invalid request');
        }

        $couponCode = $this->request->getQuery('code') ?? $this->request->getData('coupon_code');

        if (empty($couponCode)) {
            $response = [
                'success' => false,
                'message' => 'Coupon code is required'
            ];
            return $this->response->withType('application/json')->withStringBody(json_encode($response));
        }

        // Get coupon details from database
        $offer = $this->Offers->find()
            ->where([
                'offer_code' => strtoupper(trim($couponCode)),
                'status' => 'A'
            ])
            ->first();

        if ($offer) {
            $currentDate = date('Y-m-d H:i:s');
            $isActive = ($offer->offer_start_date <= $currentDate) &&
                       (empty($offer->offer_end_date) || $offer->offer_end_date >= $currentDate);

            $couponType = 'fixed';
            if ($offer->free_shipping == 1) {
                $couponType = 'shipping';
            } elseif ($offer->offer_type === 'Percentage') {
                $couponType = 'percentage';
            }

            $response = [
                'success' => true,
                'coupon' => [
                    'id' => $offer->id,
                    'code' => $offer->offer_code,
                    'name' => $offer->offer_name,
                    'description' => $offer->offer_description,
                    'type' => $couponType,
                    'discount' => (float) $offer->discount,
                    'min_cart_value' => (float) $offer->min_cart_value,
                    'max_discount' => (float) $offer->max_discount,
                    'free_shipping' => (bool) $offer->free_shipping,
                    'is_active' => $isActive,
                    'start_date' => $offer->offer_start_date->format('d M, Y'),
                    'end_date' => $offer->offer_end_date ? $offer->offer_end_date->format('d M, Y') : 'No expiry',
                    'terms_conditions' => $offer->terms_conditions
                ]
            ];
        } else {
            $response = [
                'success' => false,
                'message' => __('Coupon not found')
            ];
        }

        return $this->response->withType('application/json')->withStringBody(json_encode($response));
    }



    public function placeCodOrder()
    {
        $this->request->allowMethod(['post']);

        if (!$this->request->is('ajax')) {
            throw new NotFoundException('Invalid request');
        }
        $data = json_decode($this->request->getBody(), true);
        $selectedAddressId = $data['selected_address_id'] ?? null;

        if (!$selectedAddressId) {
            // Validate required fields only if no address is selected
            $requiredFields = ['first_name', 'last_name', 'email', 'phone', 'address_line1', 'city', 'postcode'];
            foreach ($requiredFields as $field) {
                if (empty($data[$field])) {
                    return $this->response->withType('application/json')->withStringBody(json_encode([
                        'success' => false,
                        'message' => "Please fill in the {$field} field"
                    ]));
                }
            }
        }

        // Get customer and cart data
        $session = $this->request->getSession();
        $identity = $session->read('Auth.User');
        $guestToken = $session->read('GuestToken') ?? null;
        $customerId = null;

        if (!empty($identity)) {
            $user = $this->Users->find()
                ->contain(['Customers'])
                ->where(['Users.status' => 'A', 'Users.id' => $identity->id])
                ->first();
            if ($user && $user->customer) {
                $customerId = $user->customer->id;
            }
        }

        $cartData = $this->Carts->getCartData($customerId, $guestToken);

        if (!$cartData || $cartData['total_items'] == 0) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'success' => false,
                'message' => __('Your cart is empty')
            ]));
        }
        $appliedCoupon = $session->read('applied_coupon');
        $discountAmount = $appliedCoupon ? $appliedCoupon['discount_amount'] : 0;
        $orderSummary = $this->Carts->getOrderSummary($customerId, $guestToken, 00.00, $this->deliveryCharge, $discountAmount, $appliedCoupon['code'] ?? null, $appliedCoupon['coupon_type'] ?? null);

        try {
            // Start transaction
           
        //    $this->Orders = $this->fetchTable('Orders');
        //     $this->OrderItems = $this->fetchTable('OrderItems');
        //     $this->Transactions = $this->fetchTable('Transactions');
           

            $connection = $this->Orders->getConnection();
            $connection->begin();

            // Use selected address or create new one
            $customerAddressId = null;
            $countryId = null;
            $cityId = null;

            if ($selectedAddressId) {
                $customerAddressId = $selectedAddressId;
                $this->CustomerAddresses = $this->fetchTable('CustomerAddresses');
                $selectedAddress = $this->CustomerAddresses->find()
                    ->where(['id' => $selectedAddressId])
                    ->first();
                  

                if ($selectedAddress) {
                    $countryId = $selectedAddress->country_id;
                    $cityId = $selectedAddress->city_id;
                }
            }
          
            // Generate order number
            $orderNumber = $this->generateOrderNumber();
            $orderData = [
                'customer_id' => $customerId,
                'customer_address_id' => $customerAddressId,
                'country_id' => $countryId,
                'city_id' => $cityId,
                'order_number' => $orderNumber,
                'order_date' => date('Y-m-d H:i:s'),
                'status' => 'Pending',
                'payment_method' => 'Cash on Delivery',
                'subtotal_amount' => (float) $orderSummary['subtotal'],
                //'delivery_charge' => (float) $orderSummary['shipping_cost'],
                'delivery_charge' => (float) $orderSummary['delivery_charge'],
                'discount_amount' => (float) $orderSummary['discount_amount'],
                'installation_amount' => (float) $orderSummary['installation_total'],
                'total_amount' => (float) $orderSummary['final_total'],
                'tax_amount' => (float) $orderSummary['tax_amount'],
                'delivery_mode' => 'delivery',
                'order_type' => 'Online',
                'order_notes' => $data['order_notes'] ?? null, 
                'delivery_date' => date('Y-m-d H:i:s', strtotime('+4 days')), 
                'created_by' => $customerId ?? null,
                'created_by_role' => $customerId ? 'Customer' : 'Guest',
                'payment_status' => 1,
            ];

            $order = $this->Orders->newEntity($orderData);

            if (!$this->Orders->save($order)) {
                throw new \Exception('Failed to create order');
            }

            // Create order items
            $this->createOrderItems($order->id, $cartData['cartItems']);

            // Create transaction record
            $this->createTransaction($order->id, (float) $orderSummary['final_total']);

            // Clear cart after successful order
             $this->clearCart($customerId, $guestToken);

            // Clear applied coupon
            $session->delete('applied_coupon');

            $connection->commit();

            // Send COD order confirmation email
             $this->sendCodOrderPlacedEmail($order, $cartData['cartItems'], $orderSummary);

            return $this->response->withType('application/json')->withStringBody(json_encode([
                'success' => true,
                'message' => __('Order placed successfully!'),
                'order_number' => $orderNumber,
                'order_id' => $order->id
            ]));

        } catch (\Exception $e) {
            $connection->rollback();
            $this->log('COD Order creation failed: ' . $e->getMessage(), 'error');

            return $this->response->withType('application/json')->withStringBody(json_encode([
                'success' => false,
                'message' => __('Failed to place order. Please try again.')
            ]));
        }
    }
    public function orderPlace()
    {
        // Only allow POST method
        $this->request->allowMethod(['post']);

        $data = $this->request->getData();
        $selectedAddressId = $data['selected_address_id'] ?? null;

        if (!$selectedAddressId) {
            // Validate required fields only if no address is selected
            $requiredFields = ['first_name', 'last_name', 'email', 'phone', 'address_line1', 'city', 'postcode'];
            foreach ($requiredFields as $field) {
                if (empty($data[$field])) {
                    return $this->response->withType('application/json')->withStringBody(json_encode([
                        'success' => false,
                        'message' => "Please fill in the {$field} field"
                    ]));
                }
            }
        }

        // Get customer and cart data
        $session = $this->request->getSession();
        $identity = $session->read('Auth.User');
        $guestToken = $session->read('GuestToken') ?? null;
        $customerId = null;

        if (!empty($identity)) {
            $user = $this->Users->find()
                ->contain(['Customers'])
                ->where(['Users.status' => 'A', 'Users.id' => $identity->id])
                ->first();
            if ($user && $user->customer) {
                $customerId = $user->customer->id;
            }
        }

        $cartData = $this->Carts->getCartData($customerId, $guestToken);

        if (!$cartData || $cartData['total_items'] == 0) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'success' => false,
                'message' => __('Your cart is empty')
            ]));
        }
        $appliedCoupon = $session->read('applied_coupon');
        $discountAmount = $appliedCoupon ? $appliedCoupon['discount_amount'] : 0;
        $orderSummary = $this->Carts->getOrderSummary($customerId, $guestToken, 00.00, $this->deliveryCharge, $discountAmount, $appliedCoupon['code'] ?? null, $appliedCoupon['coupon_type'] ?? null);

        try {
            // Start transaction
            $connection = $this->Orders->getConnection();
            $connection->begin();

            // Use selected address or create new one
            $customerAddressId = null;
            $countryId = null;
            $cityId = null;

            if ($selectedAddressId) {
                $customerAddressId = $selectedAddressId;
                $this->CustomerAddresses = $this->fetchTable('CustomerAddresses');
                $selectedAddress = $this->CustomerAddresses->find()
                    ->where(['id' => $selectedAddressId])
                    ->first();
                  

                if ($selectedAddress) {
                    $countryId = $selectedAddress->country_id;
                    $cityId = $selectedAddress->city_id;
                }
            }
          
            // Generate order number
            $orderNumber = $this->generateOrderNumber();
            $orderData = [
                'customer_id' => $customerId,
                'customer_address_id' => $customerAddressId,
                'country_id' => $countryId,
                'city_id' => $cityId,
                'order_number' => $orderNumber,
                'order_date' => date('Y-m-d H:i:s'),
                'status' => 'Pending',
                'payment_method' => $data['payment_method'] ?? 'Cash on Delivery',
                'subtotal_amount' => (float) $orderSummary['subtotal'],
                //'delivery_charge' => (float) $orderSummary['shipping_cost'],
                'delivery_charge' => (float) $orderSummary['delivery_charge'],
                'discount_amount' => (float) $orderSummary['discount_amount'],
                'installation_amount' => (float) $orderSummary['installation_total'],
                'total_amount' => (float) $orderSummary['final_total'],
                'tax_amount' => (float) $orderSummary['tax_amount'],
                'delivery_mode' => 'delivery',
                'order_type' => 'Online',
                'order_notes' => $data['order_notes'] ?? null, 
                'delivery_date' => date('Y-m-d H:i:s', strtotime('+4 days')), 
                'created_by' => $customerId ?? null,
                'created_by_role' => $customerId ? 'Customer' : 'Guest',
                'payment_status' => 0,
                'payment_response' => ''
            ];

            $order = $this->Orders->newEntity($orderData);

            if (!$this->Orders->save($order)) {
                throw new \Exception('Failed to create order');
            }

            // Create order items
            $this->createOrderItems($order->id, $cartData['cartItems']);

            // Create transaction record
            $this->createTransaction($order->id, (float) $orderSummary['final_total']);

            // Clear cart after successful order
            // $this->clearCart($customerId, $guestToken);

            // Clear applied coupon
            $session->delete('applied_coupon');

            $connection->commit();

            // Send COD order confirmation email
            // $this->sendCodOrderPlacedEmail($order, $cartData['cartItems'], $orderSummary);

             // Example payment data
            $data = [
                'referenceId' => $orderNumber,
                'amount' => (float) $orderSummary['final_total'],
                'returnUrl' => Configure::read('Settings.SITE_URL') . '/cart/payment-response/' . base64_encode($order->id),
                'name' => $data['first_name'] . ' ' . $data['last_name'],
                'address' => $data['address_line1'] . ' ' . $data['address_line2'],
                'city' => $data['city'],
                'state' => $data['state'],
                'phone' => $data['phone'],
                'email' => $data['email']
            ];
            // Generate form fields for payment gateway
            $formFields = $this->QibPayment->getFormFields($data);

            // Set form data to be used in the view
            $this->set(compact('formFields'));
            $this->viewBuilder()->disableAutoLayout();
            $this->render('/Carts/redirect_to_gateway');
            return;   
        } catch (\Exception $e) {
            $connection->rollback();
            $this->log('COD Order creation failed: ' . $e->getMessage(), 'error');

            if ($this->request->is('ajax')) {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'success' => false,
                    'message' => __('Failed to place order. Please try again.')
                ]));
            } else {
                $this->request->getSession()->write('toast_message', [
                    'message' => __('Failed to place order. Please try again.'),
                    'type' => 'error'
                ]);
                return $this->redirect($this->referer());
            }
        }
    }


    private function createCustomerAddress($customerId, $data)
    {
        $this->CustomerAddresses = $this->fetchTable('CustomerAddresses');

        // Get default country and city IDs (you may need to adjust these based on your database)
        $defaultCountryId = 1; // Assuming Qatar has ID 1
        $defaultCityId = 1; // Assuming Doha or default city has ID 1

        $addressData = [
            'customer_id' => $customerId,
            'first_name' => $data['first_name'],
            'last_name' => $data['last_name'],
            'email' => $data['email'],
            'phone' => $data['phone'],
            'address_line1' => $data['address_line1'],
            'address_line2' => $data['address_line2'] ?? null,
            'city' => $data['city'],
            'postcode' => $data['postcode'],
            'country' => $data['country'] ?? 'Qatar',
            'country_id' => $defaultCountryId,
            'city_id' => $defaultCityId,
            'is_default' => false,
            'status' => 'A'
        ];

        $address = $this->CustomerAddresses->newEntity($addressData);

        if ($this->CustomerAddresses->save($address)) {
            return $address->id;
        }

        return null;
    }

    private function generateOrderNumber()
    {
        return 'ORD-' . date('Ymd') . '-' . strtoupper(substr(uniqid(), -6));
    }

    private function createOrderItems($orderId, $cartItems)
    {
        foreach ($cartItems as $item) {
            // Ensure numeric values by removing any formatting
            $price = is_string($item['price']) ? (float) str_replace(',', '', $item['price']) : (float) $item['price'];
            $totalPrice = is_string($item['total_price']) ? (float) str_replace(',', '', $item['total_price']) : (float) $item['total_price'];

            $orderItemData = [
                'order_id' => $orderId,
                'product_id' => $item['product_id'],
                'product_variant_id' => $item['product_variant_id'] ?? null,
                'quantity' => (int) $item['quantity'],
                'price' => $price,
                'total_price' => $totalPrice,
                'installation_charge' => (int) ($item['installation_charge'] ?? 0),
                'installation_unit_price' => (float) ($item['installation_unit_price'] ?? 0),
                'status' => 'Pending',
                'stock_type' => 'In Stock',
                'quantity_required' => (int) $item['quantity'],
                'delivery_status' => 'pending'
            ];

            $orderItem = $this->OrderItems->newEntity($orderItemData);

            if (!$this->OrderItems->save($orderItem)) {
                throw new \Exception('Failed to create order item');
            }
        }
    }

    private function createTransaction($orderId, $amount)
    {
       
          $transactionData = [
                'order_id' => $orderId,
                'invoice_number' => $this->Transactions->generateUniqueInvoiceNum(),
                'transaction_number' => $this->Transactions->generateUniqueTransactionNum(),
                'transaction_date' => date('Y-m-d H:i:s'),
                'amount' => $amount,
                'payment_method' => 'Cash on Delivery', //$payment_method,
            ];

        $transaction = $this->Transactions->newEntity($transactionData);

        if (!$this->Transactions->save($transaction)) {
            throw new \Exception('Failed to create transaction record');
        }
    }

    private function clearCart($customerId, $guestToken)
    {

        if ($customerId) {
            $this->CartItems->deleteAll(['customer_id' => $customerId]);
            $this->Carts->deleteAll(['customer_id' => $customerId]);
        } elseif ($guestToken) {
            $this->CartItems->deleteAll(['customer_id' => $customerId]);
            $this->Carts->deleteAll(['guest_token' => $guestToken]);
        }
    }

        /**
     * Check if cart has items before country change
     */
    public function checkCartBeforeCountryChange_old()
    {
      
        $this->request->allowMethod(['post']);

        $session = $this->request->getSession();
        $identity = $session->read('Auth.User');
        $guestToken = $session->read('GuestToken');
        $customerId = null;

        // Get customer ID if logged in
        if ($identity && isset($identity->id) && !empty($identity->id)) {
            $user = $this->Users->find()
                ->contain(['Customers'])
                ->where(['Users.status' => 'A', 'Users.id' => $identity->id])
                ->first();

            if ($user && $user->customer) {
                $customerId = $user->customer->id;
            }
        }

        // Check if cart has items
        $cartData = $this->CartManager->getCartData($customerId, $guestToken);
        $hasItems = !empty($cartData['cartItems']) && count($cartData['cartItems']) > 0;

        // Check if coupon is applied
        $appliedCoupon = $session->read('applied_coupon');
        $hasCoupon = !empty($appliedCoupon);

        return $this->response->withType('application/json')->withStringBody(json_encode([
            'success' => true,
            'has_cart_items' => $hasItems,
            'has_coupon' => $hasCoupon,
            'cart_count' => count($cartData['cartItems'] ?? [])
        ]));
    }

    public function checkCartBeforeCountryChange()
    {
        $this->request->allowMethod(['post']);

        $session = $this->request->getSession();
        $identity = $session->read('Auth.User');
        $guestToken = $session->read('GuestToken');
        $customerId = null;

        // Get customer ID if logged in
        if ($identity && isset($identity->id) && !empty($identity->id)) {
            $user = $this->Users->find()
                ->contain(['Customers'])
                ->where(['Users.status' => 'A', 'Users.id' => $identity->id])
                ->first();

            if ($user && $user->customer) {
                $customerId = $user->customer->id;
            }
        }

        $cartData = [
            'cartItems' => [],
        ];

        // If GuestToken or CustomerId is missing, consider cart empty
        if (!empty($customerId) || !empty($guestToken)) {
            $cartData = $this->CartManager->getCartData($customerId, $guestToken);
        }

        $hasCartItems = !empty($cartData['cartItems']) && count($cartData['cartItems']) > 0;

        // Check for wishlist items
        $hasWishlistItems = false;
        $wishlistCount = 0;

        if ($customerId || $guestToken) {
            $conditions = [];
            if ($customerId) {
                $conditions['customer_id'] = $customerId;
            } else {
                $conditions['guest_token'] = $guestToken;
            }

            $wishlistItems = $this->Wishlists->find()
                ->where($conditions)
                ->all();

            $wishlistCount = $wishlistItems->count();
            $hasWishlistItems = $wishlistCount > 0;
        }

        // Show modal if there are items in cart OR wishlist
        $hasItems = $hasCartItems || $hasWishlistItems;

        // Check if coupon is applied
        $appliedCoupon = $session->read('applied_coupon');
        $hasCoupon = !empty($appliedCoupon);

        return $this->response->withType('application/json')->withStringBody(json_encode([
            'success' => true,
            'has_cart_items' => $hasCartItems,
            'has_wishlist_items' => $hasWishlistItems,
            'has_items' => $hasItems,
            'has_coupon' => $hasCoupon,
            'cart_count' => count($cartData['cartItems'] ?? []),
            'wishlist_count' => $wishlistCount
        ]));
    }


    /**
     * Clear cart and coupons when country changes
     */
    public function clearCartForCountryChange()
    {
        $this->request->allowMethod(['post']);

        $session = $this->request->getSession();
        $identity = $session->read('Auth.User');
        $guestToken = $session->read('GuestToken');
        $customerId = null;

        // Get customer ID if logged in
        if ($identity && isset($identity->id) && !empty($identity->id)) {
            $user = $this->Users->find()
                ->contain(['Customers'])
                ->where(['Users.status' => 'A', 'Users.id' => $identity->id])
                ->first();

            if ($user && $user->customer) {
                $customerId = $user->customer->id;
            }
        }

        try {
            // Clear cart items
            if ($customerId) {
                // Clear customer cart
                $cart = $this->Carts->find()->where(['customer_id' => $customerId])->first();
                if ($cart) {
                    $this->CartItems->deleteAll(['cart_id' => $cart->id]);
                    $this->Carts->delete($cart);
                }

                // Clear customer wishlist
                $this->Wishlists->deleteAll(['customer_id' => $customerId]);
            } elseif ($guestToken) {
                // Clear guest cart
                $cart = $this->Carts->find()->where(['guest_token' => $guestToken])->first();
                if ($cart) {
                    $this->CartItems->deleteAll(['cart_id' => $cart->id]);
                    $this->Carts->delete($cart);
                }

                // Clear guest wishlist
                $this->Wishlists->deleteAll(['guest_token' => $guestToken]);
            }

            // Clear applied coupon
            $session->delete('applied_coupon');

            return $this->response->withType('application/json')->withStringBody(json_encode([
                'success' => true,
                'message' => 'Cart, wishlist and coupons cleared successfully'
            ]));

        } catch (\Exception $e) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'success' => false,
                'message' => 'Error clearing cart and wishlist: ' . $e->getMessage()
            ]));
        }
    }

    public function paymentSuccess_old($orderId = null)
    {
        // Get order details if order ID is provided

        $countryId = $this->getRequest()->getSession()->read('siteSettings.country_id');
        //dd($countryId);
        $isArabic = ($countryId == 1);
        $order = null;
        $appliedCoupon = null;

        if ($orderId) {
            // $this->Orders = $this->fetchTable('Orders');
            $order = $this->Orders->find()
                ->contain(['OrderItems' => ['Products'], 'Transactions'])
                ->where(['Orders.id' => $orderId])
                ->first();
               

                 $productImagesTable = TableRegistry::getTableLocator()->get('ProductImages');
                if ($order && $order->order_items) {
                    foreach ($order->order_items as $item) {
                        $image = $productImagesTable->getDefaultProductImage($item->product_id);

                        if ($image) {
                            $item->product_image = $this->Media->getCloudFrontURL($image);
                        } else {
                            $item->product_image = null;
                        }
                        $product = $item->product;

                        // Choose the name field based on country
                        $productName = $isArabic ? $product->name_ar : $product->name;

                        // Optional: Store for use in view
                        $item->product_name = $productName;
                    }
                }
            
            // Get coupon information if it was applied
            if ($order && !empty($order->coupon_code)) {
                $appliedCoupon = [
                    'code' => $order->coupon_code,
                    'discount_amount' => $order->coupon_discount_amount ?? 0,
                    'coupon_type' => $order->coupon_type ?? 'fixed'
                ];
            }
        }

        $estimated_days=Configure::read('Settings.ESTIMATED_DATE');
        $this->set(compact('order','estimated_days', 'appliedCoupon'));
        $this->viewBuilder()->setTemplatePath('Carts');
        $this->render('payment_success');
    }



    public function paymentResponse($orderId = null)
    {

        $dataInfo = $this->request->getQueryParams();
        $countryId = $this->getRequest()->getSession()->read('siteSettings.country_id');
        $order = null;
        $appliedCoupon = null;

        try {
            // using order id update order status code write here
            $decodedOrderId = base64_decode($orderId, true);

            // Validate and decode orderId
            if (empty($orderId) || !is_string($orderId)) {
                throw new \Exception('Invalid order ID.');
            }
            if ($decodedOrderId === false || empty($decodedOrderId)) {
                throw new \Exception('Order ID decoding failed.');
            }

         

            // Fetch order details
            $order = $this->Orders->getDetailedOrder($decodedOrderId, $countryId);
            if (!$order) {
                throw new \Exception('Order not found.');
            }
            if($order->response_viewed == 1){
                // If response already viewed, redirect to success page
            //    return $this->redirect('/account/my-account#orders-tab');
            }
           
            $appliedCoupon = $this->Orders->extractAppliedCoupon($order);

            if (isset($dataInfo['status']) && trim($dataInfo['status']) == 'success') {
               
                $session = $this->request->getSession();
                $identity = $session->read('Auth.User');
                $guestToken = $session->read('GuestToken') ?? null;
                $customerId = null;
                if (!empty($identity)) {
                    $user = $this->Users->find()
                        ->contain(['Customers'])
                        ->where(['Users.status' => 'A', 'Users.id' => $identity->id])
                        ->first();
                    if ($user && $user->customer) {
                        $customerId = $user->customer->id;
                    }
                }
                $cartData = $this->Carts->getCartData($customerId, $guestToken);
                // $appliedCoupon = $session->read('applied_coupon');
                $discountAmount = $appliedCoupon ? $appliedCoupon['discount_amount'] : 0;
                $orderSummary = $this->Carts->getOrderSummary($customerId, $guestToken, 00.00, $this->deliveryCharge, $discountAmount, $appliedCoupon['code'] ?? null, $appliedCoupon['coupon_type'] ?? null);
              
                $this->clearCart($customerId, $guestToken);

                $this->sendCodOrderPlacedEmail($order, $cartData['cartItems'], $orderSummary);

                $order = $this->Orders->get($decodedOrderId);
                $order->payment_status = 1; // Assuming 1 means successful payment
                $order->payment_response = json_encode($dataInfo);
                $order->response_viewed = 1;
                $this->Orders->save($order);
            }
            if (isset($dataInfo['status']) && trim($dataInfo['status']) && trim($dataInfo['status']) == 'error') {
                $order = $this->Orders->get($decodedOrderId);
                $order->payment_status = 0; // Assuming 0 means failed payment
                $order->payment_response = json_encode($dataInfo);
                $order->response_viewed = 1;
                $this->Orders->save($order);
            }
            $order = $this->Orders->get($decodedOrderId);
            $order->response_viewed = 1;
            $this->Orders->save($order);

            $order = $this->Orders->getDetailedOrder($decodedOrderId, $countryId);
           
            $estimated_days = Configure::read('Settings.ESTIMATED_DATE');
            $this->set(compact('order', 'estimated_days', 'appliedCoupon','dataInfo'));
            $this->viewBuilder()->setTemplatePath('Carts');
            $this->render('payment_response');
        } catch (\Exception $e) {
            // Log error and show error message
            $this->log('paymentResponse error: ' . $e->getMessage(), 'error');
            $this->request->getSession()->write('toast_message', [
                'message' => __('Unable to load payment response: ') . $e->getMessage(),
                'type' => 'error'
            ]);
            return $this->redirect('/');
        }
    }


    public function paymentSuccess($orderId = null)
    {
        $countryId = $this->getRequest()->getSession()->read('siteSettings.country_id');
        $order = null;
        $appliedCoupon = null;

        if ($orderId) {
            $order = $this->Orders->getDetailedOrder($orderId, $countryId);
            $appliedCoupon = $this->Orders->extractAppliedCoupon($order);
        }

        $estimated_days = Configure::read('Settings.ESTIMATED_DATE');

        $this->set(compact('order', 'estimated_days', 'appliedCoupon'));
        $this->viewBuilder()->setTemplatePath('Carts');
        $this->render('payment_success');
    }


    public function addToWishlist()
    {
   
        $this->request->allowMethod(['post']);

        $session = $this->request->getSession();
        $identity = $session->read('Auth.User');
        $guestToken = $session->read('GuestToken');
        $customerId = null;

        // Handle logged-in users
        if ($identity) {
            $user = $this->Users->find()
                ->contain(['Customers' => function ($q) {
                    return $q->select(['id']);
                }])
                ->select(['Users.id'])
                ->where(['Users.status' => 'A', 'Users.id' => $identity->id])
                ->first();

            if ($user && $user->customer) {
                $customerId = $user->customer->id;
            }
        }

        // Handle guest users - generate guest token if needed
        if (!$customerId && !$guestToken) {
            $guestToken = Text::uuid();
            $session->write('GuestToken', $guestToken);
        }

        // Validate that we have either customer ID or guest token
        if (!$customerId && !$guestToken) {
            if ($this->request->is('ajax')) {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => __('Unable to identify user session.')
                ]));
            } else {
                // Use common toast functionality for non-AJAX requests
                $this->request->getSession()->write('toast_message', [
                    'message' => __('Unable to identify user session.'),
                    'type' => 'error'
                ]);
                return $this->redirect($this->referer());
            }
        }

        $productId = $this->request->getData('product_id');

        if (!$productId) {
            if ($this->request->is('ajax')) {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => __('Product ID is required.')
                ]));
            } else {
                // Use common toast functionality for non-AJAX requests
                $this->request->getSession()->write('toast_message', [
                    'message' => __('Product ID is required.'),
                    'type' => 'error'
                ]);
                return $this->redirect($this->referer());
            }
        }

        // Use WishlistsTable method to add to wishlist
        $result = $this->Wishlists->addToWishlist($customerId, $productId, $guestToken);

        if ($this->request->is('ajax')) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => $result['status'] == 200 ? 'success' : 'error',
                'message' => $result['message']
            ]));
        } else {
            // Use common toast functionality for non-AJAX requests
            $this->request->getSession()->write('toast_message', [
                'message' => $result['message'],
                'type' => $result['status'] == 200 ? 'success' : 'error'
            ]);
            return $this->redirect($this->referer());
        }
    }

    public function removeFromWishlist()
    {
        $this->request->allowMethod(['post']);

        $session = $this->request->getSession();
        $identity = $session->read('Auth.User');
        $guestToken = $session->read('GuestToken');
        $customerId = null;

        // Handle logged-in users
        if ($identity) {
            $user = $this->Users->find()
                ->contain(['Customers' => function ($q) {
                    return $q->select(['id']);
                }])
                ->select(['Users.id'])
                ->where(['Users.status' => 'A', 'Users.id' => $identity->id])
                ->first();

            if ($user && $user->customer) {
                $customerId = $user->customer->id;
            }
        }

        // Validate that we have either customer ID or guest token
        if (!$customerId && !$guestToken) {
            if ($this->request->is('ajax')) {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => __('Unable to identify user session.')
                ]));
            } else {
                // Use common toast functionality for non-AJAX requests
                $this->request->getSession()->write('toast_message', [
                    'message' => __('Unable to identify user session.'),
                    'type' => 'error'
                ]);
                return $this->redirect($this->referer());
            }
        }

        $productId = $this->request->getData('product_id');

        if (!$productId) {
            if ($this->request->is('ajax')) {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => __('Product ID is required.')
                ]));
            } else {
                // Use common toast functionality for non-AJAX requests
                $this->request->getSession()->write('toast_message', [
                    'message' => __('Product ID is required.'),
                    'type' => 'error'
                ]);
                return $this->redirect($this->referer());
            }
        }

        // Use WishlistsTable method to remove from wishlist
        $result = $this->Wishlists->removeFromWishlist($customerId, $productId, $guestToken);

        if ($this->request->is('ajax')) {
            if ($result) {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'status' => 'success',
                    'message' => __('Item successfully removed from your wishlist.')
                ]));
            } else {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => __('Item not found in your wishlist.')
                ]));
            }
        } else {
            // Use common toast functionality for non-AJAX requests
            if ($result) {
                $this->request->getSession()->write('toast_message', [
                    'message' => __('Item successfully removed from your wishlist.'),
                    'type' => 'success'
                ]);
            } else {
                $this->request->getSession()->write('toast_message', [
                    'message' => __('Item not found in your wishlist.'),
                    'type' => 'error'
                ]);
            }
            return $this->redirect($this->referer());
        }
    }

    public function wishlist_old()
    {
        $session = $this->request->getSession();
        $identity = $session->read('Auth.User');
        $guestToken = $session->read('GuestToken');
        $customerId = null;

        // Handle logged-in users
        if ($identity) {
            $user = $this->Users->find()
                ->contain(['Customers' => function ($q) {
                    return $q->select(['id']);
                }])
                ->select(['Users.id'])
                ->where(['Users.status' => 'A', 'Users.id' => $identity->id])
                ->first();

            if ($user && $user->customer) {
                $customerId = $user->customer->id;

                // Convert guest cart and wishlist to customer cart and wishlist if needed
                $this->handleGuestCartConversion($customerId);
            }
        }

        // Handle guest users - generate guest token if needed
        if (!$customerId && !$guestToken) {
            $guestToken = Text::uuid();
            $session->write('GuestToken', $guestToken);
        }

        // Validate that we have either customer ID or guest token
        if (!$customerId && !$guestToken) {
            // Use common toast functionality
            $this->request->getSession()->write('toast_message', [
                'message' => __('Unable to access wishlist.'),
                'type' => 'error'
            ]);
            return $this->redirect(['controller' => 'Home', 'action' => 'index']);
        }

        // Build conditions for wishlist query
        $conditions = [];
        if ($customerId) {
            $conditions['customer_id'] = $customerId;
        } else {
            $conditions['guest_token'] = $guestToken;
        }

        // Load Wishlists table and get wishlist items
        $wishlistItems = $this->Wishlists->find()
            ->where($conditions)
            ->contain([
                'Products' => function ($q) {
                    return $q->select([
                        'Products.id',
                        'Products.name',
                        'Products.description',
                        'Products.product_price',
                        'Products.sales_price',
                        'Products.promotion_price',
                        'Products.purchase_price',
                        'Products.status'
                    ]);
                }
            ])
            ->order(['Wishlists.created' => 'DESC'])
            ->all();

        // Process wishlist items to add additional data
        $processedWishlistItems = [];
        foreach ($wishlistItems as $item) {
            if ($item->product && $item->product->status === 'A') {
                // // Get product rating
                // $item->product->rating = $this->Reviews->getAverageRating($item->product->id);
                // $item->product->total_review = $this->Reviews->getTotalReviews($item->product->id);

                // // Get product discount
                // $item->product->discount = $this->Products->getDiscount($item->product->id);

                // Get product image
                $item->product->product_image = '';
                $image = $this->ProductImages->getDefaultProductImage($item->product->id);
              
                if ($image) {
                    $item->product->product_image = $this->Media->getCloudFrontURL($image);
  
                } else {
                    $item->product->product_image = $this->request->getAttribute('webroot') . 'img/no-img.jpg';
                }

                $processedWishlistItems[] = $item;
            }
        }
       
        $this->set(compact('processedWishlistItems'));
        $this->viewBuilder()->setTemplatePath('Carts');
        $this->render('wishlist');
    }

    public function wishlist()
    {
        $session = $this->request->getSession();
        $identity = $session->read('Auth.User');
        $guestToken = $session->read('GuestToken');
        $customerId = null;

        if ($identity) {
            $user = $this->Users->find()
                ->contain(['Customers' => function ($q) {
                    return $q->select(['id']);
                }])
                ->select(['Users.id'])
                ->where(['Users.status' => 'A', 'Users.id' => $identity->id])
                ->first();

            if ($user && $user->customer) {
                $customerId = $user->customer->id;
                $this->handleGuestCartConversion($customerId);
            }
        }

        if (!$customerId && !$guestToken) {
            $guestToken = Text::uuid();
            $session->write('GuestToken', $guestToken);
        }

        if (!$customerId && !$guestToken) {
            $this->request->getSession()->write('toast_message', [
                'message' => __('Unable to access wishlist.'),
                'type' => 'error'
            ]);
            return $this->redirect(['controller' => 'Home', 'action' => 'index']);
        }


        $wishlistItems = $this->Wishlists->getWishlistItems($customerId, $guestToken);

        $this->set('processedWishlistItems', $wishlistItems);
        $this->viewBuilder()->setTemplatePath('Carts');
        $this->render('wishlist');
    }


    private function convertGuestCartToCustomerCart($guestToken, $customerId)
    {
        try {
            // Find the guest cart
            $guestCart = $this->Carts->find()
                ->where(['guest_token' => $guestToken])
                ->first();

            if (!$guestCart) {
                // No guest cart found, nothing to convert
                return true;
            }

            // Check if customer already has a cart
            $customerCart = $this->Carts->find()
                ->where(['customer_id' => $customerId])
                ->first();

            if ($customerCart) {
                // Customer already has a cart, merge the items
                $this->mergeCartItems($guestCart->id, $customerCart->id, $customerId);

                // Delete the guest cart after merging
                $this->Carts->delete($guestCart);
            } else {
                // Customer doesn't have a cart, convert the guest cart
                $guestCart->customer_id = $customerId;
                //$guestCart->guest_token = null; // Clear the guest token

                if (!$this->Carts->save($guestCart)) {
                    \Cake\Log\Log::error('Failed to convert guest cart to customer cart for customer ID: ' . $customerId);
                    return false;
                }

                // Update customer_id in all cart items for this cart
                $this->updateCartItemsCustomerId($guestCart->id, $customerId);
            }

            \Cake\Log\Log::info('Successfully converted guest cart to customer cart for customer ID: ' . $customerId);
            return true;

        } catch (\Exception $e) {
            \Cake\Log\Log::error('Error converting guest cart to customer cart: ' . $e->getMessage());
            return false;
        }
    }

    private function mergeCartItems($guestCartId, $customerCartId, $customerId)
    {
        try {
            // Get all items from guest cart
            $guestCartItems = $this->CartItems->find()
                ->where(['cart_id' => $guestCartId])
                ->all();

            foreach ($guestCartItems as $guestItem) {
                // Check if customer cart already has this product
                $existingItem = $this->CartItems->find()
                    ->where([
                        'cart_id' => $customerCartId,
                        'product_id' => $guestItem->product_id
                    ])
                    ->first();

                if ($existingItem) {
                    // Product already exists, update quantity and price
                    $existingItem->quantity += $guestItem->quantity;
                    $existingItem->price += $guestItem->price;

                    if (!$this->CartItems->save($existingItem)) {
                        \Cake\Log\Log::error('Failed to update existing cart item during merge');
                    }
                } else {
                    // Product doesn't exist, move the item to customer cart
                    $guestItem->cart_id = $customerCartId;
                    $guestItem->customer_id = $customerId; // Set customer_id in cart item

                    if (!$this->CartItems->save($guestItem)) {
                        \Cake\Log\Log::error('Failed to move cart item during merge');
                    }
                }
            }

            return true;

        } catch (\Exception $e) {
            \Cake\Log\Log::error('Error merging cart items: ' . $e->getMessage());
            return false;
        }

    }


    private function updateCartItemsCustomerId($cartId, $customerId)
    {
        try {
            // Update all cart items for this cart to include customer_id
            $cartItems = $this->CartItems->find()
                ->where(['cart_id' => $cartId])
                ->all();

            foreach ($cartItems as $cartItem) {
                $cartItem->customer_id = $customerId;

                if (!$this->CartItems->save($cartItem)) {
                    \Cake\Log\Log::error('Failed to update customer_id for cart item ID: ' . $cartItem->id);
                    return false;
                }
            }

            \Cake\Log\Log::info('Successfully updated customer_id for all cart items in cart ID: ' . $cartId);
            return true;

        } catch (\Exception $e) {
            \Cake\Log\Log::error('Error updating cart items customer_id: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Send COD order confirmation email to customer
     */
    private function sendCodOrderPlacedEmail($order, $cartItems, $orderSummary)
    {
        try {
           
            // Get customer information
            $customer = null;
            $customerEmail = null;
            $customerName = 'Guest Customer';

            if ($order->customer_id) {
                $customer = $this->Customers->find()
                    ->contain(['Users'])
                    ->where(['Customers.id' => $order->customer_id])
                    ->first();

                if ($customer && $customer->user) {
                    $customerEmail = $customer->user->email;
                    $customerName = trim($customer->user->first_name . ' ' . $customer->user->last_name);
                }
            }

            // Skip email if no customer email found
            if (!$customerEmail) {
                $this->log('COD Order placed email skipped: No customer email found for order ' . $order->order_number, 'info');
                return false;
            }

            // Get delivery address
            $deliveryAddress = 'Address not available';
            if ($order->customer_address_id) {
                $address = $this->CustomerAddresses->find()
                    ->contain([
                        'Cities' => function ($q) {
                            return $q->select(['id', 'city_name', 'state_id']);
                        },
                        'States' => function ($q) {
                            return $q->select(['id', 'state_name', 'country_id'])
                                ->contain(['Countries' => function ($q2) {
                                    return $q2->select(['id', 'name']);
                                }]);
                        }
                    ])
                    ->where(['CustomerAddresses.id' => $order->customer_address_id])
                    ->first();

                if ($address) {
                    $addressParts = [];

                    // Add house number if available
                    if (!empty($address->house_no)) {
                        $addressParts[] = $address->house_no;
                    }

                    // Add address line 1 (correct field name)
                    if (!empty($address->address_line1)) {
                        $addressParts[] = $address->address_line1;
                    }

                    // Add address line 2 (correct field name)
                    if (!empty($address->address_line2)) {
                        $addressParts[] = $address->address_line2;
                    }

                    // Add landmark
                    if (!empty($address->landmark)) {
                        $addressParts[] = $address->landmark;
                    }

                    // Add city (correct field name)
                    if ($address->city && !empty($address->city->city_name)) {
                        $addressParts[] = $address->city->city_name;
                    }

                    // Add state
                    if ($address->state && !empty($address->state->state_name)) {
                        $addressParts[] = $address->state->state_name;
                    }

                    // Add country
                    if ($address->state && $address->state->country && !empty($address->state->country->name)) {
                        $addressParts[] = $address->state->country->name;
                    }

                    // Add zipcode
                    if (!empty($address->zipcode)) {
                        $addressParts[] = $address->zipcode;
                    }

                    $deliveryAddress = implode(', ', array_filter($addressParts));
                }
            }

            // Prepare order items for email
            $emailOrderItems = [];
            foreach ($cartItems as $item) {
                $emailOrderItems[] = [
                    'product_name' => $item['product_name'],
                    'quantity' => $item['quantity'],
                    'price_formatted' => $this->Orders->formatOrderAmount($item['price'],$order->country_id ),
                    'total_formatted' => $this->Orders->formatOrderAmount(($item['price'] * $item['quantity']),$order->country_id )
                ];
            }

            // Calculate estimated delivery date
            // $estimatedDays = \Cake\Core\Configure::read('Settings.ESTIMATED_DATE', 7);
            // $estimatedDelivery = date('M d, Y', time() + ($estimatedDays * 24 * 60 * 60));

            // Get coupon information if applied
            $appliedCoupon = null;
            if (!empty($order->coupon_code)) {
                $appliedCoupon = [
                    'code' => $order->coupon_code,
                    'discount_amount' => $this->Orders->formatOrderAmount($order->coupon_discount_amount ?? 0, $order->country_id),
                    'coupon_type' => $order->coupon_type ?? 'fixed'
                ];
            }

            // Prepare email data
            $emailData = [
                'customer_name' => $customerName,
                'customer_email' => $customerEmail,
                'order_number' => $order->order_number,
                'order_date' => date('M d, Y', strtotime($order->order_date)),
                'order_status' => $order->status,
                'total_amount_formatted' => $this->Orders->formatOrderAmount($order->total_amount,$order->country_id ),
                'discount_amount'=>$this->Orders->formatOrderAmount($order->discount_amount,$order->country_id ),
                'subtotal'=>$this->Orders->formatOrderAmount($order->subtotal_amount,$order->country_id ),
                'delivery_charge'=>$this->Orders->formatOrderAmount($order->delivery_charge,$order->country_id ),
                'installation_amount'=>$this->Orders->formatOrderAmount($order->installation_amount ?? 0,$order->country_id ),
                'tax_amount'=>$this->Orders->formatOrderAmount($order->tax_amount,$order->country_id ),
                //'total_amount_formatted' => $this->formatCurrency($order->total_amount),
                'order_items' => $emailOrderItems,
                'delivery_address' => $deliveryAddress,
                // 'estimated_delivery' => $estimatedDelivery,
                'order_notes' => $order->order_notes,
                'applied_coupon' => $appliedCoupon,
                'track_order_url' => $this->request->scheme() . '://' . $this->request->host() . '/account/orders'
            ];

            // Send email
            $subject = 'COD Order Placed - ' . $order->order_number;
            $template = 'order_placed';

            $emailResult = $this->Global->send_email(
                $customerEmail,
                null,
                $subject,
                $template,
                $emailData
            );

            if ($emailResult) {
                $this->log('COD Order confirmation email sent successfully for order: ' . $order->order_number, 'info');
                return true;
            } else {
                $this->log('Failed to send COD order confirmation email for order: ' . $order->order_number, 'error');
                return false;
            }

        } catch (\Exception $e) {
            $this->log('Error sending COD order confirmation email: ' . $e->getMessage(), 'error');
            return false;
        }
    }

    /**
     * Format currency amount
     */
    private function formatCurrency($amount)
    {
        //$country = $this->getView()->getRequest()->getSession()->read('siteSettings.country') ?? 'Qatar';
        $country = $this->getRequest()->getSession()->read('siteSettings.country') ?? 'Qatar';
        $currency = ($country == 'Qatar') ? 'QAR' : 'SAR';
        return number_format((float)$amount, 2) . " " . $currency;
    }

    /**
     * Add a product review (for product page reviews)
     */
    public function addReview()
    {
        $this->request->allowMethod(['post']);

        $session = $this->request->getSession();
        $account = $session->read('Auth.User');

        if (!$account) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'success' => false,
                'message' => __('Please login to submit a review.')
            ]));
        }

        // Get user with customer relationship to get customer ID
        $usersTable = $this->fetchTable('Users');
        $user = $usersTable->find()
            ->contain(['Customers'])
            ->where(['Users.status' => 'A'])
            ->where(['Users.id' => $account->id])
            ->first();

        if (!$user || !$user->customer) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'success' => false,
                'message' => __('Customer information not found.')
            ]));
        }

        $customerId = $user->customer->id;
        $productId = $this->request->getData('product_id');
        $rating = (int)$this->request->getData('rating');
        $reviewText = trim($this->request->getData('review') ?? '');

        // Validation
        if (!$productId || $rating < 1 || $rating > 5) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'success' => false,
                'message' => __('Invalid review data provided.')
            ]));
        }

        // Load OrderItemReviews table
        $orderItemReviewsTable = $this->fetchTable('OrderItemReviews');

        // Check if user has already reviewed this product
        $existingReview = $orderItemReviewsTable->find()
            ->where([
                'product_id' => $productId,
                'customer_id' => $customerId
            ])
            ->first();

        if ($existingReview) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'success' => false,
                'message' => __('You have already reviewed this product.')
            ]));
        }

        // Create new review
        $review = $orderItemReviewsTable->newEmptyEntity();
        $review->customer_id = $customerId;
        $review->product_id = $productId;
        $review->rating = $rating;
        $review->review = $reviewText;
        $review->status = 'pending'; // Set to pending for admin approval
        $review->order_id = null; // No order for product page reviews
        $review->order_item_id = null; // No order item for product page reviews

        if ($orderItemReviewsTable->save($review)) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'success' => true,
                'message' => __('Thank you for your review! It will be published after approval.')
            ]));
        } else {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'success' => false,
                'message' => __('Failed to submit review. Please try again.')
            ]));
        }
    }

}