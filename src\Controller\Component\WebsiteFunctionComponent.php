<?php
declare(strict_types=1);

namespace App\Controller\Component;

use Cake\Controller\Component;
use Cake\Controller\ComponentRegistry;
use Cake\ORM\TableRegistry;

/**
 * WebsiteFunction component
 */
class WebsiteFunctionComponent extends Component
{
    protected $Categories, $Products, $ProductImages, $Reviews, $Wishlists, $Users, $ContentPages, $SiteSettings;
    protected $request;
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $controller = $this->getController();
        if ($controller) {
            $this->request = $controller->getRequest();
        }

        $this->Categories = TableRegistry::getTableLocator()->get('Categories');
        $this->Products = TableRegistry::getTableLocator()->get('Products');
        $this->ProductImages = TableRegistry::getTableLocator()->get('ProductImages');
        $this->Reviews = TableRegistry::getTableLocator()->get('Reviews');
        $this->Wishlists = TableRegistry::getTableLocator()->get('Wishlists');
        $this->Users = TableRegistry::getTableLocator()->get('Users');
        $this->ContentPages = TableRegistry::getTableLocator()->get('ContentPages');
        $this->SiteSettings = TableRegistry::getTableLocator()->get('SiteSettings');
    }

    /**
     * Default configuration.
     *
     * @var array<string, mixed>
     */
    protected array $_defaultConfig = [];

    public function getProductDiscount($id)
    {
        return $this->Products->getDiscount($id);
    }

    public function getCategories()
    {
        return $this->Categories->parentCategories();
    }

    
    public function setSiteSettings($country = 'qatar', $lang = 'eng')
    {
        if (strtolower($country) == 'qatar') {
            $country_id = 1;
        } else {
            $country_id = 2;
        }

        if ($this->request) {
            $session = $this->request->getSession();
        } else {
            $session = $this->getController()->getRequest()->getSession();
        }
     

        $isArabic = (strtolower($lang) != 'eng');
        $siteSettings = $this->SiteSettings->find()->select([
            'country_id',
            'site_title',
            'site_title_ar',
            'address_line1',
            'address_line1_ar',
            'address_line2',
            'address_line2_ar',
            'country',
            'country_ar',
            'state',
            'state_ar',
            'city',
            'city_ar',
            'zipcode',
            'customer_support_no',
            'contact_no',
            'support_email',
            'business_open_time',
            'business_close_time',
            'facebook_url',
            'twitter_url',
            'instagram_url',
            'linkedin_url',
            'product_cancel_in_days',
            'product_return_in_days'
        ])->where(['country_id' => $country_id])->all();

        // If no settings found for the given country, fallback to default 'Qatar'
        if ($siteSettings->isEmpty()) {
            $country = 'Qatar';
            $siteSettings = $this->SiteSettings->find()->select([
                'country_id',
                'site_title',
                'site_title_ar',
                'address_line1',
                'address_line1_ar',
                'address_line2',
                'address_line2_ar',
                'country',
                'country_ar',
                'state',
                'state_ar',
                'city',
                'city_ar',
                'zipcode',
                'customer_support_no',
                'contact_no',
                'support_email',
                'business_open_time',
                'business_close_time',
                'facebook_url',
                'twitter_url',
                'instagram_url',
                'linkedin_url',
                'product_cancel_in_days',
                'product_return_in_days'
            ])->where(['country_id' => $country_id])->all();
        }

        foreach ($siteSettings as $setting) {
            $site_title = $setting->site_title;
            $address_line1 = $setting->address_line1;
            $address_line2 = $setting->address_line2;
            $country_val = $setting->country;
            $state_val = $setting->state;
            $city_val = $setting->city;
            if ($isArabic) {
                if (!empty($setting->site_title_ar)) {
                    $site_title = $setting->site_title_ar;
                }
                if (!empty($setting->address_line1_ar)) {
                    $address_line1 = $setting->address_line1_ar;
                }
                if (!empty($setting->address_line2_ar)) {
                    $address_line2 = $setting->address_line2_ar;
                }
                if (!empty($setting->country_ar)) {
                    $country_val = $setting->country_ar;
                }
                if (!empty($setting->state_ar)) {
                    $state_val = $setting->state_ar;
                }
                if (!empty($setting->city_ar)) {
                    $city_val = $setting->city_ar;
                }
            }
            $settingsData = [
                'country_id' => $setting->country_id,
                'site_title' => $site_title,
                'address_line1' => $address_line1,
                'address_line2' => $address_line2,
                'country' => $country_val,
                'state' => $state_val,
                'city' => $city_val,
                'zipcode' => $setting->zipcode,
                'customer_support_no' => $setting->customer_support_no,
                'contact_no' => $setting->contact_no,
                'support_email' => $setting->support_email,
                'business_open_time' => $setting->business_open_time,
                'business_close_time' => $setting->business_close_time,
                'facebook_url' => $setting->facebook_url,
                'twitter_url' => $setting->twitter_url,
                'instagram_url' => $setting->instagram_url,
                'linkedin_url' => $setting->linkedin_url,
                'product_cancel_in_days' => $setting->product_cancel_in_days ?? 0,
                'product_return_in_days' => $setting->product_return_in_days ?? 0
            ];
            foreach ($settingsData as $key => $value) {
                $session->write("siteSettings.$key", $value);
            }
        }

        $languageSettings = [
            'country' => $country,
            'language' => strtolower($lang) === 'eng' ? 'English' : 'Arabic',
            'dir' => strtolower($lang) === 'eng' ? 'ltr' : 'rtl',
            'bodylg' => strtolower($lang) === 'eng' ? 'english' : 'arabic',
            'activecls1' => strtolower($lang) === 'eng' ? '' : 'active',
            'activecls2' => strtolower($lang) === 'eng' ? 'active' : '',
        ];

        foreach ($languageSettings as $key => $value) {
            $session->write("siteSettings.$key", $value);
        }
        
        return $session->read("siteSettings");
     
    }

    public function getSeoData($type = 'default', $data = null)
    {
        $seo = [];
        if ($this->request) {
            $session = $this->request->getSession();
        } else {
            $session = $this->getController()->getRequest()->getSession();
        }
        $siteSettings = TableRegistry::getTableLocator()->get('SiteSettings')->find()->first();
        if ($type === 'product' && $data) {
            // Handle both array and object data
            if (is_array($data)) {
                $seo = [
                    'title' => $data['meta_title'] ?? ($data['name'] ?? ''),
                    'meta_title' => $data['meta_title'] ?? ($data['name'] ?? ''),
                    'description' => $data['meta_description'] ?? '',
                    'keywords' => $data['meta_keywords'] ?? '',
                    'robots' => 'index, follow',
                    'og_title' => $data['meta_title'] ?? ($data['name'] ?? ''),
                    'og_description' => $data['meta_description'] ?? '',
                    'google_analytics_script' => $siteSettings->google_analytics_script ?? '',
            ];
            } else {
                // Handle object data
                $seo = [
                    'title' => $data->meta_title ?? $data->name,
                    'meta_title' => $data->meta_title ?? $data->name,
                    'description' => $data->meta_description ?? substr(strip_tags($data->description ?? ''), 0, 160),
                    'keywords' => $data->meta_keywords ?? '',
                    'robots' => 'index, follow',
                    'og_title' => $data->meta_title ?? $data->name,
                    'og_description' => $data->meta_description ?? substr(strip_tags($data->description ?? ''), 0, 160),
                    'google_analytics_script' => $siteSettings->google_analytics_script ?? '',
                ];
            }
        } else {
            // fallback from settings
            $seo = [
                'title' => $session->read('siteSettings.site_title'),
                'meta_title' => $siteSettings->meta_title ?? 'OzoneX',
                'description' => $siteSettings->meta_description ?? '',
                'keywords' => $siteSettings->meta_keywords ?? '',
                'robots' => $siteSettings->robots_meta_tag ?? 'index, follow',
                'og_title' => $siteSettings->og_title ?? ($siteSettings->meta_title ?? 'OzoneX'),
                'og_description' => $siteSettings->og_description ?? ($siteSettings->meta_description ?? ''),
                'google_analytics_script' => $siteSettings->google_analytics_script ?? '',
            ];
        }

        // 🔁 Pass to view
        $this->_registry->getController()->set('seo', $seo);
    }
}
