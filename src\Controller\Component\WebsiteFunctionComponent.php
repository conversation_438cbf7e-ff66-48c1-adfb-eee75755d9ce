<?php
declare(strict_types=1);

namespace App\Controller\Component;

use Cake\Controller\Component;
use Cake\Controller\ComponentRegistry;
use Cake\ORM\TableRegistry;

/**
 * WebsiteFunction component
 */
class WebsiteFunctionComponent extends Component
{
    protected $Categories, $Products, $ProductImages, $Reviews, $Wishlists, $Users, $ContentPages, $SiteSettings;
    protected $request;
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $controller = $this->getController();
        if ($controller) {
            $this->request = $controller->getRequest();
        }

        $this->Categories = TableRegistry::getTableLocator()->get('Categories');
        $this->Products = TableRegistry::getTableLocator()->get('Products');
        $this->ProductImages = TableRegistry::getTableLocator()->get('ProductImages');
        $this->Reviews = TableRegistry::getTableLocator()->get('Reviews');
        $this->Wishlists = TableRegistry::getTableLocator()->get('Wishlists');
        $this->Users = TableRegistry::getTableLocator()->get('Users');
        $this->ContentPages = TableRegistry::getTableLocator()->get('ContentPages');
        $this->SiteSettings = TableRegistry::getTableLocator()->get('SiteSettings');
    }

    /**
     * Default configuration.
     *
     * @var array<string, mixed>
     */
    protected array $_defaultConfig = [];

    public function getProductDiscount($id)
    {
        return $this->Products->getDiscount($id);
    }

    public function getCategories()
    {
        return $this->Categories->parentCategories();
    }

    
    public function setSiteSettings($country = 'Qatar', $lang = 'eng')
    {
        if ($this->request) {
            $session = $this->request->getSession();
        } else {
            $session = $this->getController()->getRequest()->getSession();
        }
        $siteSettings = $this->SiteSettings->find()->select([
            'country_id',
            'site_title',
            'address_line1',
            'address_line2',
            'country',
            'state',
            'city',
            'zipcode',
            'customer_support_no',
            'contact_no',
            'support_email',
            'business_open_time',
            'business_close_time',
            'facebook_url',
            'twitter_url',
            'instagram_url',
            'linkedin_url',
             'product_cancel_in_days',
            'product_return_in_days'
        ])->where(['country' => $country])->all();

        // If no settings found for the given country, fallback to default 'Qatar'
        if ($siteSettings->isEmpty()) {
            $country = 'Qatar';
            $siteSettings = $this->SiteSettings->find()->select([
                'country_id',
                'site_title',
                'address_line1',
                'address_line2',
                'country',
                'state',
                'city',
                'zipcode',
                'customer_support_no',
                'contact_no',
                'support_email',
                'business_open_time',
                'business_close_time',
                'facebook_url',
                'twitter_url',
                'instagram_url',
                'linkedin_url',
                'product_cancel_in_days',
                'product_return_in_days'
            ])->where(['country' => $country])->all();
        }

        foreach ($siteSettings as $setting) {
            $settingsData = [
                'country_id' => $setting->country_id,
                'site_title' => $setting->site_title,
                'address_line1' => $setting->address_line1,
                'address_line2' => $setting->address_line2,
                'country' => $setting->country,
                'state' => $setting->state,
                'city' => $setting->city,
                'zipcode' => $setting->zipcode,
                'customer_support_no' => $setting->customer_support_no,
                'contact_no' => $setting->contact_no,
                'support_email' => $setting->support_email,
                'business_open_time' => $setting->business_open_time,
                'business_close_time' => $setting->business_close_time,
                'facebook_url' => $setting->facebook_url,
                'twitter_url' => $setting->twitter_url,
                'instagram_url' => $setting->instagram_url,
                'linkedin_url' => $setting->linkedin_url,
                'product_cancel_in_days' => $setting->product_cancel_in_days ?? 0,
                'product_return_in_days' => $setting->product_return_in_days ?? 0
            ];

            foreach ($settingsData as $key => $value) {
                $session->write("siteSettings.$key", $value);
            }
        }

        $languageSettings = [
            'country' => $country,
            'language' => strtolower($lang) === 'eng' ? 'English' : 'Arabic',
            'dir' => strtolower($lang) === 'eng' ? 'ltr' : 'rtl',
            'bodylg' => strtolower($lang) === 'eng' ? 'english' : 'arabic',
            'activecls1' => strtolower($lang) === 'eng' ? '' : 'active',
            'activecls2' => strtolower($lang) === 'eng' ? 'active' : '',
        ];

        foreach ($languageSettings as $key => $value) {
            $session->write("siteSettings.$key", $value);
        }
        
        return $session->read("siteSettings");
     
    }

}
