<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * OrderReturns Model
 *
 * @property \App\Model\Table\OrderItemsTable&\Cake\ORM\Association\BelongsTo $OrderItems
 * @property \App\Model\Table\OrderReturnCategoriesTable&\Cake\ORM\Association\BelongsTo $OrderReturnCategories
 *
 * @method \App\Model\Entity\OrderReturn newEmptyEntity()
 * @method \App\Model\Entity\OrderReturn newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\OrderReturn> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\OrderReturn get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\OrderReturn findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\OrderReturn patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\OrderReturn> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\OrderReturn|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\OrderReturn saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\OrderReturn>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\OrderReturn>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\OrderReturn>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\OrderReturn> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\OrderReturn>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\OrderReturn>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\OrderReturn>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\OrderReturn> deleteManyOrFail(iterable $entities, array $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class OrderReturnsTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('order_returns');
        $this->setDisplayField('reason');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');

        $this->belongsTo('OrderItems', [
            'foreignKey' => 'order_item_id',
            'joinType' => 'INNER',
        ]);
        $this->belongsTo('Orders', [
            'foreignKey' => 'order_id',
            'joinType' => 'INNER',
        ]);
        $this->belongsTo('Customers', [
            'foreignKey' => 'customers',
            'joinType' => 'INNER',
        ]);
        $this->belongsTo('OrderReturnCategories', [
            'foreignKey' => 'order_return_category_id',
            'joinType' => 'INNER',
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        // $validator
        //     ->nonNegativeInteger('order_item_id')
        //     ->notEmptyString('order_item_id');

        // $validator
        //     ->nonNegativeInteger('order_return_category_id')
        //     ->notEmptyString('order_return_category_id');

        // $validator
        //     ->scalar('reason')
        //     ->maxLength('reason', 255);

        $validator
            ->scalar('status')
            ->notEmptyString('status');

        $validator
            ->dateTime('requested_at')
            ->requirePresence('requested_at', 'create')
            ->notEmptyDateTime('requested_at');

        $validator
            ->dateTime('processed_at')
            ->allowEmptyDateTime('processed_at');

        $validator
            ->decimal('return_amount');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        // $rules->add($rules->existsIn(['order_item_id'], 'OrderItems'), ['errorField' => 'order_item_id']);
        // $rules->add($rules->existsIn(['order_return_category_id'], 'OrderReturnCategories'), ['errorField' => 'order_return_category_id']);

        return $rules;
    }

    //M
    public function add_record($attributes) {

        $new = $this->newEmptyEntity();
         foreach($attributes as $key => $value) {
             $new->$key = $value;
         }
 
         if($this->save($new)) {
             return $new->id;
         } else {
             return false;
         }
    }

    //M
    public function update_record($id, $attributes) {
        $old_attr = $this->get($id);
        $old_attr = $this->patchEntity($old_attr, $attributes);
        if ($this->save($old_attr)) {
            return $old_attr;
        } else {
            return false;
        }
    }

    //S
    public function getOrderReturns($showroomId, $limit)
    {
        $returnOrder = $this->find()
            ->select(['id', 'status', 'reason','requested_at']) // Select spreasonecific fields from Transactions
            ->contain([ 
                'OrderReturnCategories' => function ($q3) {
                    return $q3
                    ->select(['OrderReturnCategories.name']);
                    },           
                'Orders' => function ($q) use ($showroomId) {
                    return $q
                        ->select(['Orders.id', 'Orders.order_number', 'Orders.customer_id']) // Select specific fields from Orders
                        ->where(['Orders.showroom_id' => $showroomId])
                        ->contain([
                        'Customers' => function ($q2) {
                            return $q2
                            ->select(['Customers.id']) 
                            ->contain([
                                'Users' => function ($q1) {
                                    return $q1->select([
                                        'Users.id', 
                                        'Users.first_name', 
                                        'Users.last_name', 
                                        'Users.email', 
                                        'Users.mobile_no'
                                    ]); // Select specific fields from Users
                                }
                            ]);
                        }
                    ]);
                }
            ])
            ->order(['OrderReturns.created' => 'DESC'])
            ->limit($limit)
            ->all();

        return $returnOrder;
    }
    
}
