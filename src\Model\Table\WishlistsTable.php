<?php
declare(strict_types=1);

namespace App\Model\Table;
use Cake\ORM\TableRegistry;
use Cake\ORM\Query;
use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;
use Cake\Controller\ComponentRegistry;
use Cake\Http\ServerRequestFactory;
/**
 * Wishlists Model
 *
 * @property \App\Model\Table\CustomersTable&\Cake\ORM\Association\BelongsTo $Customers
 * @property \App\Model\Table\ProductsTable&\Cake\ORM\Association\BelongsTo $Products
 *
 * @method \App\Model\Entity\Wishlist newEmptyEntity()
 * @method \App\Model\Entity\Wishlist newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\Wishlist> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\Wishlist get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\Wishlist findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\Wishlist patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\Wishlist> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\Wishlist|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\Wishlist saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\Wishlist>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Wishlist>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Wishlist>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Wishlist> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Wishlist>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Wishlist>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Wishlist>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Wishlist> deleteManyOrFail(iterable $entities, array $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class WishlistsTable extends Table
{
     protected $language;
    protected $country;
    protected $country_id;
   
    public function initialize(array $config): void
    {
        parent::initialize($config);
         $request = ServerRequestFactory::fromGlobals();
        $this->language = $request->getSession()->read('siteSettings.language') ?? 'English';
        $this->country = $request->getSession()->read('siteSettings.country') ?? 'Qatar';
        $this->country_id = $request->getSession()->read('siteSettings.country_id') ?? 1;

        $this->setTable('wishlists');
        $this->setDisplayField('id');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');

        $this->belongsTo('Customers', [
            'foreignKey' => 'customer_id',
            'joinType' => 'LEFT', // Changed to LEFT to allow guest wishlists
        ]);
        $this->belongsTo('Users', [
            'foreignKey' => 'user_id', // Adjust as per your schema
            'joinType' => 'INNER',     // Use INNER or LEFT based on your logic
        ]);
        $this->belongsTo('Products', [
            'foreignKey' => 'product_id',
            'joinType' => 'INNER',
        ]);
    }

   
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->nonNegativeInteger('customer_id')
            ->allowEmptyString('customer_id'); // Allow empty for guest users

        $validator
            ->scalar('guest_token')
            ->allowEmptyString('guest_token'); // Allow empty for logged-in users

        $validator
            ->nonNegativeInteger('product_id')
            ->notEmptyString('product_id');

        return $validator;
    }
    
     
     

    public function buildRules(RulesChecker $rules): RulesChecker
    {
        // Only check customer existence if customer_id is provided (not for guest users)
        $rules->add(
            function ($entity, $options) {
                if (!empty($entity->customer_id)) {
                    return $this->Customers->exists(['id' => $entity->customer_id]);
                }
                return true; // Allow guest wishlists without customer_id
            },
            ['errorField' => 'customer_id', 'message' => 'Customer does not exist']
        );

        $rules->add($rules->existsIn(['product_id'], 'Products'), ['errorField' => 'product_id']);

        return $rules;
    }
   
    // new method
    public function getWishlistItems($customerId = null, $guestToken = null)
    {
        $isArabic = ($this->language === 'ar' || $this->language === 'Arabic' || strtolower($this->language) === 'arabic');
     

        $conditions = [];
        if ($customerId) {
            $conditions['customer_id'] = $customerId;
        } elseif ($guestToken) {
            $conditions['guest_token'] = $guestToken;
        } else {
            return [];
        }

        $wishlistItems = $this->find()
            ->where($conditions)
            ->contain([
                'Products' => function ($q) {
                    return $q->select([
                        'Products.id',
                        'Products.name',
                        'Products.name_ar',
                        'Products.description',
                        'Products.description_ar',
                        'Products.product_price',
                        'Products.sales_price',
                        'Products.promotion_price',
                        'Products.purchase_price',
                        'Products.status'
                    ]);
                }
            ])
            ->order(['Wishlists.created' => 'DESC'])
            ->all();

        // Load related tables/components
        $productImagesTable = TableRegistry::getTableLocator()->get('ProductImages');
        $registry = new ComponentRegistry();
        $mediaComponent = new \App\Controller\Component\MediaComponent($registry);

        $processedItems = [];

        foreach ($wishlistItems as $item) {
            $product = $item->product;

            if (!$product || $product->status !== 'A') {
                continue;
            }

            // Product name localization
            $product->product_name = $isArabic ? $product->name_ar : $product->name;
            $product->product_description= $isArabic ? $product->description_ar : $product->description;

            // Product image
            $image = $productImagesTable->getDefaultProductImage($product->id);
            $product->product_image = $image
                ? $mediaComponent->getCloudFrontURL($image)
                : '/img/no-img.jpg'; // fallback image

            $processedItems[] = $item;
        }
     
        return $processedItems;
    }



    public function addToWishlist($customer_id, $product_id, $guest_token = null)
    {
        
        // Build conditions based on whether it's a customer or guest
        $conditions = ['product_id' => $product_id];
        if ($customer_id) {
            $conditions['customer_id'] = $customer_id;
        } else {
            $conditions['guest_token'] = $guest_token;
        }

        // Check if the entry already exists to prevent duplicates
        $existingEntry = $this->find()->where($conditions)->first();

        if ($existingEntry) {
            return [
                'status' => 400,
                'message' => __('This item is already in your wishlist.'),
            ];
        }

        // Create a new wishlist entry
        $wishlistData = ['product_id' => $product_id];
        if ($customer_id) {
            $wishlistData['customer_id'] = $customer_id;
        } else {
            $wishlistData['guest_token'] = $guest_token;
        }

        $wishlistEntry = $this->newEntity($wishlistData);

        // Save the wishlist entry and return the result
        if ($this->save($wishlistEntry)) {
            return [
                'status' => 200,
                'message' => __('Item successfully added to your wishlist.'),
                'data' => $wishlistEntry,
            ];
        }

        // Handle save failure
        return [
            'status' => 400,
            'message' => __('Unable to add the item to your wishlist. Please try again.'),
        ];
    }

    public function viewWishlist($customer_id, $guest_token = null)
    {
        $conditions = [];
        if ($customer_id) {
            $conditions['customer_id'] = $customer_id;
        } else {
            $conditions['guest_token'] = $guest_token;
        }

        return $this->find()
            ->where($conditions)
            ->contain(['Products'])
            ->toArray();
    }

    public function removeFromWishlist_old($customer_id, $product_id)
    {
        $entry = $this->find()
                    ->where(['product_id' => $product_id, 'customer_id' => $customer_id])
                    ->first();
        if ($entry) {
            return $this->delete($entry);
        }

        return false;
    }
    public function removeFromWishlist($customerId, $productId, $guestToken)
{
    $conditions = ['product_id' => $productId];

    if ($customerId !== null) {
        $conditions['customer_id'] = $customerId;
    } elseif ($guestToken !== null) {
        $conditions['guest_token'] = $guestToken;
    } else {
        // No valid identifier
        return false;
    }

    $wishlistItem = $this->find()
        ->where($conditions)
        ->first();

    if ($wishlistItem) {
        return $this->delete($wishlistItem);
    }

    return false;
}


    public function whishListCheckSingle($userId, $productId)
    {
        // Fetch the user with the associated customer ID
        $user = $this->Users->find()
            ->contain([
                'Customers' => function ($q) {
                    return $q->select(['id']); // Select only the Customer.id field
                }
            ])
            ->select(['Users.id']) // Select the necessary fields from Users
            ->where(['Users.status' => 'A', 'Users.id' => $userId]) // Combine conditions in one call
            ->first();

        // Ensure the user and associated customer exist
        if (!$user || empty($user->customer)) {
            return false; // Return false if no valid user or customer is found
        }

        // Check if the product exists in the wishlist for the customer
        $entry = $this->find()
            ->where(['customer_id' => $user->customer->id, 'product_id' => $productId])
            ->first();

        return (bool)$entry; // Return true if entry exists, otherwise false
    }

    /**
     * Check if a product is in guest wishlist
     *
     * @param string $guestToken
     * @param int $productId
     * @return bool
     */
    public function wishListCheckGuest($guestToken, $productId)
    {
        if (empty($guestToken) || empty($productId)) {
            return false;
        }

        $entry = $this->find()
            ->where(['guest_token' => $guestToken, 'product_id' => $productId])
            ->first();

        return !empty($entry);
    }

    /**
     * Check if a product is in customer wishlist by customer ID
     *
     * @param int $customerId
     * @param int $productId
     * @return bool
     */
    public function wishListCheckCustomer($customerId, $productId)
    {
        if (empty($customerId) || empty($productId)) {
            return false;
        }

        $entry = $this->find()
            ->where(['customer_id' => $customerId, 'product_id' => $productId])
            ->first();

        return !empty($entry);
    }

    /**
     * Check if a product is in wishlist (for both customer and guest)
     *
     * @param int|null $customerId
     * @param string|null $guestToken
     * @param int $productId
     * @return bool
     */
    public function isInWishlist($customerId, $guestToken, $productId)
    {
        if ($customerId) {
            return $this->wishListCheckCustomer($customerId, $productId);
        } elseif ($guestToken) {
            return $this->wishListCheckGuest($guestToken, $productId);
        }

        return false;
    }
}
