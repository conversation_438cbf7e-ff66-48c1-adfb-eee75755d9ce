<!-- Success Modal Popup -->
<?php /*
<div class="modal fade show" id="successModal" tabindex="-1" aria-labelledby="successModalLabel" aria-hidden="false" style="display: block; background: rgba(0,0,0,0.8);">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content success-modal-content">
            <div class="modal-header border-0 text-center position-relative">
                <button type="button" class="btn-close position-absolute top-0 end-0 m-3" data-bs-dismiss="modal" aria-label="Close" onclick="closeSuccessModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body text-center py-5 position-relative">
                <!-- Modal Confetti Container -->
                <div class="modal-confetti-container">
                    <div class="modal-confetti-piece"></div>
                    <div class="modal-confetti-piece"></div>
                    <div class="modal-confetti-piece"></div>
                    <div class="modal-confetti-piece"></div>
                    <div class="modal-confetti-piece"></div>
                    <div class="modal-confetti-piece"></div>
                    <div class="modal-confetti-piece"></div>
                    <div class="modal-confetti-piece"></div>
                    <div class="modal-confetti-piece"></div>
                    <div class="modal-confetti-piece"></div>
                </div>

                <!-- Animated Success Icon -->
                <div class="success-icon-container mb-4">
                    <div class="success-checkmark">
                        <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                            <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none"/>
                            <path class="checkmark__check" fill="none" d="m14.1 27.2l7.1 7.2 16.7-16.8"/>
                        </svg>
                    </div>
                </div>

                <!-- Success Message -->
                <div class="success-message-container">
                    <h2 class="success-title mb-3">🎉<?= __('Order Placed Successfully!  ') ?> 🎉</h2>
                    <p class="success-subtitle mb-4"> <?= __('Thank you for your purchase! Your order has been confirmed and is being processed.') ?></p>

                    <?php if ($order): ?>
                        <div class="order-info-card">
                            <h5 class="mb-3"><?= __('Order Details') ?></h5>
                            <p><strong><?= __('Order ID:') ?></strong> #<?= h($order->order_number) ?></p>
                            <p><strong><?= __('Total Amount:') ?></strong>
                             <!-- <?= number_format($order->total_amount, 2) ?> QAR -->
                              <?= $this->Price->setPriceFormat($order->total_amount )?>
                            </p>
                            <?php if (!empty($appliedCoupon)): ?>
                                <div class="coupon-success-info mt-3 p-3 bg-success-light rounded">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-ticket-alt text-success me-2"></i>
                                        <div>
                                            <strong class="text-success"><?= __('Coupon Applied:') ?> <?= h($appliedCoupon['code']) ?></strong>
                                            <br>
                                            <small class="text-muted">
                                                You saved <?= $this->Price->setPriceFormat($appliedCoupon['discount_amount']) ?>
                                                <?php if ($appliedCoupon['coupon_type'] === 'percentage'): ?>
                                                    with this discount coupon!
                                                <?php else: ?>
                                                    with this fixed discount coupon!
                                                <?php endif; ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                            <p><strong><?= __('Estimated Delivery:') ?></strong> <?= date('d M, Y', strtotime($estimated_days)) ?> </p>
                        </div>
                    <?php endif; ?>

                    <div class="action-buttons mt-4">
                        <button class="btn btn-primary btn-lg me-3" onclick="closeSuccessModal()"><?= __('Continue Shopping') ?></button>
                        <a href="<?= $this->Url->build(['controller' => 'home', 'action' => 'home']) ?>" class="btn btn-outline-primary btn-lg"><?= __('Go to Homepage') ?></a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
*/ ?>

  <section class="cart-tab-head my-5">
        <div class="container">
            <ul class="nav nav-tabs" id="purchase-tab" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link " id="home-tab" data-bs-toggle="tab" data-bs-target="#home-tab-pane"
                        type="button" role="tab" aria-controls="home-tab-pane" aria-selected="true">
                        <div class="d-flex align-items-center "><div class="cart-img"><img src="../../img/ozone/cart.png" class="img-fluid" /></div>
                             <?= __(' Cart') ?> </div>
                    </button>
                    <!-- <hr> -->
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="profile-tab" data-bs-toggle="tab" data-bs-target="#profile-tab-pane"
                        type="button" role="tab" aria-controls="profile-tab-pane" aria-selected="false">
                        <div class="d-flex align-items-center "><div class="cart-img"><img src="../../img/ozone/cart.png" class="img-fluid" /></div>
                                <?= __('Checkout') ?> </div>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="contact-tab" data-bs-toggle="tab" data-bs-target="#contact-tab-pane"
                        type="button" role="tab" aria-controls="contact-tab-pane" aria-selected="false">
                        <div class="d-flex align-items-center "><div class="cart-img"><img src="../../img/ozone/cart.png" class="img-fluid" /></div>
                                <?= __('Order Complete') ?> </div>
                    </button>
                </li>
            </ul>
        </div>
    </section>
 <section>
        <div class="container">
            <div class="tab-content" id="myTabContent">
                <div class="tab-pane mb-5 fade show active" id="home-tab-pane" role="tabpanel"
                    aria-labelledby="home-tab" tabindex="0">
                    <div class="cart-section">
                        <div class="container py-5">
                            <div class="row g-4">

                                <!-- Order Items -->
                                <div class="col-lg-8">
                                    <?php if ($order && $order->order_items):
                                        ?>
                                        <?php foreach ($order->order_items as $item): ?>
                                            <div class="cart-card d-flex flex-column flex-md-row align-items-start mb-4">
                                                    <a href="/product/<?= h($item->product_id) ?>" class="wishlist-card-link">
                                                     <img src="<?=($item->product_image ?? 'Product') ?>" alt="Product"
                                                    class="img-fluid rounded-4 mb-3 mb-md-0 me-md-4" />
                                                    </a>

                                                <div class="purchase-cart">
                                                    <h5 class="fw-bold"><?= h($item->product_name ?? 'Product') ?></h5>
                                                    <p class="mb-1"> <?= __('Quantity:') ?> <?= h($item->quantity) ?></p>
                                                     <h4 class="text-success fw-bold mb-3">
                                                        <!-- <?= number_format($item->price, 2) ?> QAR</h4> -->
                                                         <?= $this->Price->setPriceFormat($item->price )?> </h4>

                                                    <h5 class="">
                                                        <!-- <?= number_format($item->total_price, 2) ?> QAR</h4> -->
                                                        <?= __('Total') ?> - <?= $this->Price->setPriceFormat($item->total_price )?> </h5>

                                                    <div class="bg-light rounded-3 p-3 mb-3">
                                                        <p class="fw-bold mb-1"><?= __('Delivery and Returns') ?></p>
                                                        <small class="text-muted"><?= __('Standard delivery 4–9 business days') ?><br><?= __('Orders
                                                            are processed and delivered Monday–Friday (excluding public holidays)') ?>
                                                           </small>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <div class="cart-card d-flex flex-column flex-md-row align-items-start">
                                            <img src="../../img/ozone/cart-purshace.png" alt="Product"
                                                class="img-fluid rounded-4 mb-3 mb-md-0 me-md-4" />

                                            <div class="purchase-cart">
                                                <h5 class="fw-bold"><?= __('Your Order') ?></h5>
                                                <p class="mb-1"><?= __('Order details will be available shortly') ?></p>
                                                <h4 class="text-success fw-bold mb-3"><?= __('Processing...') ?></h4>

                                                <div class="bg-light rounded-3 p-3 mb-3">
                                                    <p class="fw-bold mb-1"><?= __('Delivery and Returns') ?></p>
                                                    <small class="text-muted"><?= __('Standard delivery 4–9 business days<br>Orders
                                                        are processed and delivered Monday–Friday (excluding public holidays)') ?></small>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <!-- Order Summary -->
                                <div class="col-lg-4">
                                    <div class="summary-card purchase-success">
                                        <h5 class="fw-bold title pt-3 pb-4"> <?= __('Order Summary')?></h5>

                                        <?php if ($order): ?>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span class="describtion"><?= __('Order Number')?> </span>
                                                <span class="text-success fw-bold"><?= h($order->order_number) ?></span>
                                            </div>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span class="describtion"><?= __(' Payment Method')?></span>
                                                <span class="text-success"><?= h($order->payment_method) ?></span>
                                            </div>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span class="describtion"><?= __('Order Status')?></span>
                                                <span class="text-success"><?= h($order->status) ?></span>
                                            </div>
                                        <?php endif; ?>

                                        <hr>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span class="describtion"><?= __('Subtotal')?></span>
                                            <span class="text-success">
                                                <!-- <?= $order ? number_format($order->subtotal_amount, 2) : '0.00' ?> QAR -->
                                                 <?= $order ? $this->Price->setPriceFormat($order->subtotal_amount ) : '0.00' ?>
                                            </span>
                                        </div>
                                        <?php if ($order && $order->discount_amount >= 0): ?>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span class="describtion"><?= __('Discount')?> </span>
                                                <span class="text-success">-
                                                    <!-- <?= number_format($order->discount_amount, 2) ?> QAR -->
                                                   <?= $this->Price->setPriceFormat($order->discount_amount) ?>
                                                </span>
                                            </div>
                                        <?php endif; ?>
                                         <?php if ($order && $order->tax_amount >= 0): ?>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span class="describtion"><?= __('Tax Amount')?> </span>
                                                <span class="text-success">
                                                    <!-- <?= number_format($order->tax_amount, 2) ?> QAR -->
                                                   <?= $this->Price->setPriceFormat($order->tax_amount) ?>
                                                </span>
                                            </div>
                                        <?php endif; ?>
                                        <!-- <div class="d-flex justify-content-between mb-2">
                                            <span class="describtion">Region</span>
                                            <span class="text-success">Qatar</span>
                                        </div> -->
                                        <!-- <div class="d-flex justify-content-between mb-4">
                                            <span class="describtion">Shipping Costs</span>
                                            <span class="text-success">
                                                <?= $order ? $this->Price->setPriceFormat($order->delivery_charge)  : '0.00' ?>
                                            </span>
                                        </div> -->
                                        <div class="d-flex justify-content-between mb-4">
                                            <span class="describtion"><?= __('Delivery Charge')?> </span>
                                            <span class="text-success">
                                                <?= $order ? $this->Price->setPriceFormat($order->delivery_charge)  : '0.00' ?>
                                            </span>
                                        </div>

                                        <?php if ($order && !empty($order->installation_amount) && $order->installation_amount > 0): ?>
                                        <div class="d-flex justify-content-between mb-4">
                                            <span class="describtion">  <?= __('Installation Charge')?> </span>
                                            <span class="text-success">
                                                <?= $this->Price->setPriceFormat($order->installation_amount) ?>
                                            </span>
                                        </div>
                                        <?php endif; ?>
                                        <hr>
                                        <div class="d-flex justify-content-between mb-4">
                                            <span class="total-head"><?= __('TOTAL')?></span>
                                            <span class="total-amount">
                                                <!-- <?= $order ? number_format($order->total_amount, 2) : '0.00' ?> QAR -->
                                                <?= $order ? $this->Price->setPriceFormat($order->total_amount)  : '0.00' ?>
                                            </span>
                                        </div>
                                        <hr>
                                        <div class="d-flex justify-content-between mb-4">
                                            <small class="estimated"><?= __('Estimated Delivery by')?></small>
                                            <span class="estimated-date"><?= date('d M, Y', strtotime($estimated_days)) ?></span>
                                        </div>

                                        <?php if (!empty($order) && strtolower($order->payment_status) == 1) { ?>

                                        <div class="order-success-notification mt-4 text-center">
                                            <div class="order-success-icon mb-2">
                                                <svg width="48" height="48" viewBox="0 0 48 48" fill="none">
                                                    <circle cx="24" cy="24" r="22" stroke="#28c76f" stroke-width="4" fill="#e8f5e9"/>
                                                    <path d="M16 24.5L22 30.5L32 18.5" stroke="#28c76f" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
                                                </svg>
                                            </div>
                                            <h4 class="fw-bold text-success mb-1 text-center"><?= __('Order Placed!') ?></h4>
                                            <p class="mb-0 text-muted"><?= __('Your order was placed successfully and is being processed.') ?></p>
                                        </div>
                                        <style>
                                            .order-success-notification {
                                                background: rgba(40, 199, 111, 0.08);
                                                border-radius: 12px;
                                                padding: 1.5rem 1rem;
                                                box-shadow: 0 2px 8px rgba(40,199,111,0.08);
                                                display: inline-block;
                                                width: 100%;
                                            }
                                            .order-success-icon {
                                                display: flex;
                                                justify-content: center;
                                                align-items: center;
                                            }
                                        </style>
                                        <?php } ?>
                                         <?php if (!empty($order) && strtolower($order->payment_status) == 0) { ?>

                                        <div class="order-failed-notification mt-4 text-center">
                                            <div class="order-failed-icon mb-2">
                                                <svg width="48" height="48" viewBox="0 0 48 48" fill="none">
                                                    <circle cx="24" cy="24" r="22" stroke="#ff3d00" stroke-width="4" fill="#ffebee"/>
                                                    <path d="M16 16L32 32M32 16L16 32" stroke="#ff3d00" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
                                                </svg>
                                            </div>
                                            <h4 class="fw-bold text-danger mb-1 text-center"><?= __('Order Failed!') ?></h4>
                                            <p class="mb-0 text-muted"><?= __('Your order could not be processed. Please try again.') ?></p>
                                        </div>
                                        <style>
                                            .order-failed-notification {
                                                background: rgba(255, 61, 0, 0.08);
                                                border-radius: 12px;
                                                padding: 1.5rem 1rem;
                                                box-shadow: 0 2px 8px rgba(255, 61, 0, 0.08);
                                                display: inline-block;
                                                width: 100%;
                                            }
                                            .order-failed-icon {
                                                display: flex;
                                                justify-content: center;
                                                align-items: center;
                                            }
                                        </style>

                                        <?php } ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
<script src="https://cdn.jsdelivr.net/npm/canvas-confetti@1.5.1/dist/confetti.browser.min.js"></script>
<script>
    // Enhanced Confetti Animation
    function createConfetti() {
        // Main confetti burst
        confetti({
            particleCount: 100,
            spread: 70,
            origin: { y: 0.6 },
            colors: ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3', '#54a0ff']
        });

        // Side confetti bursts
        setTimeout(() => {
            confetti({
                particleCount: 50,
                angle: 60,
                spread: 55,
                origin: { x: 0 },
                colors: ['#ff6b6b', '#4ecdc4', '#45b7d1']
            });
        }, 200);

        setTimeout(() => {
            confetti({
                particleCount: 50,
                angle: 120,
                spread: 55,
                origin: { x: 1 },
                colors: ['#96ceb4', '#feca57', '#ff9ff3']
            });
        }, 400);
    }

    // Continuous confetti effect
    function continuousConfetti() {
        confetti({
            particleCount: 3,
            angle: 60,
            spread: 55,
            origin: { x: 0 },
            colors: ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57']
        });
        confetti({
            particleCount: 3,
            angle: 120,
            spread: 55,
            origin: { x: 1 },
            colors: ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57']
        });
    }

    // Firework effect
    function randomInRange(min, max) {
        return Math.random() * (max - min) + min;
    }

    function firework() {
        confetti({
            angle: randomInRange(55, 125),
            spread: randomInRange(50, 70),
            particleCount: randomInRange(50, 100),
            origin: {
                x: randomInRange(0.1, 0.9),
                y: randomInRange(0.1, 0.9)
            },
            colors: ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3', '#54a0ff']
        });
    }

    // Modal Management
    function closeSuccessModal() {
        const modal = document.getElementById('successModal');
        modal.style.display = 'none';
        // Remove blur from background
        document.querySelector('.order-confirmation').style.filter = 'none';
    }

    // Enhanced Confetti Animation for Modal
    function createModalConfetti() {
        // Celebration burst when modal appears
        confetti({
            particleCount: 150,
            spread: 70,
            origin: { y: 0.6 },
            colors: ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3', '#54a0ff']
        });

        // Side confetti bursts
        setTimeout(() => {
            confetti({
                particleCount: 80,
                angle: 60,
                spread: 55,
                origin: { x: 0 },
                colors: ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57']
            });
        }, 300);

        setTimeout(() => {
            confetti({
                particleCount: 80,
                angle: 120,
                spread: 55,
                origin: { x: 1 },
                colors: ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57']
            });
        }, 500);

        // Golden shower effect
        setTimeout(() => {
            confetti({
                particleCount: 100,
                spread: 120,
                origin: { y: 0.3 },
                colors: ['#ffd700', '#ffed4e', '#f9ca24', '#f0932b']
            });
        }, 800);
    }

    // Initialize modal and celebrations
    window.onload = function () {
        // Show modal immediately
        const modal = document.getElementById('successModal');
        modal.style.display = 'block';

        // Start confetti celebration
        setTimeout(() => {
            createModalConfetti();
        }, 500);

        // Additional celebration after 2 seconds
        setTimeout(() => {
            createModalConfetti();
        }, 2500);

        // Start continuous confetti every 4 seconds while modal is open
        const confettiInterval = setInterval(() => {
            if (modal.style.display !== 'none') {
                continuousConfetti();
            } else {
                clearInterval(confettiInterval);
            }
        }, 4000);

        // Fireworks every 6 seconds while modal is open
        const fireworkInterval = setInterval(() => {
            if (modal.style.display !== 'none') {
                firework();
            } else {
                clearInterval(fireworkInterval);
            }
        }, 6000);
    };

    // Close modal when clicking outside
    window.onclick = function(event) {
        const modal = document.getElementById('successModal');
        if (event.target === modal) {
            closeSuccessModal();
        }
    };

    // Close modal with Escape key
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            closeSuccessModal();
        }
    });
</script>

<style>
/* Success Modal Styles */
.success-modal-content {
    border: none;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    background: linear-gradient(135deg, #276100 0%, #28c76f 50%, #54ca68 100%);
    color: white;
    overflow: hidden;
    position: relative;
}

.success-modal-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%, rgba(255,255,255,0.1) 100%);
    animation: shimmer 3s ease-in-out infinite;
}

.btn-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
    transition: all 0.3s ease;
}

.btn-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* Success Checkmark Animation */
.success-icon-container {
    position: relative;
    display: inline-block;
}

.success-checkmark {
    width: 100px;
    height: 100px;
    margin: 0 auto;
    position: relative;
}

.checkmark {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    display: block;
    stroke-width: 3;
    stroke: #28c76f;
    stroke-miterlimit: 10;
    margin: 10% auto;
    box-shadow: inset 0px 0px 0px #28c76f;
    animation: fill 0.4s ease-in-out 0.4s forwards, scale 0.3s ease-in-out 0.9s both, bounce 0.6s ease-in-out 1.2s;
}

.checkmark__circle {
    stroke-dasharray: 166;
    stroke-dashoffset: 166;
    stroke-width: 3;
    stroke-miterlimit: 10;
    stroke: #28c76f;
    fill: none;
    animation: stroke 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
}

.checkmark__check {
    transform-origin: 50% 50%;
    stroke-dasharray: 48;
    stroke-dashoffset: 48;
    stroke-width: 3;
    stroke-miterlimit: 10;
    stroke: #ffffff;
    animation: stroke 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.8s forwards;
}

/* Success Message Styles */
.success-title {
    font-size: 2.2rem;
    font-weight: 700;
    color: #ffffff;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3), 0 0 20px rgba(255, 255, 255, 0.5);
    animation: titleGlow 2s ease-in-out infinite alternate;
}

.success-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    line-height: 1.6;
}

.order-info-card {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 15px;
    padding: 1.5rem;
    margin: 1.5rem 0;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.order-info-card h5 {
    color: #e8f5e9;
    margin-bottom: 1rem;
    font-weight: 600;
}

.order-info-card p {
    margin: 0.5rem 0;
    font-size: 1rem;
}

.action-buttons .btn {
    border-radius: 25px;
    padding: 12px 30px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.action-buttons .btn-primary {
    background: linear-gradient(45deg, #30583A, #54ca68);
    border: none;
    color: white;
}

.action-buttons .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(40, 199, 111, 0.4);
    background: linear-gradient(45deg, #276100, #28c76f);
}

.action-buttons .btn-outline-primary {
    background: transparent;
    border: 2px solid white;
    color: white;
}

.action-buttons .btn-outline-primary:hover {
    background: white;
    color: #276100;
    transform: translateY(-2px);
}

/* Animations */
@keyframes shimmer {
    0% { transform: translateX(-100%); }
    50% { transform: translateX(100%); }
    100% { transform: translateX(100%); }
}

@keyframes stroke {
    100% {
        stroke-dashoffset: 0;
    }
}

@keyframes scale {
    0%, 100% {
        transform: none;
    }
    50% {
        transform: scale3d(1.1, 1.1, 1);
    }
}

@keyframes fill {
    100% {
        box-shadow: inset 0px 0px 0px 30px #4bb71b;
    }
}

@keyframes icon-line-tip {
    0% {
        width: 0;
        left: 1px;
        top: 19px;
    }
    54% {
        width: 0;
        left: 1px;
        top: 19px;
    }
    70% {
        width: 50px;
        left: -8px;
        top: 37px;
    }
    84% {
        width: 17px;
        left: 21px;
        top: 48px;
    }
    100% {
        width: 25px;
        left: 14px;
        top: 45px;
    }
}

@keyframes icon-line-long {
    0% {
        width: 0;
        right: 46px;
        top: 54px;
    }
    65% {
        width: 0;
        right: 46px;
        top: 54px;
    }
    84% {
        width: 55px;
        right: 0px;
        top: 35px;
    }
    100% {
        width: 47px;
        right: 8px;
        top: 38px;
    }
}

@keyframes titleGlow {
    0% {
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3), 0 0 20px rgba(255, 255, 255, 0.5);
    }
    100% {
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3), 0 0 30px rgba(255, 255, 255, 0.8), 0 0 40px rgba(255, 255, 255, 0.6);
    }
}

/* Modal Confetti Styles */
.modal-confetti-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
    z-index: 1;
}

.modal-confetti-piece {
    position: absolute;
    width: 8px;
    height: 8px;
    background: #ffffff;
    animation: modalConfettiFall 3s linear infinite;
}

.modal-confetti-piece:nth-child(1) {
    left: 10%;
    animation-delay: 0s;
    background: #ffffff;
    border-radius: 50%;
}

.modal-confetti-piece:nth-child(2) {
    left: 20%;
    animation-delay: 0.5s;
    background: #e8f5e9;
    transform: rotate(45deg);
}

.modal-confetti-piece:nth-child(3) {
    left: 30%;
    animation-delay: 1s;
    background: #ffffff;
    border-radius: 50%;
}

.modal-confetti-piece:nth-child(4) {
    left: 40%;
    animation-delay: 1.5s;
    background: #e8f5e9;
    transform: rotate(45deg);
}

.modal-confetti-piece:nth-child(5) {
    left: 50%;
    animation-delay: 2s;
    background: #ffffff;
    border-radius: 50%;
}

.modal-confetti-piece:nth-child(6) {
    left: 60%;
    animation-delay: 0.3s;
    background: #e8f5e9;
    transform: rotate(45deg);
}

.modal-confetti-piece:nth-child(7) {
    left: 70%;
    animation-delay: 0.8s;
    background: #ffffff;
    border-radius: 50%;
}

.modal-confetti-piece:nth-child(8) {
    left: 80%;
    animation-delay: 1.3s;
    background: #e8f5e9;
    transform: rotate(45deg);
}

.modal-confetti-piece:nth-child(9) {
    left: 90%;
    animation-delay: 1.8s;
    background: #ffffff;
    border-radius: 50%;
}

.modal-confetti-piece:nth-child(10) {
    left: 15%;
    animation-delay: 2.3s;
    background: #e8f5e9;
    transform: rotate(45deg);
}

@keyframes modalConfettiFall {
    0% {
        transform: translateY(-100vh) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translateY(100vh) rotate(720deg);
        opacity: 0;
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* Fireworks Animation */
.fireworks-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
    z-index: 1;
}

.firework {
    position: absolute;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    animation: firework 2s ease-out infinite;
}

.firework:nth-child(1) {
    left: 20%;
    top: 30%;
    animation-delay: 0s;
    background: radial-gradient(circle, #ff6b6b, #ee5a24);
}

.firework:nth-child(2) {
    left: 80%;
    top: 20%;
    animation-delay: 0.5s;
    background: radial-gradient(circle, #4834d4, #686de0);
}

.firework:nth-child(3) {
    left: 60%;
    top: 40%;
    animation-delay: 1s;
    background: radial-gradient(circle, #00d2d3, #01a3a4);
}

.firework:nth-child(4) {
    left: 30%;
    top: 50%;
    animation-delay: 1.5s;
    background: radial-gradient(circle, #ff9ff3, #f368e0);
}

.firework:nth-child(5) {
    left: 70%;
    top: 60%;
    animation-delay: 2s;
    background: radial-gradient(circle, #feca57, #ff9f43);
}

/* Floating Particles */
.particles-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
    z-index: 2;
}

.particle {
    position: absolute;
    width: 6px;
    height: 6px;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
    border-radius: 50%;
    animation: float 4s ease-in-out infinite;
}

.particle:nth-child(1) { left: 10%; animation-delay: 0s; }
.particle:nth-child(2) { left: 20%; animation-delay: 0.5s; }
.particle:nth-child(3) { left: 30%; animation-delay: 1s; }
.particle:nth-child(4) { left: 40%; animation-delay: 1.5s; }
.particle:nth-child(5) { left: 50%; animation-delay: 2s; }
.particle:nth-child(6) { left: 60%; animation-delay: 2.5s; }
.particle:nth-child(7) { left: 70%; animation-delay: 3s; }
.particle:nth-child(8) { left: 80%; animation-delay: 3.5s; }
.particle:nth-child(9) { left: 90%; animation-delay: 4s; }
.particle:nth-child(10) { left: 15%; animation-delay: 0.3s; }
.particle:nth-child(11) { left: 85%; animation-delay: 0.8s; }
.particle:nth-child(12) { left: 45%; animation-delay: 1.3s; }

/* Success Badge */
.success-badge-container {
    position: relative;
    z-index: 10;
    margin: 3rem 0;
}

.success-badge {
    position: relative;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
    animation: badgeEntrance 1s ease-out, badgePulse 3s ease-in-out 1s infinite;
    overflow: hidden;
}

.badge-glow {
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
    animation: rotate 4s linear infinite;
}

.badge-content {
    position: relative;
    z-index: 2;
    color: white;
}

.success-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    animation: iconBounce 2s ease-in-out infinite;
}

.success-text h3 {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.success-text p {
    font-size: 1rem;
    opacity: 0.9;
    margin: 0;
}

.badge-shine {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: shine 3s ease-in-out infinite;
}

/* Celebration Text */
.celebration-text {
    position: relative;
    z-index: 10;
    margin: 2rem 0;
    animation: textEntrance 1.5s ease-out 0.5s both;
}

.rainbow-text {
    font-size: 2.5rem;
    font-weight: 800;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: rainbowMove 3s ease-in-out infinite, textBounce 2s ease-in-out infinite;
    text-shadow: 0 0 30px rgba(255, 107, 107, 0.5);
}

.celebration-subtitle {
    font-size: 1.3rem;
    color: #666;
    margin-top: 1rem;
    animation: fadeInUp 1s ease-out 1s both;
}

/* Floating Hearts */
.hearts-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
    z-index: 3;
}

.heart {
    position: absolute;
    font-size: 2rem;
    animation: heartFloat 6s ease-in-out infinite;
    opacity: 0.8;
}

.heart:nth-child(1) { left: 10%; animation-delay: 0s; }
.heart:nth-child(2) { left: 30%; animation-delay: 1s; }
.heart:nth-child(3) { left: 50%; animation-delay: 2s; }
.heart:nth-child(4) { left: 70%; animation-delay: 3s; }
.heart:nth-child(5) { left: 90%; animation-delay: 4s; }
.heart:nth-child(6) { left: 20%; animation-delay: 5s; }

/* Sparkles */
.sparkles-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
    z-index: 4;
}

.sparkle {
    position: absolute;
    font-size: 1.5rem;
    animation: sparkleFloat 4s ease-in-out infinite;
}

.sparkle:nth-child(1) { left: 15%; top: 20%; animation-delay: 0s; }
.sparkle:nth-child(2) { left: 85%; top: 30%; animation-delay: 0.5s; }
.sparkle:nth-child(3) { left: 25%; top: 70%; animation-delay: 1s; }
.sparkle:nth-child(4) { left: 75%; top: 80%; animation-delay: 1.5s; }
.sparkle:nth-child(5) { left: 45%; top: 10%; animation-delay: 2s; }
.sparkle:nth-child(6) { left: 65%; top: 60%; animation-delay: 2.5s; }
.sparkle:nth-child(7) { left: 35%; top: 40%; animation-delay: 3s; }
.sparkle:nth-child(8) { left: 55%; top: 90%; animation-delay: 3.5s; }

/* Keyframe Animations */
@keyframes firework {
    0% {
        transform: scale(0) rotate(0deg);
        opacity: 1;
        box-shadow: 0 0 0 0 currentColor;
    }
    50% {
        transform: scale(1) rotate(180deg);
        opacity: 1;
        box-shadow:
            0 0 20px 10px currentColor,
            0 0 40px 20px currentColor,
            0 0 60px 30px currentColor;
    }
    100% {
        transform: scale(2) rotate(360deg);
        opacity: 0;
        box-shadow:
            0 0 40px 20px transparent,
            0 0 80px 40px transparent,
            0 0 120px 60px transparent;
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(100vh) scale(0);
        opacity: 0;
    }
    10% {
        opacity: 1;
        transform: translateY(90vh) scale(1);
    }
    90% {
        opacity: 1;
        transform: translateY(-10vh) scale(1);
    }
    100% {
        transform: translateY(-20vh) scale(0);
        opacity: 0;
    }
}

@keyframes badgeEntrance {
    0% {
        transform: scale(0) rotate(-180deg);
        opacity: 0;
    }
    50% {
        transform: scale(1.1) rotate(-90deg);
        opacity: 0.8;
    }
    100% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
}

@keyframes badgePulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 25px 50px rgba(102, 126, 234, 0.5);
    }
}

@keyframes rotate {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes iconBounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

@keyframes shine {
    0% {
        left: -100%;
    }
    50% {
        left: 100%;
    }
    100% {
        left: 100%;
    }
}

@keyframes textEntrance {
    0% {
        transform: translateY(50px) scale(0.8);
        opacity: 0;
    }
    100% {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

@keyframes rainbowMove {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

@keyframes textBounce {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes fadeInUp {
    0% {
        transform: translateY(30px);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes heartFloat {
    0% {
        transform: translateY(100vh) scale(0) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
        transform: translateY(90vh) scale(1) rotate(45deg);
    }
    90% {
        opacity: 1;
        transform: translateY(-10vh) scale(1) rotate(315deg);
    }
    100% {
        transform: translateY(-20vh) scale(0) rotate(360deg);
        opacity: 0;
    }
}

@keyframes sparkleFloat {
    0%, 100% {
        transform: scale(0) rotate(0deg);
        opacity: 0;
    }
    25% {
        transform: scale(1) rotate(90deg);
        opacity: 1;
    }
    50% {
        transform: scale(1.2) rotate(180deg);
        opacity: 0.8;
    }
    75% {
        transform: scale(1) rotate(270deg);
        opacity: 1;
    }
}

/* Card Animations */
.summary-card.purchase-success {
    animation: slideInRight 1s ease-out;
}

.cart-card {
    animation: slideInLeft 1s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translate3d(100%, 0, 0);
        opacity: 0;
    }
    to {
        transform: translate3d(0, 0, 0);
        opacity: 1;
    }
}

@keyframes slideInLeft {
    from {
        transform: translate3d(-100%, 0, 0);
        opacity: 0;
    }
    to {
        transform: translate3d(0, 0, 0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .rainbow-text {
        font-size: 2rem;
    }

    .celebration-subtitle {
        font-size: 1.1rem;
    }

    .success-badge {
        padding: 1.5rem;
    }

    .success-text h3 {
        font-size: 1.5rem;
    }

    .success-icon {
        font-size: 2.5rem;
    }

    .heart {
        font-size: 1.5rem;
    }

    .sparkle {
        font-size: 1.2rem;
    }

    .particle {
        width: 4px;
        height: 4px;
    }

    .firework {
        width: 3px;
        height: 3px;
    }
}

/* Enhanced Checkmark Styles */
.enhanced-checkmark {
    position: relative;
    z-index: 10;
}

.checkmark-circle {
    position: relative;
    display: inline-block;
    font-size: 4rem;
    animation: checkmarkBounce 2s ease-in-out infinite;
}

.checkmark-pulse {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80px;
    height: 80px;
    border: 3px solid #28a745;
    border-radius: 50%;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes checkmarkBounce {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

@keyframes pulse {
    0% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(2);
        opacity: 0;
    }
}

/* Sparkles Container */
.sparkles-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
    z-index: 2;
}

.sparkle {
    position: absolute;
    font-size: 2rem;
    animation: sparkleFloat 4s ease-in-out infinite;
    opacity: 0;
}


</style>
