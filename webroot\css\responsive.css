@import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&display=swap');
@media only screen and (max-width: 1400px) {
    .owl-carousel .owl-nav button.owl-prev {
        left: -40px;
    }

    .aboutus-sec-3 h1 {
        font-size: 56px !important;
    }

    .owl-carousel .owl-nav button.owl-next {
        right: -40px;
    }

    .owl-carousel .owl-nav button.owl-next,
    .owl-carousel .owl-nav button.owl-prev {
        padding: 12px !important;
    }

    .o-general-banner .carousel-control-prev {
        left: 68%;
    }

    .navbar-light .navbar-nav .nav-link {
        padding: 0px !important;
    }
}

@media only screen and (max-width: 1200px) {
    .navbar-light .navbar-nav .nav-link {

        font-size: 13px !important;
    }

    .about-general-banner {

        height: 385px;
    }

    .product-card .price {
        font-size: 15px;
    }

    .navbar-light .navbar-nav .nav-link {

        font-size: 15px !important;
    }

    .services .carousel-control-prev {
        left: -5%;
    }

    .aboutus-sec-3-bg-green h2 {
        font-size: 32px;
        font-weight: 700;
    }
}

@media only screen and (max-width: 1100px) {
    .exclude .header {
        padding-top: 40px;
        padding-bottom: 0px;
    }
.exclude .content-exclude{
    margin: auto 1%;

}
    .exclude {
        height: 100%;
    }

    .exclude .content h3 {
        font-size: 30px;
    }
}

@media only screen and (max-width: 1024px) {

    .footer-banner {
        /* background: url(../img/ozone/Footer-md-screen.png) no-repeat bottom center; */
        background-color: #CBFED74D;
    }


    .better-air {
        margin-top: 0%;
    }

    .navbar-light .navbar-nav .show>.nav-link,
    .navbar-light .navbar-nav .nav-link.active {
        font-size: 12px;
    }

    .navbar-light .navbar-nav .show>.nav-link,
    .navbar-light .navbar-nav .nav-link {
        font-size: 12px;
    }

    .lead-md-banner h1 {
        font-size: 35px;
    }

    .lead-md-banner h2 {
        font-size: 32px;
        /* margin-top: 20px; */
    }

    .desktop-filter {
        display: none;
    }
}

@media only screen and (max-width: 990px) {
    .swiper-free-mode>.swiper-wrapper {
        overflow: scroll;
    }
.about-last-sec {

    margin: 0px 0px;
}
    .swiper-counter {
        top: 0px;
        right: 15%;
    }

    .product-details-banner .product-title {
        font-size: 32px;
    }

    .swiper-navigation {
        right: 7%;
        top: 25px;
    }

    body.rtl .swiper-navigation {
        right: 4% !important;
    }

    .store-card .frg-pass {

        margin-top: 50px !important;

    }

    .store-card {
        padding: 30px 30px 40px !important;

    }

    .aboutus-banner-sec-2 h2 {
        font-size: 18px;
        font-weight: 700;
        text-align: justify;
        margin: 10px 0px !important;
        line-height: 25px;
    }

    .wishlist .wishlist-img {

        height: 100% !important;
    }

    .product-gallery {
        margin-top: 20px !important;
    }

    .sec-title {
        font-size: 35px;
    }

    #gallerycarousel .carousel-inner img {
        padding: 0px !important;
        margin: 0px !important;
    }

    .price-range {
        font-size: 25px !important;
    }

    .product-details-banner .form-label {
        font-size: 14px;
    }

    .product-feat-img-mobile {
        padding: 0 !important;
    }

    .product-features .enhance,
    .product-features .auto-clean {
        padding: 0 !important;
    }

    .product-features .featured-content {
        padding: 5% 0px 0px;
    }

    .featured-content h1 {
        font-size: 22px;
    }

    .featured-content p {
        color: #30583A;
        font-size: 12px;
    }

    .breadcrumb .active {
        font-size: 10px;
        line-height: 30px;
    }

    #purchase-tab .nav-tabs .nav-item.show .nav-link,
    .nav-tabs .nav-link.active {
        font-size: 16px;
    }

    .cart-tab-head ul.nav-tabs li::after {

        width: 0;
    }

    .breadcrumb-item a {
        font-size: 10px;
    }

    .product-breadcrumbs {
        margin: 5px 2px !important;
    }

    #gallerycarousel .carousel-control-prev,
    #gallerycarousel .carousel-control-next {
        display: flex !important;
    }

    #gallerycarousel .carousel-inner .carousel-item {
        margin: 0px !important;
        padding: 0px !important;
    }

    .purchase-cta {
        margin-top: 10px;
    }

    .need-help .text-success {
        font-size: 18px;
    }

    .reviews .sec-title {
        font-size: 22px;
        color: #30583A;
    }

    .purchase-cta {
        margin-top: 10px;
    }

    .product-details-banner h1 {
        font-size: 25px;
    }

    .scroll-spy .nav-link {
        font-size: 18px;
    }

    .scroll-spy .nav-link.active {
        font-size: 18px;
    }

    .about-content {
        background: url(../img/ozone/lead-md-banner.png) no-repeat top left;
    }

    .centralized-ac {
        background: url(../img/ozone/centralized-ac-mb.png) no-repeat top center;
        height: auto;
        min-height: 160px;
        width: 100%;
        background-size: contain;
        margin: 0 !important;
    }

    .highlight-img-3-brief {
        padding-top: 0px !important;
    }

    .about-general-banner {
        background: url(../img/ozone/abt-us-banner-mb.png) no-repeat center;
        position: relative;
        height: 166px;
        width: 100%;
        background-size: cover;
    }

    .aboutus-describtion {
        color: #0a501c;
        font-size: 15px;
        font-weight: 400;
        margin: 17px 0px 0px !important;
        text-align: justify;

    }

    .content-brief {
        background: url(../img/ozone/Subtract-corprate-brief.png) no-repeat center;
        margin-top: 15px !important;
    }

    .aboutus-banner-sec-2 {
        background: url(../img/ozone/aboutus-sec-2.png) no-repeat center;
        position: relative;
        height: auto;
    }

    .aboutus-sec-3 h1 {
        text-align: left;
    }

    .aboutus-sec-3-bg-green h2 {
        font-size: 22px;
    }

    .aboutus-sec-3-bg-green .aboutus-describtion {
        font-size: 12px;
        overflow: visible;
        padding-bottom: 40px;
    }

    .aboutus-sec-3-bg-green .card-aboutus {
        padding: 0;
    }

    .services .carousel-item .card-box-shadow {
        padding: 10px 7px 10px 0px;
        height: auto;
        width: 95%;
        margin: 5px auto;
        border-radius: 25px;
        box-shadow: 0px 0px 13px 3px #058E6E24;
    }

    .content-brief .content p {
        color: #231F20;
        font-size: 18px;
        font-weight: 400;
        line-height: 35px;
        text-align: justify;
        padding-bottom: 12px;
        padding: 25px 0px 12px;
    }

    .misson-vission .vision {
        border: 1px solid #00000000;
        padding: 15px;
        border-radius: 20px;
    }

    /* .misson-vission .vision:nth-of-type(2) {
        border: 1px solid #6FC284;
        padding: 10px;
        border-radius: 20px;
    } */
    .misson-vission .vision:hover {
        border: 1px solid #6FC284;
        padding: 15px;
        border-radius: 20px;
    }

    .misson-vission {
        background: none;
    }

    .misson-vission .vision .title {
        margin: 0px 0px 0px 10px;
    }

    .misson-vission .vision .title {
        color: #034833;
        font-size: 24px;
        font-weight: 700;

    }

    .misson-vission .vision .describtion {
        font-size: 16px;
        min-height: max-content !important;
        padding: 0px;
        border-radius: 0px !important;
        text-align: justify;
    }

    .misson-vission .vision .innovation {
        font-size: 16px;
    }

    .exclude .header {
        padding-top: 40px;
        padding-bottom: 15px;
        border-bottom: 1px solid transparent;
        border-image-source: linear-gradient(270deg, rgba(78, 101, 182, 0) 5.37%, #000000 50.54%, rgba(78, 101, 182, 0) 97.98%);
        border-image-slice: 1;
    }

    #zoom-icon {
        width: 35px !important;
    }

    .exclude .section-title {
        font-size: 22px;
        text-align: center;
        padding: 10px !important;
    }

    .exclude {
        background: #6FC284;
        height: 100%;
        margin: 40px 0px;
        border-radius: 25px;
        padding-bottom: 20px;
    }

    .exclude .content h3 {
        color: #fff;
        font-size: 30px;
        font-weight: 700;
        text-align: left;
        margin-bottom: 0px;
    }

    .exclude .about-idea {
        width: 64px;
        height: 64px;
    }

    .our-strength .content {
        padding-top: 0;
    }

    .our-strength {
        background: url(../img/ozone/our-strength-banner.png) no-repeat top -298px center;
        height: auto;
        top: 0;
    }

    #our-strength {
        margin: 0;
    }

    .section-title-abut-install {
        position: relative;
        font-size: 25px;
        font-weight: 700;
        text-align: center;
        margin: 26px 0px !important;
    }

    .carousel.slide {
        padding: 0;
    }


    .filter-sidebar {
        position: fixed;
        top: 0;
        left: -320px;
        width: 300px;
        height: 100vh;
        background: #fff;
        box-shadow: 2px 0 8px rgba(0, 0, 0, 0.2);
        z-index: 1050;
        transition: left 0.3s ease;
        overflow-y: auto;
    }

    .filter-sidebar.active {
        left: 0;
    }

    .filter-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: rgba(0, 0, 0, 0.4);
        z-index: 1049;
        display: none;
    }

    .filter-backdrop.active {
        display: block;
    }

    /* On desktop, show sidebar normally */
    @media (min-width: 992px) {
        .filter-sidebar {
            position: static !important;
            width: auto !important;
            height: auto !important;
            box-shadow: none !important;
            left: 0 !important;
        }

        .filter-backdrop {
            display: none !important;
        }
    }

    .show-results p {
        margin: 0px;
        color: #231F20;
        font-size: 12px;
        font-weight: 400;
    }

    .filters-btn-md {

        color: #30583A;
        border-radius: 20px;
        background: #ffffff00;
        border: 1px solid #30583A;
        font-weight: 700;
        font-size: 14px;

    }

    .catageries {
        overflow: auto;
        flex-direction: row !important;
        flex-wrap: nowrap !important;
        align-items: center;
        padding: 10px 0px;
    }

    .products .btn-success {
        font-size: 14px;
        margin: auto;
    }

    .products .btn-secondary {
        font-size: 14px;
        white-space: nowrap;
        margin: 0px 5px;
    }

    .banner-section {
        position: relative;
        margin-top: 0px !important;
    }

    .navbar-head-main {
        box-shadow: none !important;
    }

    .banner-section img {
        width: 100%;
    }

    .description {

        margin: 30px 0px !important;
    }

    .dropdown-toggle::after {
        background: url(../img/ozone/arrow-down-md-head.png) no-repeat center bottom;
    }

    .banner {
        min-height: 500px;
    }

    .navbar-head-main {
        border-bottom: 0px solid #355C3F;
    }

    .navbar-head-main .navbar-collapse {
        border-bottom: 1px solid #355C3F;
        /* border-bottom-left-radius: 12px;
        border-bottom-right-radius: 12px; */
    }

    .fi-qa {
        margin: 5px 0px;
    }

    .right-bord {
        border-right: 1px solid #D9D9D9;
        height: 37px;
    }

    .lead-banner {
        background: url(../img/ozone/secound-banner.png) no-repeat top 10px left;
        margin: 30px 0px;
    }

    .navbar-icons .dropdown .btn,
    .navbar-icons .dropdown .btn:hover {
        color: #ffffff;
        font-weight: 600;
    }

    .explore-btn {
        position: relative;
        bottom: 0;
        margin-bottom: 20px;
    }

    .explore-btn:hover {
        position: relative;
        bottom: 0;
        margin-bottom: 20px;
    }

    .nav-tabs {
        margin-top: 40px;
    }

    .section-title {
        font-size: 32px;
    }

    .air-quality {
        background: url(../img/ozone/air-quality.png) no-repeat center bottom -60px;
        padding: 20px 0px;
        margin: 0px;
    }

    .latest-offers {
        background: none;
        /* min-height: 764px; */
    }

    #general-carousel {
        /* position: absolute; */
    }

    .owl-carousel .owl-stage .owl-item {
        /* width: 350px !important; */
    }

    .product-card .substract img {
        /* height: 333px; */
        object-fit: contain;
    }

    #purchase-tab .nav-tabs .nav-item.show .nav-link,
    .nav-tabs .nav-link.active {
        color: #ffffff;
        font-size: 16px;
        font-weight: 700;
        background: #231f20;
        border: 0px;
    }

    body.rtl .filter-slide-panel.open {
        right: 0;
    }

    body.rtl .filter-slide-panel {

        right: -350px;
    }

    .cart-tab-head #purchase-tab .nav-tabs .nav-item.show .nav-link,
    .cart-tab-head .nav-tabs .nav-link {
        color: #717378;
        font-size: 12px;
        font-weight: 400;
        background: #231f2000;
        border: 0px;
        white-space: nowrap;
        margin-bottom: 10px;
        padding: 5px 0px !important;
        margin: 5px 0px !important;
    }

    .cart-tab-head {
        background: #F4F4F4 !important;
        margin: 0px !important;
    }

    .product-general-banner .blur-bg {
        background: #ffffff57;
        backdrop-filter: blur(10px);
        padding: 60px 10px 60px 25px;
        border-top-right-radius: 65px;
        border-bottom-right-radius: 65px;
        margin: auto 0px;
        width: 60%;
    }

    .catogory-md {
        margin: 15px 0px;
    }

    .product-general-banner .blur-bg p {
        color: #231F20;
        font-size: 16px;
        font-weight: 400;
    }
}

@media only screen and (max-width: 800px) {
    .mobile-searchfield {
        width: 100%;
    }

        .services .carousel-item .card-box-shadow img {
        width: 20px;
        margin-right: 10px;
        margin-left: 10px;
    }

    .services .carousel-item .card-box-shadow p {
        font-size: 12px;
        font-weight: 700;
    }

    .aboutus-sec-3-bg-green .card-aboutus img {
        width: 40px;

    }

    .services {
        padding-top: 0 !important;
        padding-bottom: 0 !important;
    }

    .highlight-latest::after {
        display: none;
    }

    .product-card .substract {

        min-height: 270px;
    }

    .product-card .badge {
        margin-left: 18%;
    }

    /* .mobile-searchfield .input-group {
        padding: 5px;
        margin: 12px 0px 0px;
    } */
    .mobile-searchfield .input-group {
        padding: 5px;
        margin: 12px 0px 0px;
        border: 1px solid #999999;
        border-radius: 20px;
    }

    .mobile-searchfield #product-search-trigger {

        display: flex;
        width: 100%;
        text-decoration: none;
    }

    .mobile-searchfield .input-group .input-group-text {
        padding: 5px;
    }

    body.rtl .product-card .add-cart {
        background-color: #DBF5E2;
        position: relative;
        left: 0px;
        padding: 10px;
        border-top-right-radius: 0px;
        border-top-left-radius: 30px !important;
    }

    body.rtl .add-cart .bor-top-rig {
        border-bottom: 8px solid #DBF5E2;
        position: absolute;
        left: unset;
        right: -7px;
        top: -12px;
        padding: 6px;
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 119px;
        border-left: 0;
        border-right: 8px solid #DBF5E2;
    }

    body.rtl .product-card:hover .add-cart {
        background-color: #FFF8DE;
        position: relative;
        right: 0px;
        padding: 10px;
        border-top-left-radius: 30px !important;
        border-top-right-radius: 0px;
    }

    .product-card:hover .energy-rating {
        background-color: #FF6200;
        color: white;
        display: flex;
        padding: 15px;
        align-items: center;
        justify-content: center;
        border-radius: 30px;
    }

    body.rtl .product-card:hover .add-cart .bor-bot-left {
        border-bottom: 8px solid #FFF8DE;
        position: absolute;
        right: unset;
        bottom: -8px;
        padding: 6px;
        border-bottom-right-radius: 119px;
        border-bottom-left-radius: 0;
        border-left: 0px solid #FFF8DE;
        left: -13px;
        border-right: 8px solid #FFF8DE;
    }


    body.rtl #productListItems .product-card .substract .position-absolute .d-flex {
        flex-direction: row-reverse !important;
    }

    .gap-4.catogory-md {
        gap: 0.5rem !important;
    }

    body.rtl .product-card .badge {

        margin-left: 8%;
        margin-right: 0%;
    }

    body.rtl .product-card:hover .add-cart .bor-top-rig {
        border-bottom: 8px solid #FFF8DE;
        position: absolute;
        right: -7px;
        top: -12px;
        left: unset;
        padding: 6px;
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 119px;
        border-left: 0px solid #FFF8DE;
        border-right: 8px solid #FFF8DE;
    }

    .form-login .logo img {
        width: 150px;
    }

    .login .login-container {
        padding: 33px 0px 0px 0px;
        border-top-left-radius: 0px;
    }

    .login .login-container .btn-close {
        position: relative;
    }

    .banner {
        min-height: 433px;
    }

    .testimonial-banner .highlight::after {
        content: "";
        position: absolute;
        left: 14px;
        bottom: -1px;
        width: 100%;
        height: 23px;
        background: url(../img/ozone/Brush.png) no-repeat bottom left;
        border-radius: 50px;
    }

    .navbar-light .navbar-toggler-icon {
        background-image: url(../img/ozone/navbar-menu-md.png) !important;
        width: 35px;
        height: 35px;
    }

    .navbar-head-main .dropdown-toggle::after {
        content: "";
        display: inline-block;
        background: url(../img/ozone/arrow-down-green.png) no-repeat center bottom;
        background-size: contain;
        width: 7px;
        height: 10px;
        margin-left: 0px;
        margin-bottom: -2px;
        border: 0 !important;
        vertical-align: middle;
        filter: brightness(5) saturate(100%) invert(100%) sepia(100%) saturate(0%) hue-rotate(288deg) brightness(102%) contrast(102%);
    }

    .purchase-cart .form-check {
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
    }

    .navbar-head-main .right-bord {
        height: 19px;
    }

    .head-icons-md {
        width: 25px;
        padding: 0px !important;
        margin: 0px !important;
    }

    .navbar-light .navbar-nav .show>.nav-link,
    .navbar-light .navbar-nav .nav-link {
        margin-bottom: 10px;
    }

    .product-card {
        padding: 8px;
    }

    .clickable-product-card .px-3 {
        padding-left: 5px !important;
        padding-right: 5px !important;
        padding-bottom: 10px !important;
    }

    .product-card .substract img {
        padding: 10% 0px 10px;
    }

    .product-details-banner h1.product-title {
        font-size: 32px;
    }

    #our-strength .item .testimonial-card {

        width: 100%;
    }
    .abt-exclude-logo {
    width: 200px;
    margin: auto;
}
}

@media only screen and (max-width: 700px) {
    .product-card .price {
        justify-content: space-between;
        align-items: center;
    }

    .product-card .substract img {
        height: 100%;
    }


    .contact-section {
        padding: 0 0;
        margin-top: 35px !important;
        margin-bottom: 30px;
    }

    .contact-info hr {

        margin: auto;
    }

    body.rtl .product-card .badge {
        margin-left: 14%;
        margin-right: 0%;
    }

    .product-card:hover .add-cart .bor-bot-left {
        border-bottom: 8px solid #FFF8DE;
        position: absolute;
        right: unset;
        bottom: -8px;
        padding: 6px;
        border-bottom-right-radius: 119px;
        border-bottom-left-radius: 0;
        border-left: 0px solid #FFF8DE;
        left: -13px;
        border-right: 8px solid #FFF8DE;
    }

    body.rtl .add-cart .bor-bot-left {
        border-bottom: 8px solid #DBF5E2;
        position: absolute;
        right: unset;
        bottom: -8px;
        padding: 6px;
        border-bottom-right-radius: 119px;
        border-right: 8px solid #DBF5E2;
        left: -13px;
        border-bottom-left-radius: 0;
        border-left: 0px solid #DBF5E2;
    }

    #productListItems .col-md-6 {
        padding: 0px;
    }

    .catogory-md {
        flex-wrap: nowrap !important;
        align-content: center;
        justify-content: flex-start;
        align-items: center;
        overflow: auto;
        padding: 10px 0px !important;
        margin-top: 20px;
    }

    #productListItems {
        margin-left: auto;
        margin-right: auto;
    }

    .prdt-viewmore {
        text-align: left !important;
    }

    .aboutus-sec-3 h1 {
        font-size: 35px !important;
    }

    .highlight-img-3 {
        background: url(../img/ozone/addition-md.png) no-repeat right 18px bottom -7px !important;
        padding-bottom: 7px !important;
        padding-top: 31px !important;
        padding-right: 40px;
    }

    .increment-decrement {

        max-width: 105px !important;
    }

    .increment-decrement .btn,
    .increment-decrement input {
        padding: 5px !important;
    }

    .vision img {
        width: 62px;
        height: 62px;
    }

    .vision .d-flex {
        align-items: center !important;
    }

    .mobile-reverse-product {
        flex-direction: column-reverse !important;
    }

    .highlight-img-2-addition {
        background: url(../img/ozone/addition-md.png) no-repeat left 48% top 75% !important;
        background-size: contain;
        padding-bottom: 25px;
        padding-top: 30px !important;
        padding-right: 40% !important;
        /* height: 100px; */
    }

    .highlight-img-3-brief {
        background: url(../img/ozone/Brush-md.png) no-repeat right -17px bottom;
        padding-bottom: 12px !important;
        padding-top: 31px;
        padding-right: 30px;
    }

    .desktop {
        display: none !important;
    }

    .banner {
        min-height: auto !important;
    }

    .navbar-brand .logo-head {
        width: 140px !important;
    }

    .testimonials {
        background: url(../img/ozone/testimonial-1.png) no-repeat center;
        background-size: cover;
        min-height: 100%;
        display: flex;
        align-items: end;
        justify-content: center;
        padding: 107px 20px 20px;
        border-radius: 18px;
    }

    .testimonials-two {
        background: url(../img/ozone/testimonial-2.png) no-repeat center;
        background-size: cover;
        min-height: 100%;
        display: flex;
        align-items: end;
        justify-content: center;
        padding: 107px 20px 20px;
        border-radius: 18px;
    }

    .testimonials-three {
        background: url(../img/ozone/testimonial-3.png) no-repeat center;
        background-size: cover;
        min-height: 100%;
        display: flex;
        align-items: end;
        justify-content: center;
        padding: 107px 20px 20px;
        border-radius: 18px;
    }

    .testimonial-card {
        margin-bottom: 0px
    }

    /* 
    #purchase-tab .nav-tabs .nav-item.show .nav-link,
    .nav-tabs .nav-link .cart-img {
        padding: 5px 7px 7px !important;
    } */
    #purchase-tab .nav-tabs .nav-item.show .nav-link,
    .nav-tabs .nav-link .cart-img {
        padding: 0px 7px 7px !important;
    }

    .cart-remove-add-qua {
        justify-content: space-around !important;
    }

    .cart-section .apply-coupon-btn {
        font-size: 11px;
    }

    .cart-section .btn-place-order {
        padding: 12px 20px;
        font-size: 14px;
    }

    .secure-pay-terms {
        font-size: 10px;
    }

    .payment-icons {
        justify-content: space-evenly;
    }

    .about-content h1 {
        font-size: 30px;
        font-weight: 700;
    }

    .footer-links {

        justify-content: center;
    }

    .filter-slide-panel {
        height: 200vh !important;
    }

    .energy-rating {

        padding: 15px 20px;
    }

    .energy-rating svg {
        width: 25px;
    }

    .product-card .substract .product-img {
        position: relative;
        top: 5px;
        left: 0px;
        height: 205px;
    }

    .products .btn-success {
        font-size: 14px;
        margin: auto;
        white-space: nowrap;
    }
}

@media only screen and (max-width: 490px) {
    .product-card .substract {
        min-height: 300px;
    }

    .product-card .substract img {
        padding: 10% 0px 6%;
    }

    .product-card .price .text-muted {
        font-size: 10px;
    }

    .product-card .substract img {
        height: 170px;
    }

    .owl-carousel .owl-nav button.owl-next {
        right: 0px;
    }

    .about-general-banner {
        height: 151px !important;
    }

    .owl-carousel .owl-nav button.owl-prev {
        left: 0px;
    }

    .navbar-head-main {
        border-top: 0px solid #355C3F;
    }



    .dropdown-toggle::after {
        content: "";
        /* Ensure the pseudo-element is rendered */
        display: inline-block;
        background: url(../img/ozone/arrow-down-green.png) no-repeat center bottom;
        background-size: contain;
        width: 20px;
        height: 10px;
        margin-left: 0px;
        margin-bottom: -2px;
        border: 0 !important;
        vertical-align: middle;

        /* Color the monochrome PNG using filter */
        filter: brightness(0) saturate(100%) invert(100%) sepia(100%) saturate(0%) hue-rotate(288deg) brightness(102%) contrast(102%);
        /* This example applies a bluish tone */
    }

    .popularity-filter .dropdown .dropdown-toggle::after {
        content: "";
        /* Ensure the pseudo-element is rendered */
        display: inline-block;
        background: url(../img/ozone/arrow-down-green.png) no-repeat center bottom;
        background-size: contain;
        width: 20px;
        height: 10px;
        margin-left: 0px;
        margin-bottom: -2px;
        border: 0 !important;
        vertical-align: middle;

        /* Color the monochrome PNG using filter */
        filter: brightness(5) saturate(100%) invert(100%) sepia(100%) saturate(0%) hue-rotate(288deg) brightness(102%) contrast(102%);
        /* This example applies a bluish tone */
    }

    .navbar-icons .dropdown .btn,
    .navbar-icons .dropdown .btn:hover {
        color: #ffffff;
    }

    .navbar-collapse .navbar-icons .dropdown .btn,
    .navbar-icons .dropdown .btn:hover {
        color: #040404;
    }

    .md-language .right-bord {
        border-right: 1px solid #D9D9D9;
        height: 37px;
    }

    .dropdown #dropdownregion {
        line-height: 0 !important;
        padding: 0px;
    }



    .navbar-light .navbar-toggler {
        color: none;
        border-color: 0px;
        padding: 0px;
    }

    .banner {
        min-height: 180px;
    }

    .better-air {
        font-size: 25px;
        margin-top: 100px;
        margin-bottom: 0px !important;
    }

    .lead-banner {
        background: none;
        height: 100%;
        max-width: 100%;
        margin: auto;
    }

    .lead-md-banner {
        background: url(../img/ozone/lead-md-banner.png) no-repeat center left;
        height: 100%;
        max-width: 432px;
        margin: auto auto 0px !important;
    }

    .lead-md-banner h1 {
        line-height: 50px;
        font-size: 34px;
        margin-top: 30px;
    }

    .explore-btn {

        position: relative;
        bottom: 0;
    }

    .products {
        margin-top: 0px;
    }

    .section-title {

        margin-bottom: 0px;
        font-size: 30px;

    }

    .air-quality {
        background: none;
    }

    /* #purchase-tab .nav-tabs .nav-item.show .nav-link,
    .nav-tabs .nav-link {
        color: #231F20;
        font-size: 12px;
        font-weight: 600;
        background: #231F2026;
        border: 0px;
        white-space: nowrap;
        margin-bottom: 10px;
    } */

    #products-tab {
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        overflow-y: hidden;
        padding-bottom: 10px;
    }

    /* .products .tab-content .tab-pane .row {
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        overflow-x: scroll;
    } */

    .btn-outline-primary {
        background-color: #6FC284;
    }

    .latest-offers .owl-carousel .owl-nav button.owl-next,
    .latest-offers .owl-carousel .owl-nav button.owl-prev {
        display: none;
    }

    .o-general-banner {
        background: url(../img/ozone/shop-banner-md-screen.png) no-repeat center;
        min-height: 166px;
        max-width: 440px;
    }

    .o-general-banner .blur-bg {
        background: #ffffff57;
        left: 0;
        top: 20%;
        backdrop-filter: blur(10px);
        width: 50%;
        max-width: fit-content;
        padding: 10px;
        border-top-right-radius: 10px;
        border-bottom-right-radius: 10px;
    }

    .tech-team {
        width: 300px;
    }

    .o-general-banner .blur-bg img {
        max-width: 104px;
    }

    .o-general-banner .blur-bg p {
        font-size: 8px;
        margin: 5px 0px !important;
    }

    .o-general-banner .btn-outline-dark {

        font-size: 8px;
    }

    .carousel-control-prev,
    .carousel-control-next {
        display: none;
    }

    /* .customer-stories-content {
        position: absolute;
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        padding: 10px;
        border-radius: 8px;
        top: 310px;
        background-image: url(../img/ozone/lead-md-banner-right.png) no-repeat right center;
    }
        
    */
    .customer-stories-content {
        position: absolute;
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        padding: 10px;
        border-radius: 8px;
        top: 310px;
        overflow: hidden;
    }

    .customer-stories-content::before {
        content: "";
        position: absolute;
        top: 35%;
        right: 0;
        bottom: 0;
        width: 100%;
        /* adjust as needed */
        background: url('../img/ozone/lead-md-banner-right.png') no-repeat right center;
        background-size: contain;
        z-index: 1;
        pointer-events: none;
        /* allows clicks through the image */
    }

    .testimonial-banner .title,
    .testimonial-banner .section-title {
        text-align: left !important;
    }

    .testimonial-banner .title {
        font-weight: 600;
    }

    .success-banner {
        background: url(../img/ozone/success-banner.png) no-repeat center;
        min-height: 800px;
    }

    .customer-stories-content .title {
        font-weight: 600;
    }

    .testimonial-banner .highlight::after {

        background: none;
    }

    .testimonial-banner .owl-carousel .owl-nav button.owl-next,
    .testimonial-banner .owl-carousel .owl-nav button.owl-prev {
        display: none;
    }

    footer {
        padding: 10px 0px 0px;
    }

        .footer-banner {
        background: url(../img/ozone/footer-md-screen.png) no-repeat bottom center;
        background-color: #CBFED74D;
        min-height: 541px;
        padding: 32px 15px 0px;
        text-align: center;
    }

    footer ul li {
        margin-bottom: 0px;
    }

    .copy-rights {
        text-align: center !important;
    }

}

@media only screen and (max-width: 390px) {
    .lead-md-banner h1 {
        line-height: 42px;
        font-size: 28px;
        margin-top: 30px;
    }

    .section-title {
        font-size: 35px;
    }
}

@media only screen and (max-width: 350px) {
    .lead-md-banner h1 {
        line-height: 38px;
        font-size: 23px;
        margin-top: 30px;
    }

    .energy-rating {

        padding: 14px 18px;
    }

    .navbar-brand .logo-head {
        width: 87px !important;
    }

    .product-card .badge {
        font-size: 10px !important;
        /* margin-left: 0px !important;  */
    }
}

@media only screen and (min-width: 700px) {

    .navbar-icons .dropdown .btn,
    .navbar-icons .dropdown .btn:hover {
        font-size: 12px;
    }



    .mobile {
        display: none;
    }

    .help-card h5 {
        font-size: 27px;
    }

    .thumb-swiper .swiper-slide-thumb-active {
        width: 68px !important;
        height: 68px !important;
    }

    .navbar-icons .dropdown .btn,
    .navbar-icons .dropdown .btn:hover {
        padding: 0px 5px;
    }

    .navbar-icons button {
        padding: 0px 5px;
    }




}


@media only screen and (min-width: 1024px) {
    .mobile-filter {
        display: none;
    }

    .misson-vission {
        background: url(../img/ozone/vision-banner.png) no-repeat left 40% center !important;
    }

    .banner {
        min-height: 595px;
    }

    .owl-carousel .owl-nav button.owl-prev {
        left: -34px;
    }

    .owl-carousel .owl-nav button.owl-next {
        right: -34px;
    }
}


@media only screen and (min-width: 1230px) {
    .navbar-icons button {
        padding: 0px 8px;
    }


    .cart-tab-head ul.nav-tabs li::after {
        width: 150px;
    }

    .navbar-icons .dropdown .btn,
    .navbar-icons .dropdown .btn:hover {

        padding: 0px 8px;
    }

    .thumb-swiper .swiper-slide-thumb-active {

        width: 104px !important;
        height: 85px !important;
    }

    .banner {
        min-height: 690px;
    }
}

@media only screen and (min-width: 1380px) {
    .highlight-img-2-addition {
        background: url(../img/ozone/addition.png) no-repeat left 170px top 30px;
    }

    .product-gallery .photo img {

        max-height: 310px;
    }

    .navbar-icons .dropdown .btn,
    .navbar-icons .dropdown .btn:hover {
        font-size: 16px;
    }

    .banner {
        min-height: 690px;
    }

    .help-card h5 {
        font-size: 35px;
    }

    .aboutus-banner-sec-2 .about-content {
        margin-top: 104px !important;
    }

    .aboutus-sec-3 h1 {
        font-size: 56px;
    }
}

@media only screen and (min-width: 1480px) {
    .banner {
        min-height: 863px;
    }

    .footer-banner {
        background: url(../img/ozone/Footer.png) no-repeat center !important;

    }
}