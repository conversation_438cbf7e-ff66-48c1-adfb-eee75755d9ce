<?= $this->Html->meta('title', isset($meta_title) ? $meta_title : '', ['block' => true]) ?>
<?= $this->Html->meta('description', isset($meta_description) ? $meta_description : '', ['block' => true]) ?>
<?= $this->Html->meta('keywords', isset($meta_keywords) ? $meta_keywords : '', ['block' => true]) ?>


<?php $this->start('add_css'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('css/productCategoryListing.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/productViewPage.css') ?>">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"/>
<script src="<?= $this->Url->webroot('js/productViewPage.js') ?>"></script>
<style>
.mainContainer {
    display: flex;
    justify-content: center;
}

.mainContainerInner {
    width: 1133px;
    margin-right: -35px;
    border-radius: 10px;
    display: flex;
    height: max-content;
}

</style>
<?php $this->end(); ?>

    <section class=" my-2 my-lg-5 d-none d-lg-block">
        <div class="container">
            <nav aria-label="breadcrumb" class="">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="#"><?= __('Home') ?></a></li>
                    <li class="breadcrumb-item active" aria-current="page"><?= __('Contact Us') ?></li>
                </ol>
            </nav>
        </div>
    </section>
<div class="mainContainer">
    <div class="mainContainerInner">
        <div class="cms-content">
          
            <div class="content">
                <?= $page->content // Not escaping content as it contains HTML ?>
            </div>
        </div>
    </div>
</div>
