<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * OrderItemReview Entity
 *
 * @property int $id
 * @property int $customer_id
 * @property int $order_id
 * @property int $order_item_id
 * @property int $product_id
 * @property int $rating
 * @property string|null $review
 * @property string $status
 * @property string $publish_status
 * @property \Cake\I18n\FrozenTime $created
 * @property \Cake\I18n\FrozenTime $modified
 *
 * @property \App\Model\Entity\Customer $customer
 * @property \App\Model\Entity\Order $order
 * @property \App\Model\Entity\OrderItem $order_item
 * @property \App\Model\Entity\Product $product
 */
class OrderItemReview extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array<string, bool>
     */
    protected $_accessible = [
        'customer_id' => true,
        'order_id' => true,
        'order_item_id' => true,
        'product_id' => true,
        'rating' => true,
        'review' => true,
        'status' => true,
        'publish_status' => true,
        'created' => true,
        'modified' => true,
        'customer' => true,
        'order' => true,
        'order_item' => true,
        'product' => true,
    ];

    /**
     * Get star rating as HTML
     *
     * @return string
     */
    public function getStarRatingHtml()
    {
        $html = '';
        for ($i = 1; $i <= 5; $i++) {
            if ($i <= $this->rating) {
                $html .= '<i class="fas fa-star text-warning"></i>';
            } else {
                $html .= '<i class="far fa-star text-muted"></i>';
            }
        }
        return $html;
    }

    /**
     * Get formatted review date
     *
     * @return string
     */
    public function getFormattedDate()
    {
        return $this->created->format('M d, Y');
    }

    /**
     * Get review status badge
     *
     * @return array
     */
    public function getStatusBadge()
    {
        $badges = [
            'pending' => ['class' => 'bg-warning text-dark', 'label' => 'Pending Review'],
            'published' => ['class' => 'bg-success text-white', 'label' => 'Published'],
            'rejected' => ['class' => 'bg-danger text-white', 'label' => 'Rejected']
        ];

        return $badges[$this->publish_status] ?? ['class' => 'bg-secondary text-white', 'label' => 'Unknown'];
    }

    /**
     * Check if review is published
     *
     * @return bool
     */
    public function isPublished()
    {
        return $this->publish_status === 'published' && $this->status === 'Active';
    }

    /**
     * Get truncated review text
     *
     * @param int $length
     * @return string
     */
    public function getTruncatedReview($length = 100)
    {
        if (empty($this->review)) {
            return '';
        }

        if (strlen($this->review) <= $length) {
            return $this->review;
        }

        return substr($this->review, 0, $length) . '...';
    }
}
