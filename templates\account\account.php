<!DOCTYPE html>
<html lang="en">
<?php
        // Get session instance
        $session = $this->request->getSession();
        $currentLang = $session->read('siteSettings.language') ? strtolower($session->read('siteSettings.language')) : 'english';
        $country = $session->read('siteSettings.country') ? strtolower($session->read('siteSettings.country')) : 'Qatar';
?>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <?= $this->Html->meta('csrfToken', $this->request->getAttribute('csrfToken')); ?>
    <title><?= $session->read('siteSettings.site_title'); ?></title>
    <link rel="stylesheet" href="../../bundles/bootstrap/css/bootstrap.min.css" />
    <link rel="stylesheet" href="../../bundles/bootstrap/css/bootstrap.css" />
    <link rel="stylesheet" href="../../css/ozone.css" />
    <link rel="stylesheet" href="../../css/responsive.css" />
    <!-- For SVG format -->
    <link rel="icon" type="image/svg+xml" href="../../img/ozone/Ozonex-svg.svg">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Owl Stylesheets -->
    <link rel="stylesheet" href="../../css/owl.carousel.min.css">
    <link rel="stylesheet" href="../../css/owl.theme.default.min.css">
    <link href="https://cdn.jsdelivr.net/npm/nouislider@15.7.0/dist/nouislider.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <script src="../../js/jquery.min.js"></script>
    <script src="../../js/owl.carousel.js"></script>
    <style>
        .address_btn{
            color:#004225;
        }
        .accordion-collapse.collapse {
            transition: height 0.3s ease;
        }
        i .fa-star{
            color: #FF6200;
        }

        /* Review functionality styles */
        .star {
            cursor: pointer;
            transition: color 0.2s ease;
            margin: 0 2px;
            color: #ccc;
            display: inline-block;
            user-select: none;
        }

        .star:hover {
            color: #FF6200 !important;
        }

        .star i {
            pointer-events: none;
        }

        #singleStarRating .star,
        .bulk-star-rating .star {
            font-size: 1.5rem;
            margin: 0 3px;
        }
        .profile-pic-wrapper {
        position: relative;
        display: inline-block;
        width: 150px;
        height: 150px;
    }

    .profile-pic-wrapper img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 50%;
    }

    .profile-overlay-text {
        position: absolute;
        bottom: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5); /* Semi-transparent overlay */
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        opacity: 0;
        border-radius: 50%;
        transition: opacity 0.3s ease;
        font-size: 14px;
        font-weight: bold;
    }

    .profile-pic-wrapper:hover .profile-overlay-text {
        opacity: 1;
    }

    /* Password visibility toggle styles */
    span.show-password-icon-account {
        position: absolute;
        top: 50%;
        right: 15px;
        transform: translateY(-50%);
        color: #6c757d;
        cursor: pointer;
        z-index: 10;
        font-size: 16px;
        transition: color 0.3s ease;
    }
body.rtl span.show-password-icon-account {
        position: absolute;
        top: 50%;
            right: auto !important;
        left: 15px;
        transform: translateY(-50%);
        color: #6c757d;
        cursor: pointer;
        z-index: 10;
        font-size: 16px;
        transition: color 0.3s ease;
    }
    span.show-password-icon-account:hover {
        color: #495057;
    }

    .input-group.position-relative {
        position: relative;
    }

    /* Review text styling */
    .review-text-container {
        background-color: #f8f9fa;
        padding: 8px 12px;
        border-radius: 6px;
        border-left: 3px solid #007bff;
    }

    .review-content em {
        color: #495057;
        font-style: italic;
        font-size: 0.9rem;
        line-height: 1.4;
    }

    .show-more-link {
        font-size: 0.85rem;
        text-decoration: none;
        font-weight: 500;
        cursor: pointer;
    }

    .show-more-link:hover {
        text-decoration: underline;
    }
    </style>
</head>
<body>
  <!-- Leading Company Section -->
    <section class="myaccount">
        <div class="container">
            <div class="row py-5">
                <!-- Sidebar Navigation -->
                <div class="col-md-3 mb-4">
                    <div class="list-group">
                        <a class="list-group-item list-group-item-action active" data-bs-toggle="list" href="#profile"><?= __('My Profile') ?></a>
                        <a class="list-group-item list-group-item-action" data-bs-toggle="list" href="#billing-tab"><?= __('My Address') ?></a>
                        <a class="list-group-item list-group-item-action" data-bs-toggle="list" href="#orders-tab"><?= __('My Orders') ?></a>
                        <a class="list-group-item list-group-item-action" data-bs-toggle="list" href="#wishlist-tab"><?= __('My Wishlist') ?></a>
                        <a class="list-group-item list-group-item-action" data-bs-toggle="list" href="#changePassword"><?= __('Change Password') ?></a>
                        <a class="list-group-item list-group-item-action" href="<?= $this->Url->build(['controller' => 'Customer', 'action' => 'logout']) ?>"><?= __('Logout') ?></a>
                    </div>
                </div>

                <!-- Tab Content -->
                <div class="col-md-9">
                    <div class="tab-content">
                        <!-- Profile Tab -->
                        <div class="tab-pane fade show active" id="profile">
                            <div class="container ">
                                <h4><?= __('My Profile') ?></h4>
                                <hr />

                                <!-- Profile Form -->
                                    <div class="mb-3 text-center position-relative">
                                        <label for="profilePic" style="cursor: pointer;">
                                            <div class="profile-pic-wrapper">
                                                <img id="profileImagePreview" src="<?= $users->customer->profile_photo ?? '../assets/dp.png' ?>"
                                                    alt="Profile Picture" class="rounded-circle mb-3" width="150" height="150">
                                                <div class="profile-overlay-text"><?= __('Change Profile') ?></div>
                                            </div>
                                        </label>
                                    </div>       

                                    <form class="account-form" id="my_profile_form" method="post" action="<?= $this->Url->build(['controller' => 'Account','action' => 'updateMyAccount']); ?>" enctype="multipart/form-data">
                                    <?= $this->Form->hidden('_csrfToken', ['value' => $this->request->getAttribute('csrfToken')]); ?>
                                    
                                    <div class="row">
                                        <!-- Name -->
                                        <div class="col-md-6 mb-3">
                                            <label for="name" class="form-label"><?= __('Name') ?></label>
                                            <input type="text" class="form-control" id="name" name="name" value="<?= $users->first_name.' '.$users->last_name ?>" placeholder="<?= __('Enter your name') ?>" required>
                                        </div>

                                        <!-- Email -->
                                        <div class="col-md-6 mb-3">
                                            <label for="email" class="form-label"><?= __('Email Address') ?></label>
                                            <input type="email" class="form-control" id="email" name="email" value="<?= $users->email ?>" placeholder="<?= __('Enter your email') ?>" required>
                                        </div>
                                        <div class="text-center mb-3" id="emailErrorProfile" style="color:orange;display:none"></div>

                                        <!-- Phone -->
                                        <!-- Phone -->
                                        <div class="col-md-6 mb-3">
                                            <label for="phone" class="form-label"><?= __('Phone Number') ?></label>
                                            <div class="input-group">
                                                <?= $this->Form->control('country_code', [
                                                    'type' => 'select',
                                                    'label' => false,
                                                    'class' => 'form-select',
                                                    'options' => ['+974' => '+974', '+91' => '+91', '+966' => '+966'],
                                                    'style' => 'max-width: 120px;',
                                                    'value' => $users->country_code ?? '+974', // fallback
                                                    'templates' => ['inputContainer' => '{{content}}'], // remove wrapper div
                                                ]) ?>
                                                <?= $this->Form->control('phone', [
                                                    'type' => 'tel',
                                                    'label' => false,
                                                    'class' => 'form-control',
                                                    'placeholder' => __('Enter your phone number'),
                                                    'required' => true,
                                                    'value' => $users->mobile_no ?? '',
                                                    'templates' => ['inputContainer' => '{{content}}'],
                                                ]) ?>
                                            </div>
                                        </div>
                                                                                
                                        <div class="col-md-6 mb-3">
                                            <label for="dob" class="form-label"><?= __('Date of Birth') ?></label>
                                            <input type="date" class="form-control" id="dob" name="dob" value="<?= $users->dob ?>" placeholder="<?= __('Select your date of birth') ?>">
                                        </div>
                                        
                                        <div class="text-center mb-3" id="phoneErrorProfile" style="color:orange;display:none"></div>
                                        <!-- Gender -->
                                        <div class="col-md-6 mb-3">
                                            <label for="gender" class="form-label"><?= __('Gender') ?></label>
                                            <select class="form-select" id="gender" name="gender">
                                                <option value=""><?= __('Select your Gender') ?></option>
                                                <option value="M" <?= isset($users->customer->gender) && $users->customer->gender == 'M' ? 'selected' : '' ?>><?= __('Male') ?></option>
                                                <option value="F" <?= isset($users->customer->gender) && $users->customer->gender == 'F' ? 'selected' : '' ?>><?= __('Female') ?></option>
                                                <option value="O" <?= isset($users->customer->gender) && $users->customer->gender == 'O' ? 'selected' : '' ?>><?= __('Other') ?></option>
                                                <option value="P" <?= isset($users->customer->gender) && $users->customer->gender == 'P' ? 'selected' : '' ?>><?= __('Prefer no to say') ?></option>
                                            </select>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="email" class="form-label"><?= __('Email (Login)') ?></label>
                                            <input type="email" class="form-control" id="email" value="<?= $users->email ?>"
                                                readonly>
                                        </div>

                                        <div class="col-md-6 mb-3 d-none">
                                            <label for="profile_photo" class="form-label"><?= __('Profile Photo') ?></label>
                                            <input class="form-control" type="file" id="profile_photo" name="profile_photo" accept="image/*"
                                            onchange="previewProfileImage(event)">
                                        </div>
                                    </div>
                                    <button type="submit" class="btn btn-primary"><?= __('Save Changes') ?></button>
                                </form>
                            </div>
                        </div>

                        <!-- Add Address Modal -->
                            <div class="modal fade" id="addAddressModal" tabindex="-1" aria-labelledby="addAddressModalLabel"
                                aria-hidden="true">
                                <div class="modal-dialog modal-lg">
                                    <form class="modal-content" method="post" action="<?= $this->Url->build(['controller' => 'Account','action' => 'addAddress']); ?>" enctype="multipart/form-data">
                                    <?= $this->Form->hidden('_csrfToken', ['value' => $this->request->getAttribute('csrfToken')]); ?>

                                        <div class="modal-header">
                                            <h5 class="modal-title"><?= __('Add New Address') ?></h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"
                                                aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <div class="row">
                                                
                                                <div class="col-md-6 mb-3">
                                                    <label for="address_type" class="form-label"><?= __('Address Type') ?> <span class="text-danger">*</span></label>
                                                    <select class="form-select" id="address_type" name="address_type" required>
                                                        <option value=""><?= __('Select Address Type') ?></option>
                                                        <option value="Home"><?= __('Home') ?></option>
                                                        <option value="Work"><?= __('Work') ?></option>
                                                    </select>
                                                </div>

                                                <div class="col-6 mb-3">
                                                    <label for="name" class="form-label"><?= __('Name') ?> <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control" id="name" name="name" required>
                                                </div>

                                                <div class="col-6 mb-3">
                                                    <label for="house_no" class="form-label"><?= __('House No') ?> <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control" id="house_no" name="house_no" required>
                                                </div>

                                                <!-- Address 1 -->
                                                <div class="col-6 mb-3">
                                                    <label for="address1" class="form-label"><?= __('Address 1') ?> <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control" id="address1" name="address1" required>
                                                </div>

                                                <!-- Address 2 -->
                                                <div class="col-6 mb-4">
                                                    <label for="address2" class="form-label"><?= __('Address 2') ?> <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control" id="address2" name="address2" required>
                                                </div>

                                                <div class="col-6 mb-3">
                                                    <label for="landmark" class="form-label"><?= __('Landmark') ?> <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control" id="landmark" name="landmark" required>
                                                </div>

                                                <!-- Country -->
                                                <div class="col-md-6 mb-3">
                                                    <label for="country" class="form-label"><?= __('Country') ?> <span class="text-danger">*</span></label>
                                                    <select class="form-select" id="country" name="country" required>
                                                        <option value=""><?= __('Select your country') ?></option>
                                                        <?php foreach ($countries as $id => $name): ?>
                                                            <option value="<?= h($id) ?>" <?= (!empty($users->customer->country_id) && $users->customer->country_id == $id) ? 'selected' : '' ?>>
                                                                <?= h($name) ?>
                                                            </option>
                                                        <?php endforeach; ?>
                                                    </select>
                                                </div>

                                                <!-- State -->
                                                <div class="col-md-6 mb-3">
                                                    <label for="state" class="form-label"><?= __('State') ?> <span class="text-danger">*</span></label>
                                                    <select class="form-select" id="state" name="state" required>
                                                        
                                                    </select>
                                                </div>

                                                <!-- City -->
                                                <div class="col-md-6 mb-3">
                                                    <label for="city" class="form-label"><?= __('City') ?> <span class="text-danger">*</span></label>
                                                    <select class="form-select" id="city" name="city" required>
                                                        
                                                    </select>
                                                </div>

                                                <!-- Zip Code -->
                                                <div class="col-md-6 mb-3">
                                                    <label for="zip" class="form-label"><?= __('Zip Code') ?> <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control" id="zip" name="zip" required>
                                                </div>

                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="submit" class="btn btn-primary"><?= __('Submit') ?></button>
                                        </div>
                                    </form>
                                </div>
                            </div>

                        <!-- Edit Address Modal -->
                            <div class="modal fade" id="editAddressModal" tabindex="-1" aria-labelledby="editAddressModalLabel"
                                aria-hidden="true">
                                <div class="modal-dialog modal-lg">
                                    <form class="modal-content" method="post" action="<?= $this->Url->build(['controller' => 'Account','action' => 'editAddress']); ?>" enctype="multipart/form-data">
                                    <?= $this->Form->hidden('_csrfToken', ['value' => $this->request->getAttribute('csrfToken')]); ?>
                                    
                                        <div class="modal-header">
                                            <h5 class="modal-title"><?= __('Edit Address') ?></h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"
                                                aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <div class="row">
                                                <input type="hidden" id="address_id" name="address_id" value="">
                                                <div class="col-md-6 mb-3">
                                                    <label for="address_type" class="form-label"><?= __('Address Type') ?></label>
                                                    <select class="form-select" id="address_type" name="address_type">
                                                        <option value=""><?= __('Select Address Type') ?></option>
                                                        <option value="Home"><?= __('Home') ?></option>
                                                        <option value="Work"><?= __('Work') ?></option>
                                                    </select>
                                                </div>

                                                <div class="col-6 mb-3">
                                                    <label for="name" class="form-label"><?= __('Name') ?></label>
                                                    <input type="text" class="form-control" id="name" name="name" required>
                                                </div>
                                                
                                                <div class="col-6 mb-3">
                                                    <label for="house_no" class="form-label"><?= __('House No') ?></label>
                                                    <input type="text" class="form-control" id="house_no" name="house_no" required>
                                                </div>

                                                <!-- Address 1 -->
                                                <div class="col-6 mb-3">
                                                    <label for="address1" class="form-label"><?= __('Address 1') ?></label>
                                                    <input type="text" class="form-control" id="address1" name="address1">
                                                </div>

                                                <!-- Address 2 -->
                                                <div class="col-6 mb-4">
                                                    <label for="address2" class="form-label"><?= __('Address 2') ?></label>
                                                    <input type="text" class="form-control" id="address2" name="address2">
                                                </div>
                                                
                                                <div class="col-6 mb-3">
                                                    <label for="landmark" class="form-label"><?= __('Landmark') ?></label>
                                                    <input type="text" class="form-control" id="landmark" name="landmark">
                                                </div>
                                                
                                                <!-- Country -->
                                                <div class="col-md-6 mb-3">
                                                    <label for="country" class="form-label"><?= __('Country') ?></label>
                                                    <select class="form-select" id="country" name="country">
                                                        <option value=""><?= __('Select your country') ?></option>
                                                        <?php foreach ($countries as $id => $name): ?>
                                                            <option value="<?= h($id) ?>" <?= (!empty($users->customer->country_id) && $users->customer->country_id == $id) ? 'selected' : '' ?>>
                                                                <?= h($name) ?>
                                                            </option>
                                                        <?php endforeach; ?>
                                                    </select>
                                                </div>

                                                <!-- State -->
                                                <div class="col-md-6 mb-3">
                                                    <label for="state" class="form-label"><?= __('State') ?></label>
                                                    <select class="form-select" id="state" name="state" required>
                                                        
                                                    </select>
                                                </div>

                                                <!-- City -->
                                                <div class="col-md-6 mb-3">
                                                    <label for="city" class="form-label"><?= __('City') ?></label>
                                                    <select class="form-select" id="city" name="city" required>
                                                        
                                                    </select>
                                                </div>
     
                                                <!-- Zip Code -->
                                                <div class="col-md-6 mb-3">
                                                    <label for="zip" class="form-label"><?= __('Zip Code') ?></label>
                                                    <input type="text" class="form-control" id="zip" name="zip">
                                                </div>

                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="submit" class="btn btn-primary"><?= __('Submit') ?></button>
                                        </div>
                                    </form>
                                </div>
                            </div>

                        <!-- Delete Address Modal -->
                            <div class="modal fade" id="deleteAddressModal" tabindex="-1" aria-labelledby="deleteAddressModalLabel"
                                aria-hidden="true">
                                <div class="modal-dialog modal-lg">
                                    <form class="modal-content" method="post" action="<?= $this->Url->build(['controller' => 'Account','action' => 'deleteAddress']); ?>" enctype="multipart/form-data">
                                    <?= $this->Form->hidden('_csrfToken', ['value' => $this->request->getAttribute('csrfToken')]); ?>
                                    
                                        <div class="modal-header">
                                            <h5 class="modal-title"><?= __('Delete Address') ?></h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"
                                                aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <p><?= __('Are you sure you want to delete this address? This action cannot be undone.') ?></p>
                                            <input type="hidden" id="address_id" name="address_id" value="">
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?= __('Cancel') ?></button>
                                            <button type="submit" class="btn btn-danger"><?= __('Submit') ?></button>
                                        </div>
                                    </form>
                                </div>
                            </div>

                        <!-- Billing Information Tab -->
                        <div class="tab-pane fade" id="billing-tab">
                            <div class="container ">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h4><?= __('My Address') ?></h4>
                                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addAddressModal"><i class="fas fa-plus"></i> <?= __('Add Address') ?></button>
                                </div>

                                <hr />

                                <div class="row">

                                    <?php if (!empty($customerAddresses)): ?>
                                        <?php foreach ($customerAddresses as $k => $address): ?>
                                        <div class="col-md-4 mb-3">
                                            <div class="card">
                                                <div class="card-header justify-content-between align-items-center d-flex">
                                                    <small><?= $address->type ?></small>
                                                    <div class="float-end">
                                                        <input type="hidden" id="csrf-token-address" value="<?= h($this->request->getAttribute('csrfToken')) ?>">
                                                    </div>
                                                </div>
                                                <div class="card-body">
                                                    <h5 class="card-title"><?= $address->name ?></h5>
                                                    <small><?= $address->house_no . ' ' . $address->address_line1 ?></small><br>
                                                    <small><?= $address->address_line2 . ' ' . $address->landmark ?></small><br>
                                                    <small><?= $address->city->city_name . ' ' . $address->state->state_name . ' ' . $address->zipcode ?></small><br>
                                                    <small><?= $address->country->name ?></small><br><br>

                                                    <a class="btn btn-link text-decoration-none" onclick="editAddress(<?= $address->id ?>)" title="Edit">Edit</a>|
                                                    <a class="btn btn-link text-decoration-none" onclick="deleteAddress(<?= $address->id ?>)" title="Delete">Remove</a>
                                                </div>
                                            </div>
                                        </div>

                                        <?php endforeach; ?>
                                        <?php else: ?>
                                            <div class="alert alert-info"><?= __('No Orders Found.') ?></div>
                                    <?php endif; ?>     

                                </div>
                            </div>
                        </div>

                        <!-- Orders Tab -->
                        <div class="tab-pane fade" id="orders-tab">
                            <div class="container" style="min-height: 65vh;">
                                <h4><?= __('My Orders') ?></h4>
                                <hr />

                                <!-- Order List -->
                                <div class="accordion" id="orderAccordion">
                                    <?php if (!empty($orders)): ?>
                                        <?php foreach ($orders as $k => $order): ?>
                                            <div class="accordion-item mb-3">
                                                <h2 class="accordion-header" id="headingOrder<?= $order->id ?>">
                                                    <button class="accordion-button <?= $k === 0 ? '' : 'collapsed' ?>" type="button" data-bs-toggle="collapse"
                                                        data-bs-target="#collapseOrder<?= $order->id ?>" aria-expanded="<?= $k === 0 ? 'true' : 'false' ?>" aria-controls="collapseOrder<?= $order->id ?>"><b>
                                                        <?= __('Order') ?> #<?= h($order->order_number ?? $order->id) ?></b> 
                                                        <span class="status-badge status-<?= strtolower($order->status) ?> ms-2"><?= h($order->status) ?></span>
                                                    </button>
                                                </h2>
                                                <div id="collapseOrder<?= $order->id ?>" class="accordion-collapse collapse <?= $k === 0 ? 'show' : '' ?>"
                                                    aria-labelledby="headingOrder<?= $order->id ?>" data-bs-parent="#orderAccordion">
                                                    <div class="accordion-body">
                                                        <div class="row mb-3">
                                                            <div class="col-md-6">
                                                                <p><strong><?= __('Order Date:') ?></strong> <?= h($order->order_date ? date('Y-m-d', strtotime($order->order_date)) : '') ?></p>
                                                                <p><strong><?= __('Estimated Delivery:') ?></strong> <?= h($order->delivery_date ?? '-') ?></p>
                                                            </div>
                                                        </div>
                                                        <?php if (!empty($order->order_items)): ?>
                                                            <ul class="list-group mb-3">
                                                                <?php foreach ($order->order_items as $item): ?>
                                                                    <li class="list-group-item">
                                                                        <div class="d-flex justify-content-between mb-3">
                                                                            <div>
                                                                                <strong><a class="text-decoration-none text-dark" target="_blank" href="/product/<?= h($item->product->id) ?>"><?= h($item->product_name ?? 'Product') ?></a>
                                                                                <a class="text-dark" target="_blank" href="/product/<?= h($item->product->id) ?>"><i class="mx-2 fas fa-eye"></i></a></strong><br>
                                                                                <?= __('Quantity:') ?> <?= h($item->quantity) ?>
                                                                            </div>
                                                                            <div><strong><?= $this->Price->setPriceFormat($item->price) ?></strong></div>
                                                                        </div>
                                                                        <?php if ($order->status == 'Delivered'): ?>
                                                            <?php
                                                            // Check if this order item has been reviewed
                                                            $hasReview = false;
                                                            $reviewRating = 0;
                                                            $reviewText = '';
                                                            if (!empty($item->order_item_reviews)) {
                                                                foreach ($item->order_item_reviews as $review) {
                                                                    if ($review->customer_id == $users->customer->id) {
                                                                        $hasReview = true;
                                                                        $reviewRating = $review->rating;
                                                                        $reviewText = $review->review ?? '';
                                                                        break;
                                                                    }
                                                                }
                                                            }
                                                            ?>
                                                            <div class="mt-2">
                                                                <?php if ($hasReview): ?>
                                                                    <div>
                                                                        <div class="d-flex align-items-center">
                                                                            <strong class="me-2"><?= __('Your Review:') ?></strong>
                                                                            <div class="star-display">
                                                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                                                    <i class="<?= $i <= $reviewRating ? 'fas' : 'far' ?> fa-star text-warning"></i>
                                                                                <?php endfor; ?>
                                                                            </div>
                                                                            <!-- <span class="badge bg-success ms-2"><?= __('Reviewed') ?></span> -->
                                                                        </div>
                                                                        <?php if (!empty($reviewText)): ?>
                                                                            <div class="review-text-container mt-2">
                                                                                <div class="review-content" id="reviewContent_<?= $item->id ?>">
                                                                                    <?php if (strlen($reviewText) > 100): ?>
                                                                                        <span class="review-short">
                                                                                            <em>"<?= h(substr($reviewText, 0, 100)) ?>..."</em>
                                                                                        </span>
                                                                                        <span class="review-full d-none">
                                                                                            <em>"<?= h($reviewText) ?>"</em>
                                                                                        </span>
                                                                                        <a href="javascript:void(0)" class="text-primary ms-1 show-more-link"
                                                                                           onclick="toggleReviewText(<?= $item->id ?>)">
                                                                                            <?= __('Show More') ?>
                                                                                        </a>
                                                                                    <?php else: ?>
                                                                                        <span class="review-full">
                                                                                            <em>"<?= h($reviewText) ?>"</em>
                                                                                        </span>
                                                                                    <?php endif; ?>
                                                                                </div>
                                                                            </div>
                                                                        <?php endif; ?>
                                                                    </div>
                                                                <?php else: ?>
                                                                    <button type="button" class="btn btn-outline-primary btn-sm"
                                                                            onclick="openReviewModal(<?= h($order->id) ?>, <?= h($item->id) ?>, <?= h($item->product_id) ?>, '<?= h($item->product_name) ?>')">
                                                                        <i class="fas fa-star me-1"></i><?= __('Rate & Review') ?>
                                                                    </button>
                                                                <?php endif; ?>
                                                            </div>
                                                        <?php endif; ?>
                                                                    </li>
                                                                <?php endforeach; ?>
                                                            </ul>
                                                        <?php endif; ?>
                                                        <!-- Action Buttons -->
                                                        <div class="d-flex flex-wrap gap-2">
                                                            <?php if ($order->status == 'Pending' || $order->status == 'Processing' || $order->status == 'Approved'): ?>
                                                                <button class="btn btn-outline-danger btn-sm" onclick="cancelOrder(<?= $order->id ?>)"><?= __('Cancel Order') ?></button>
                                                            <?php endif; ?>
                                                            <?php
                                                                $returnDays = (int)($session->read('siteSettings.product_return_in_days') ?? 0);
                                                                ?>
                                                            <?php if ($order->status == 'Delivered' && strtotime($order->delivery_date) > strtotime("-{$returnDays} days")):  ?>
                                                                <button class="btn btn-primary btn-sm" onclick="returnOrder(<?= $order->id ?>)"><?= __('Request Return') ?> </button>
                                                            <?php endif; ?>
                                                            <a target="_blank" href="<?= $this->Url->build(['controller' => 'Account','action' => 'downloadInvoice', $order->id]); ?>" class="btn btn-outline-primary btn-sm"><?= __('Download Invoice (pdf)') ?></a>

                                                            <?php if ($order->status == 'Delivered'): ?>
                                                                <?php
                                                                // Check if there are any unreviewed items in this order
                                                                $hasUnreviewedItems = false;
                                                                foreach ($order->order_items as $item) {
                                                                    $itemReviewed = false;
                                                                    if (!empty($item->order_item_reviews)) {
                                                                        foreach ($item->order_item_reviews as $review) {
                                                                            if ($review->customer_id == $users->customer->id) {
                                                                                $itemReviewed = true;
                                                                                break;
                                                                            }
                                                                        }
                                                                    }
                                                                    if (!$itemReviewed) {
                                                                        $hasUnreviewedItems = true;
                                                                        break;
                                                                    }
                                                                }
                                                                ?>
                                                                <!-- <?php if ($hasUnreviewedItems): ?>
                                                                    <button type="button" class="btn btn-warning btn-sm" onclick="openBulkReviewModal(<?= h($order->id) ?>)">
                                                                        <i class="fas fa-star me-1"></i><?= __('Review All Items') ?>
                                                                    </button>
                                                                <?php endif; ?> -->
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <div class="alert alert-info"><?= __('No Orders Found.') ?></div>
                                    <?php endif; ?>
                                </div>
                                <!-- Load More Orders Button -->
                                <div class="text-center my-4">
                                    <button id="loadMoreOrdersBtn" class="btn btn-primary" style="display: none;">
                                        <span class="btn-text"><?= __('Load More Orders') ?></span>
                                        <span class="btn-loading d-none">
                                            <i class="fas fa-spinner fa-spin me-2"></i><?= __('Loading...')?>
                                        </span>
                                    </button>
                                    <div id="noMoreOrdersMsg" class="text-muted mt-2" style="display: none;">
                                        <?= __('No more orders to load') ?>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Include Single Review Modal Component -->
                            
                             <div class="modal fade" id="reviewModal" tabindex="-1" aria-labelledby="reviewModalLabel" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <form id="singleReviewForm">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="reviewModalLabel"><?= __('Rate and Review Product') ?></h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <div class="text-center mb-3">
                                                    <h6 id="productName"></h6>
                                                </div>
                                                <!-- Star Rating -->
                                                <div class="mb-3 text-center">
                                                    <label class="form-label d-block"><?= __('Your Rating') ?></label>
                                                    <div id="singleStarRating">
                                                        <span class="star fs-3" data-value="1"><i class="far fa-star"></i></span>
                                                        <span class="star fs-3" data-value="2"><i class="far fa-star"></i></span>
                                                        <span class="star fs-3" data-value="3"><i class="far fa-star"></i></span>
                                                        <span class="star fs-3" data-value="4"><i class="far fa-star"></i></span>
                                                        <span class="star fs-3" data-value="5"><i class="far fa-star"></i></span>
                                                    </div>
                                                </div>
                                                <!-- Review Textarea -->
                                                <div class="mb-3">
                                                    <label for="singleReviewText" class="form-label"><?= __('Your Review (Optional)') ?></label>
                                                    <textarea class="form-control" id="singleReviewText" rows="4" placeholder="<?= __('Write your review here') ?>..."></textarea>
                                                </div>
                                                <input type="hidden" id="singleOrderId" name="order_id">
                                                <input type="hidden" id="singleOrderItemId" name="order_item_id">
                                                <input type="hidden" id="singleProductId" name="product_id">
                                                <input type="hidden" id="singleSelectedRating" name="rating" value="0">
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?= __('Cancel') ?></button>
                                                <button type="submit" class="btn btn-primary"><?= __('Submit Review') ?></button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>


                            <!-- Bulk Review Modal -->
                            <div class="modal fade" id="bulkReviewModal" tabindex="-1" aria-labelledby="bulkReviewModalLabel" aria-hidden="true">
                                <div class="modal-dialog modal-lg">
                                    <div class="modal-content">
                                        <form id="bulkReviewForm">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="bulkReviewModalLabel"><?= __('Review All Order Items') ?></h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <div id="bulkReviewItems">
                                                    <!-- Items will be loaded here dynamically -->
                                                </div>
                                                <input type="hidden" id="bulkOrderId" name="order_id">
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?= __('Cancel') ?></button>
                                                <button type="submit" class="btn btn-primary"><?= __('Submit All Reviews') ?></button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <!-- Cancel Modal -->
                            <div class="modal fade" id="cancelModal" tabindex="-1" aria-labelledby="cancelModalLabel"
                                aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <form id="cancellationForm">
                                            <div class="modal-header">
                                                <h5 class="modal-title"><?= __('Cancel Order') ?></h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal"
                                                    aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <input type="hidden" id="cancel_order_id" name="order_id" value="">
                                                <div class="mb-3">
                                                    <label for="cancellation_reason" class="form-label"><?= __('Additional Comments (Optional)') ?></label>
                                                    <textarea class="form-control" id="cancellation_reason" name="reason" rows="3"
                                                        placeholder="<?= __('Please provide any additional details about your cancellation...') ?>"></textarea>
                                                </div>

                                                <div class="alert alert-warning">
                                                    <small><?= __('Note: This order can only be cancelled before shipment. Once submitted, your cancellation request will be reviewed by our team.') ?></small>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary"
                                                    data-bs-dismiss="modal"><?= __('Close') ?></button>
                                                <button type="submit" class="btn btn-danger"><?= __('Submit Cancellation Request') ?></button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <!-- Return Modal -->
                            <div class="modal fade" id="returnModal" tabindex="-1" aria-labelledby="returnModalLabel"
                                aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <form id="returnForm">
                                            <div class="modal-header">
                                                <h5 class="modal-title"><?= __('Request Return/Refund') ?></h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal"
                                                    aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <p><?= __('Request a return for this order. Please note that all returns must be within the allowed return period and are subject to admin approval.') ?></p>
                                                <div class="mb-3">
                                                    <label for="returnReason" class="form-label"><?= __('Reason for return') ?></label>
                                                    <textarea class="form-control" id="returnReason" rows="3"
                                                        required></textarea>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary"
                                                    data-bs-dismiss="modal"><?= __('Close') ?></button>
                                                <button type="submit" class="btn btn-warning"><?= __('Submit Request') ?></button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        <div id="scroll-sentinel"></div>
                        </div>
                        
                        <!-- wishlist Tab -->
                        <div class="tab-pane fade" id="wishlist-tab">
                            <div class="container">
                                <h4 class="mb-4"><?= __('My Wishlist') ?></h4>

                                <!-- Wishlist Grid -->
                                <div class="row row-cols-1 row-cols-md-3 g-4">
                                    <?php if (!empty($wishlistItems)): ?>
                                        <?php foreach ($wishlistItems as $wishlist): ?>
                                            <div class="col">
                                                <div class="card wishlist-card h-100">
                                                    <img src="<?= h($wishlist->product->product_image ?? '../../img/ozone/Meryl_Lounge-view.png') ?>" class="card-img-top"
                                                        alt="Product Image">
                                                    <div class="card-body d-flex flex-column">
                                                        <h5 class="card-title"><?= h($wishlist->product->product_name ?? 'Product Title') ?></h5>
                                                        <p class="card-text"><strong><?= __('Price:') ?></strong> <?= isset($wishlist->product->promotion_price) ? $this->Price->setPriceFormat($wishlist->product->promotion_price) : '' ?></p>
                                                        <!-- <p class="card-text"><strong><?= __('Added By:') ?></strong> <?= h($wishlist->product->added_by ?? 'Admin Name') ?></p> -->
                                                        <input type="hidden" id="csrf-token" value="<?= h($this->request->getAttribute('csrfToken')) ?>">
                                                        <div class="mt-auto">
                                                            <button class="btn btn-primary btn-sm w-100 mb-2" onclick="add_to_cart(<?= $wishlist->product->id ?>,<?= $users->customer->id ?>)"><?= __('Add to Cart') ?></button>
                                                            <button class="btn btn-outline-danger btn-sm w-100" onclick="remove_from_wishlist(<?= $wishlist->product->id ?>,<?= $users->customer->id ?>)"><?= __('Remove') ?></button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <div class="col">
                                            <div class="alert alert-info w-100"><?= __('No wishlist items found.') ?></div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Change Password Tab -->
                        <div class="tab-pane fade" id="changePassword">
                            <h4><?= __('Change Password') ?></h4>
                            <hr />
                            <form class="account-form" id="my_profile_form" method="post" action="<?= $this->Url->build(['controller' => 'Account','action' => 'changePassword']); ?>" enctype="multipart/form-data">
                                <?= $this->Form->hidden('_csrfToken', ['value' => $this->request->getAttribute('csrfToken')]); ?>

                                <div class="mb-3">
                                    <label for="currentPassword" class="form-label"><?= __('Current Password') ?></label>
                                    <div class="input-group position-relative">
                                        <input type="password" class="form-control" id="currentPassword" name="currentPassword"
                                            placeholder="<?= __('Enter current password') ?>" required>
                                        <span class="show-password-icon-account" onclick="toggleCurrentPasswordVisibility()">
                                            <i class="fa fa-eye"></i>
                                        </span>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="newPassword" class="form-label"><?= __('New Password') ?></label>
                                    <div class="input-group position-relative">
                                        <input type="password" class="form-control" id="newPassword" name="newPassword"
                                            placeholder="<?= __('Enter new password') ?>" required>
                                        <span class="show-password-icon-account" onclick="toggleNewPasswordVisibility()">
                                            <i class="fa fa-eye"></i>
                                        </span>
                                    </div>
                                </div>
                                <div class="text-center mb-3" id="passwordError" style="color:orange;display:none"></div>
                                <div class="mb-3">
                                    <label for="confirmPassword" class="form-label"><?= __('Confirm New Password') ?></label>
                                    <div class="input-group position-relative">
                                        <input type="password" class="form-control" id="confirmPassword" name="confirmPassword"
                                            placeholder="<?= __('Confirm new password') ?>" required>
                                        <span class="show-password-icon-account" onclick="toggleConfirmPasswordVisibility()">
                                            <i class="fa fa-eye"></i>
                                        </span>
                                    </div>
                                </div>
                                <div class="text-center mb-3 password_error" style="color:orange;display:none"><?= __('Confirm Password must be same as Password') ?></div>
                                <button id="update-password" type="submit" class="btn btn-success"><?= __('Update Password') ?></button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

<script>
    $(document).ready(function () {
        $('#addAddressModal').find('#country').on('change', function () {
            var countryId = $(this).val();
            if (countryId) {
                $.ajax({
                    url: '<?= $this->Url->build(['controller' => 'Account', 'action' => 'getStatesByCountry']) ?>',
                    type: 'POST',
                    data: { country_id: countryId },
                    headers: {
                        'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                    },
                    success: function (response) {
                        var $state = $('#addAddressModal').find('#state');
                        var $city = $('#addAddressModal').find('#city');

                        $state.empty().append('<option value=""><?= __('Select your State') ?></option>');
                        $city.empty().append('<option value=""><?= __('Select your City') ?></option>'); // reset cities

                        $.each(response.states, function (id, name) {
                            $state.append($('<option>', {
                                value: id,
                                text: name
                            }));
                        });
                    }
                });
            }
        });

        $('#editAddressModal').find('#country').on('change', function () {
            const countryId = $(this).val();
            const selectedStateId = $(this).data('state-id') || null;

            if (countryId) {
                $.ajax({
                    url: '<?= $this->Url->build(['controller' => 'Account', 'action' => 'getStatesByCountry']) ?>',
                    type: 'POST',
                    data: { country_id: countryId },
                    headers: {
                        'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                    },
                    success: function (response) {
                        var $state = $('#editAddressModal').find('#state');
                        var $city = $('#editAddressModal').find('#city');

                        $state.empty().append('<option value=""><?= __('Select your State') ?></option>');
                        $city.empty().append('<option value=""><?= __('Select your City') ?></option>');

                        $.each(response.states, function (id, name) {
                            $state.append($('<option>', {
                                value: id,
                                text: name
                            }));
                        });

                        if (selectedStateId) {
                            $state.val(selectedStateId).trigger('change');
                        }
                    }
                });
            }
        });

        $('#addAddressModal').find('#state').on('change', function () {
            var stateId = $(this).val();
            if (stateId) {
                $.ajax({
                    url: '<?= $this->Url->build(['controller' => 'Account', 'action' => 'getCitiesByState']) ?>',
                    type: 'POST',
                    data: { state_id: stateId },
                    headers: {
                        'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                    },
                    dataType: 'json',
                    success: function (response) {
                        var $city = $('#addAddressModal').find('#city');
                        $city.empty().append('<option value=""><?= __('Select your City') ?></option>');

                        $.each(response.cities, function (id, name) {
                            $city.append($('<option>', {
                                value: id,
                                text: name
                            }));
                        });
                    }
                });
            }
        });

        $('#editAddressModal').find('#state').on('change', function () {
            const stateId = $(this).val();
            const selectedCityId = $('#editAddressModal').find('#country').data('city-id') || null;

            if (stateId) {
                $.ajax({
                    url: '<?= $this->Url->build(['controller' => 'Account', 'action' => 'getCitiesByState']) ?>',
                    type: 'POST',
                    data: { state_id: stateId },
                    headers: {
                        'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                    },
                    dataType: 'json',
                    success: function (response) {
                        var $city = $('#editAddressModal').find('#city');
                        $city.empty().append('<option value=""><?= __('Select your City') ?></option>');

                        $.each(response.cities, function (id, name) {
                            $city.append($('<option>', {
                                value: id,
                                text: name
                            }));
                        });

                        if (selectedCityId) {
                            $city.val(selectedCityId);
                        }
                    }
                });
            }
        });
    });

    function cancelOrder(order_id) {
        // Set the order ID in the hidden field
        $('#cancel_order_id').val(order_id);

      

        // Show the modal
        $('#cancelModal').modal('show');

        // Remove any existing event handlers to prevent multiple bindings
        $('#cancellationForm').off('submit');

        $('#cancellationForm').on('submit', function (e) {
            e.preventDefault();

            // Validate form

            // Get form data
            const formData = {
                order_id: order_id,
                reason: $('#cancellation_reason').val(),
                product_id: 1 // This might need to be dynamic based on your needs
            };

            $.ajax({
                url: '<?= $this->Url->build(['controller' => 'Account', 'action' => 'cancelOrder']) ?>',
                type: 'POST',
                data: formData,
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(response) {
                    if (response.status == 'success') {
                        $('#cancelModal').modal('hide');
                        showToastMessage(response.message, 'success');
                        window.location.href = window.location.pathname + '#orders-tab';
                        window.location.reload();
                    } else {
                        showToastMessage(response.message, 'error');
                    }
                },
                error: function() {
                    showToastMessage('<?= __('An error occurred while cancelling the order.') ?>', 'error');
                }
            });
        });
    }

  

    function returnOrder(order_id) {
        $('#returnModal').modal('show');
        $('#returnForm').on('submit', function (e) {
            const returnReason = $('#returnReason').val();
            e.preventDefault();
            $.ajax({
                url: '<?= $this->Url->build(['controller' => 'Account', 'action' => 'returnOrder']) ?>',
                type: 'POST',
                data: { order_id: order_id, reason: returnReason },
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(response) {
                    if (response.status == 'success') { 
                        $('#returnModal').modal('hide');
                        showToastMessage(response.message, 'success');
                        window.location.href = window.location.pathname + '#orders-tab';
                        window.location.reload();
                    } else {
                        showToastMessage(response.message, 'error');
                    }
                },
                error: function() {
                    showToastMessage('An error occurred while returning the order.', 'error');
                }
            });
        });
    }

    function rateProduct(product_id) {
        $('#ratingModal').modal('show');
        $('#ratingForm').on('submit', function (e) {
            e.preventDefault();
            const rating = $('#selectedRating').val();
            const review = $('#reviewText').val();
            $.ajax({
                url: '<?= $this->Url->build(['controller' => 'Account', 'action' => 'rateProduct']) ?>',
                type: 'POST',
                data: { product_id: product_id, rating: rating, review: review },
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function (response) {
                    if (response.status == 'success') {
                        showToastMessage(response.message, 'success');
                        $('#ratingModal').modal('hide');
                    } else {
                        showToastMessage(response.message, 'error');
                    }
                },
                error: function (xhr, status, error) {
                    var response = JSON.parse(xhr.responseText);
                    toastr.warning(response.message || 'An error occurred', '', {
                        timeOut: 3000,
                        progressBar: true,
                    });
                }
            });
        });
    }

    // New Load More Orders functionality
    let ordersOffset = 5; // Start from 5 since first 5 are already loaded
    let isLoadingOrders = false;
    let hasMoreOrders = true;

    // Show load more button if there are more than 5 orders initially
    <?php if (count($orders) >= 5): ?>
        document.getElementById('loadMoreOrdersBtn').style.display = 'block';
    <?php endif; ?>

    // Load More Orders Button Click Handler
    document.getElementById('loadMoreOrdersBtn').addEventListener('click', function() {
        if (isLoadingOrders || !hasMoreOrders) return;

        isLoadingOrders = true;
        const btn = this;
        const btnText = btn.querySelector('.btn-text');
        const btnLoading = btn.querySelector('.btn-loading');

        // Show loading state
        btnText.classList.add('d-none');
        btnLoading.classList.remove('d-none');
        btn.disabled = true;

        // Make AJAX request
        fetch(`/account/load-more-orders?offset=${ordersOffset}`, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.text();
        })
        .then(html => {
            if (html.trim()) {
                // Parse and append new orders
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = html;
                const newOrders = tempDiv.children;

                const orderContainer = document.getElementById('orderAccordion');
                Array.from(newOrders).forEach(orderElement => {
                    orderContainer.appendChild(orderElement);
                });

                ordersOffset += 5;

                // Check if we got less than 5 orders (means no more orders)
                if (newOrders.length < 5) {
                    hasMoreOrders = false;
                    btn.style.display = 'none';
                    document.getElementById('noMoreOrdersMsg').style.display = 'block';
                }
            } else {
                // No more orders
                hasMoreOrders = false;
                btn.style.display = 'none';
                document.getElementById('noMoreOrdersMsg').style.display = 'block';
            }
        })
        .catch(error => {
            console.error('Error loading more orders:', error);
            alert('<?= __('Error loading more orders. Please try again.') ?>');
        })
        .finally(() => {
            // Reset button state
            isLoadingOrders = false;
            btnText.classList.remove('d-none');
            btnLoading.classList.add('d-none');
            btn.disabled = false;
        });
    });

    // Old star rating code removed - using new review system

    document.addEventListener("DOMContentLoaded", function () {
        const hash = window.location.hash;
        if (hash) {
            setTimeout(function () {
                const triggerEl = document.querySelector(`a[href="${hash}"]`);
                if (triggerEl) {
                    const tab = new bootstrap.Tab(triggerEl);
                    tab.show();
                }
            }, 200);
        }
    });

    function deleteAddress(address_id) {
        $('#deleteAddressModal').find('#address_id').val(address_id);
        $('#deleteAddressModal').modal('show');
    }

    function editAddress(address_id) {
        const csrfToken = $('#csrf-token-address').val();
        $.ajax({
            url: '<?= $this->Url->build(['controller' => 'Account', 'action' => 'getAddressById']) ?>',
            type: 'POST',
            data: { address_id: address_id, _csrfToken: csrfToken },
            success: function(response) {
                if (response.success) {
                    $('#editAddressModal').find('#address_id').val(response.address.id);
                    $('#editAddressModal').find('#name').val(response.address.name);
                    $('#editAddressModal').find('#address_type').val(response.address.type);
                    $('#editAddressModal').find('#house_no').val(response.address.house_no);
                    $('#editAddressModal').find('#address1').val(response.address.address_line1);
                    $('#editAddressModal').find('#address2').val(response.address.address_line2);
                    $('#editAddressModal').find('#landmark').val(response.address.landmark);
                    $('#editAddressModal').find('#zip').val(response.address.zipcode);
                    $('#editAddressModal').find('#country').val(response.address.country_id).data('state-id', response.address.state_id)
                    .data('city-id', response.address.city_id).trigger('change');
                    $('#editAddressModal').modal('show');
                } else {
                    showToastMessage(response.message, 'error');
                }
            },
            error: function() {
                showToastMessage('<?= __('An error occurred while removing the item.') ?>', 'error');
            }
        });
    }

    function add_to_cart(product_id, customer_id) {
        const csrfToken = $('#csrf-token').val();
        $.ajax({
            url: '<?= $this->Url->build(['controller' => 'Account', 'action' => 'addToCartFromWishlist']) ?>',
            type: 'POST',
            data: { product_id: product_id, customer_id: customer_id, _csrfToken: csrfToken },
            success: function(response) {
                if (response.success) {
                    showToastMessage(response.message, 'success');
                    window.location.hash = '#wishlist-tab';
                    location.reload();
                } else {
                    showToastMessage(response.message, 'error');
                }
            },
            error: function() {
                showToastMessage('<?= __('An error occurred while adding the item to the cart.') ?>', 'error');
            }
        });
    }

    function remove_from_wishlist(product_id, customer_id) {
        const csrfToken = $('#csrf-token').val();
        $.ajax({
            url: '<?= $this->Url->build(['controller' => 'Account', 'action' => 'removeWishlistData']) ?>',
            type: 'POST',
            data: { product_id: product_id, customer_id: customer_id, _csrfToken: csrfToken },
            success: function(response) {
                if (response.success) {
                    showToastMessage(response.message, 'success');
                    window.location.hash = '#wishlist-tab';
                    location.reload();
                } else {
                    showToastMessage(response.message, 'error');
                }
            },
            error: function() {
                showToastMessage('<?= __('An error occurred while removing the item.') ?>', 'error');
            }
        });
    }

    $(".profile-overlay-text").on("click", function() {
        $("#profile_photo").click();
    });

    function previewProfileImage(event) {
        const reader = new FileReader();
        reader.onload = function () {
            document.getElementById('profileImagePreview').src = reader.result;
        };
        reader.readAsDataURL(event.target.files[0]);
    }
    
    function confirmDelete() {
        if (confirm('<?= __('Are you sure you want to delete your account? This action cannot be undone.') ?>')) {
            document.getElementById('deleteAccountForm').submit();
        }
    }

    $(document).ready(function() {
        // Check for toast message from session
        <?php
        $toastMessage = $session->read('toast_message');
        if ($toastMessage):
            // Clear the message from session after reading
            $session->delete('toast_message');
        ?>
        showToastMessage('<?= h($toastMessage['message']) ?>', '<?= h($toastMessage['type']) ?>');
        <?php endif; ?>
    });

    function showToastMessage(message, type) {
        // Create message container with enhanced styling
        const messageContainer = $(`
            <div class="alert alert-dismissible fade show" style="
                margin-bottom: 10px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                border-radius: 8px;
                border: none;
                animation: slideInRight 0.3s ease-out;
            "></div>
        `);

        // Set message type and styling
        const alertClass = type === 'success' ? 'alert-success' :
                          type === 'error' ? 'alert-danger' :
                          type === 'warning' ? 'alert-warning' : 'alert-info';

        messageContainer.addClass(alertClass);

        // Add message content with icon
        const icon = type === 'success' ? 'fas fa-check-circle' :
                    type === 'error' ? 'fas fa-exclamation-circle' :
                    type === 'warning' ? 'fas fa-exclamation-triangle' : 'fas fa-info-circle';

        messageContainer.html(`
            <div class="alert-body d-flex align-items-center">
                <i class="${icon} me-2"></i>
                <span><b>${message}</b></span>
                <button type="button" class="btn-close ms-auto" onclick="$(this).closest('.alert').fadeOut(300, function(){ $(this).remove(); })"></button>
            </div>
        `);

        // Add to container and show with animation
        $('#toast-message-container').append(messageContainer);

        // Auto hide after 4 seconds
        setTimeout(() => {
            messageContainer.fadeOut(300, function() {
                $(this).remove();
            });
        }, 4000);
    }

    $(document).ready(function() {
        $('#changePassword #newPassword').on('keyup', function () {
                var password = $(this).val();
                var errorMsg = '';

                if (password.length < 8) {
                    $('#passwordError').text('<?= __('Password must be at least 8 characters long.') ?>').show();
                    $('#send_code_btn').prop('disabled',true);
                    $('#update-password').prop('disabled',true);
                } else if (!/[A-Z]/.test(password)) {
                    $('#passwordError').text('<?= __('Password must include at least one uppercase letter.') ?>').show();
                    $('#send_code_btn').prop('disabled',true);
                    $('#update-password').prop('disabled',true);
                } else if (!/[a-z]/.test(password)) {
                    $('#passwordError').text('<?= __('Password must include at least one lowercase letter.') ?>').show();
                    $('#send_code_btn').prop('disabled',true);
                    $('#update-password').prop('disabled',true);
                } else if (!/[0-9]/.test(password)) {
                    $('#passwordError').text('<?= __('Password must include at least one number.') ?>').show();
                    $('#send_code_btn').prop('disabled',true);
                    $('#update-password').prop('disabled',true);
                } else if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
                    $('#passwordError').text('<?= __('Password must include at least one special character.') ?>').show();
                    $('#send_code_btn').prop('disabled',true);
                    $('#update-password').prop('disabled',true);
                }
                else{
                    $('#passwordError').text('').hide()
                    $('#send_code_btn').prop('disabled',false);
                    $('#update-password').prop('disabled',false);
                }
        });

        $('#changePassword #confirmPassword').on('keyup', function() {
                const password = $('#changePassword #newPassword').val();
                const confirmPassword = $('#changePassword #confirmPassword').val();
                if (password !== confirmPassword) {
                    $('.password_error').show();
                    $('#update-password').prop('disabled', true);
                } else {
                    $('.password_error').hide();
                    $('#update-password').prop('disabled', false);
                }
        });

        $('#my_profile_form').find('#email').on('keyup', function () {
                var email = $(this).val().trim();
                var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

                if (email === '') {
                    $('#emailErrorProfile').text('<?= __('Email is required.') ?>').show();
                    $('.verify-btn').prop('disabled',true);
                    $('#send_code_btn').prop('disabled',true);
                    $('.btn-login').prop('disabled',true);
                } else if (!emailRegex.test(email)) {
                    $('#emailErrorProfile').text('<?= __('Please enter a valid email address.') ?>').show();
                    $('.verify-btn').prop('disabled',true);
                    $('#send_code_btn').prop('disabled',true);
                    $('.btn-login').prop('disabled',true);
                } else {
                    $('#emailErrorProfile').text('').show();
                    $('.verify-btn').prop('disabled',false);
                    $('#send_code_btn').prop('disabled',false);
                    $('.btn-login').prop('disabled',false);
                }
        });

        $('#my_profile_form').find('#phone').on('keyup', function () {
            var phone = $(this).val().trim();
            var phoneRegex = /^[6-9]\d{9}$/;

            if (phone === '') {
                $('#phoneErrorProfile').text('<?= __('Phone number is required.') ?>').show();
                $('.verify-btn').prop('disabled', true);
                $('#send_code_btn').prop('disabled', true);
                $('.btn-login').prop('disabled', true);
            }
            //  else if (!phoneRegex.test(phone)) {
            //     $('#phoneErrorProfile').text('<?= __('Enter a valid 10-digit phone number.') ?>').show();
            //     $('.verify-btn').prop('disabled', true);
            //     $('#send_code_btn').prop('disabled', true);
            //     $('.btn-login').prop('disabled', true);
            // } 
            else {
                $('#phoneErrorProfile').text('').hide();
                $('.verify-btn').prop('disabled', false);
                $('#send_code_btn').prop('disabled', false);
                $('.btn-login').prop('disabled', false);
            }
        });
    });

    // Password visibility toggle functions
    function toggleCurrentPasswordVisibility() {
        const passwordField = document.getElementById('currentPassword');
        const icon = event.target;
        const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordField.setAttribute('type', type);

        // Toggle icon
        if (type === 'text') {
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    }

    function toggleNewPasswordVisibility() {
        const passwordField = document.getElementById('newPassword');
        const icon = event.target;
        const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordField.setAttribute('type', type);

        // Toggle icon
        if (type === 'text') {
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    }

    // Review functionality
    function openReviewModal(orderId, orderItemId, productId, productName) {
        console.log('Opening review modal for:', {orderId, orderItemId, productId, productName}); // Debug log

        document.getElementById('singleOrderId').value = orderId;
        document.getElementById('singleOrderItemId').value = orderItemId;
        document.getElementById('singleProductId').value = productId;
        document.getElementById('productName').textContent = productName;
        document.getElementById('singleSelectedRating').value = '0';
        document.getElementById('singleReviewText').value = '';

        // Reset stars visually
        const stars = document.querySelectorAll('#singleStarRating .star');
        stars.forEach(star => {
            const icon = star.querySelector('i');
            icon.className = 'far fa-star';
            star.style.color = '#ccc';
        });

        const modal = new bootstrap.Modal(document.getElementById('reviewModal'));
        modal.show();

        // Re-initialize star ratings when modal is shown
        setTimeout(() => {
            initializeSingleStarRating();
        }, 100);
    }

    // Toggle review text function
    function toggleReviewText(itemId) {
        const reviewContent = document.getElementById('reviewContent_' + itemId);
        const shortText = reviewContent.querySelector('.review-short');
        const fullText = reviewContent.querySelector('.review-full');
        const showMoreLink = reviewContent.querySelector('.show-more-link');

        if (shortText && !shortText.classList.contains('d-none')) {
            // Show full text
            shortText.classList.add('d-none');
            fullText.classList.remove('d-none');
            showMoreLink.textContent = '<?= __('Show Less') ?>';
        } else {
            // Show short text
            shortText.classList.remove('d-none');
            fullText.classList.add('d-none');
            showMoreLink.textContent = '<?= __('Show More') ?>';
        }
    }

    // function openBulkReviewModal(orderId) {
    //     document.getElementById('bulkOrderId').value = orderId;

    //     // Load reviewable items
    //     fetch(`/account/get-reviewable-items?order_id=${orderId}`)
    //         .then(response => response.json())
    //         .then(data => {
    //             if (data.status === 'success') {
    //                 const container = document.getElementById('bulkReviewItems');
    //                 container.innerHTML = '';

    //                 data.data.items.forEach((item, index) => {
    //                     const itemHtml = `
    //                         <div class="border rounded p-3 mb-3">
    //                             <div class="d-flex align-items-center mb-3">
    //                                 <img src="${item.product_image || '/img/no-image.png'}" alt="${item.product_name}"
    //                                      class="me-3" style="width: 60px; height: 60px; object-fit: cover;">
    //                                 <div>
    //                                     <h6 class="mb-1">${item.product_name}</h6>
    //                                     <small class="text-muted"><?= __('Quantity:') ?> ${item.quantity}</small>
    //                                 </div>
    //                             </div>
    //                             <div class="mb-3 text-center">
    //                                 <label class="form-label d-block"><?= __('Rating') ?></label>
    //                                 <div class="bulk-star-rating" data-index="${index}">
    //                                     <span class="star fs-4" data-value="1"><i class="far fa-star"></i></span>
    //                                     <span class="star fs-4" data-value="2"><i class="far fa-star"></i></span>
    //                                     <span class="star fs-4" data-value="3"><i class="far fa-star"></i></span>
    //                                     <span class="star fs-4" data-value="4"><i class="far fa-star"></i></span>
    //                                     <span class="star fs-4" data-value="5"><i class="far fa-star"></i></span>
    //                                 </div>
    //                             </div>
    //                             <div class="mb-3">
    //                                 <label class="form-label"><?= __('Review (Optional)') ?></label>
    //                                 <textarea class="form-control bulk-review-text" rows="3"
    //                                           placeholder="<?= __('Write your review here') ?>..."></textarea>
    //                             </div>
    //                             <input type="hidden" class="bulk-order-item-id" value="${item.order_item_id}">
    //                             <input type="hidden" class="bulk-product-id" value="${item.product_id}">
    //                             <input type="hidden" class="bulk-rating" value="0">
    //                         </div>
    //                     `;
    //                     container.innerHTML += itemHtml;
    //                 });

    //                 // Initialize star ratings for bulk items
    //                 initializeBulkStarRatings();

    //                 const modal = new bootstrap.Modal(document.getElementById('bulkReviewModal'));
    //                 modal.show();
    //             } else {
    //                 showToastMessage(data.message, 'error');
    //             }
    //         })
    //         .catch(error => {
    //             console.error('Error:', error);
    //             showToastMessage('<?= __('Failed to load reviewable items') ?>', 'error');
    //         });
    // }

    function toggleConfirmPasswordVisibility() {
        const passwordField = document.getElementById('confirmPassword');
        const icon = event.target;
        const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordField.setAttribute('type', type);

        // Toggle icon
        if (type === 'text') {
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    }
</script>

<script src="../../javascript/ozone.js"></script>
    <!-- Bootstrap JS -->
<script src="../../bundles/bootstrap/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>


<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"
        integrity="sha512-v2CJ7UaYy4JwqLDIrZUI/4hqeoQieOmAZNXBeQyjo21dadnwR+8ZaIJVT8EE2iyI61OV8e6M8PP2/4hpQINQ/g=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script src="https://cdn.jsdelivr.net/npm/nouislider@15.7.0/dist/nouislider.min.js"></script>
    <!-- vendors -->
<script src="../../carousel/assets/vendors/highlight.js"></script>
<script src="../../carousel/assets/js/app.js"></script>
<script src="https://cdn.jsdelivr.net/npm/nouislider@15.7.0/dist/nouislider.min.js"></script>

<script>
    // Initialize star ratings
    function initializeSingleStarRating() {
        const stars = document.querySelectorAll('#singleStarRating .star');
        stars.forEach((star, index) => {
            star.addEventListener('click', function() {
                const rating = parseInt(this.dataset.value);
                console.log('Star clicked, rating:', rating); // Debug log
                document.getElementById('singleSelectedRating').value = rating;

                // Update visual stars
                stars.forEach((s, starIndex) => {
                    const icon = s.querySelector('i');
                    if (starIndex < rating) {
                        icon.className = 'fas fa-star';
                        s.style.color = '#FF6200';
                    } else {
                        icon.className = 'far fa-star';
                        s.style.color = '#ccc';
                    }
                });

                console.log('Rating input value set to:', document.getElementById('singleSelectedRating').value); // Debug log
            });

            star.addEventListener('mouseenter', function() {
                const rating = parseInt(this.dataset.value);
                stars.forEach((s, starIndex) => {
                    const icon = s.querySelector('i');
                    if (starIndex < rating) {
                        icon.className = 'fas fa-star';
                        s.style.color = '#FF6200';
                    } else {
                        icon.className = 'far fa-star';
                        s.style.color = '#ccc';
                    }
                });
            });

            star.addEventListener('mouseleave', function() {
                // Reset to selected rating on mouse leave
                const selectedRating = parseInt(document.getElementById('singleSelectedRating').value) || 0;
                stars.forEach((s, starIndex) => {
                    const icon = s.querySelector('i');
                    if (starIndex < selectedRating) {
                        icon.className = 'fas fa-star';
                        s.style.color = '#FF6200';
                    } else {
                        icon.className = 'far fa-star';
                        s.style.color = '#ccc';
                    }
                });
            });
        });
    }

    function initializeBulkStarRatings() {
        const ratingContainers = document.querySelectorAll('.bulk-star-rating');
        ratingContainers.forEach(container => {
            const stars = container.querySelectorAll('.star');
            const index = container.dataset.index;

            stars.forEach((star, starIndex) => {
                star.addEventListener('click', function() {
                    const rating = parseInt(this.dataset.value);
                    const ratingInput = container.parentElement.parentElement.querySelector('.bulk-rating');
                    ratingInput.value = rating;

                    // Update visual stars for this container
                    stars.forEach((s, sIndex) => {
                        const icon = s.querySelector('i');
                        if (sIndex < rating) {
                            icon.className = 'fas fa-star';
                            s.style.color = '#FF6200';
                        } else {
                            icon.className = 'far fa-star';
                            s.style.color = '#ccc';
                        }
                    });
                });

                star.addEventListener('mouseenter', function() {
                    const rating = parseInt(this.dataset.value);
                    stars.forEach((s, sIndex) => {
                        const icon = s.querySelector('i');
                        if (sIndex < rating) {
                            icon.className = 'fas fa-star';
                            s.style.color = '#FF6200';
                        } else {
                            icon.className = 'far fa-star';
                            s.style.color = '#ccc';
                        }
                    });
                });

                star.addEventListener('mouseleave', function() {
                    // Reset to selected rating on mouse leave
                    const ratingInput = container.parentElement.parentElement.querySelector('.bulk-rating');
                    const selectedRating = parseInt(ratingInput.value) || 0;
                    stars.forEach((s, sIndex) => {
                        const icon = s.querySelector('i');
                        if (sIndex < selectedRating) {
                            icon.className = 'fas fa-star';
                            s.style.color = '#FF6200';
                        } else {
                            icon.className = 'far fa-star';
                            s.style.color = '#ccc';
                        }
                    });
                });
            });
        });
    }

    // Form submissions
    document.getElementById('singleReviewForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const rating = parseInt(document.getElementById('singleSelectedRating').value);
        console.log('Selected rating:', rating); // Debug log

        if (!rating || rating < 1 || rating > 5) {
            showToastMessage('<?= __('Please select a rating') ?>', 'error');
            return;
        }

        const formData = {
            order_id: document.getElementById('singleOrderId').value,
            reviews: [{
                order_item_id: document.getElementById('singleOrderItemId').value,
                product_id: document.getElementById('singleProductId').value,
                rating: rating,
                review: document.getElementById('singleReviewText').value
            }]
        };

        console.log('Form data:', formData); // Debug log
        submitReview(formData, 'reviewModal');
    });

    document.getElementById('bulkReviewForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const reviews = [];
        const items = document.querySelectorAll('#bulkReviewItems > div');
        let hasValidReview = false;

        items.forEach((item, index) => {
            const ratingInput = item.querySelector('.bulk-rating');
            const rating = parseInt(ratingInput.value);

            console.log(`Item ${index} rating:`, rating); // Debug log

            if (rating && rating >= 1 && rating <= 5) {
                hasValidReview = true;
                reviews.push({
                    order_item_id: item.querySelector('.bulk-order-item-id').value,
                    product_id: item.querySelector('.bulk-product-id').value,
                    rating: rating,
                    review: item.querySelector('.bulk-review-text').value
                });
            }
        });

        console.log('Valid reviews:', reviews); // Debug log

        if (!hasValidReview) {
            showToastMessage('<?= __('Please rate at least one item') ?>', 'error');
            return;
        }

        const formData = {
            order_id: document.getElementById('bulkOrderId').value,
            reviews: reviews
        };

        console.log('Bulk form data:', formData); // Debug log
        submitReview(formData, 'bulkReviewModal');
    });

    function submitReview(formData, modalId) {
        fetch('/account/add-review', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': document.querySelector('meta[name="csrfToken"]').getAttribute('content')
            },
            body: JSON.stringify(formData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                showToastMessage(data.message, 'success');
                bootstrap.Modal.getInstance(document.getElementById(modalId)).hide();
                // Reload page to show updated reviews
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showToastMessage(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToastMessage('<?= __('Failed to submit review') ?>', 'error');
        });
    }

    // Initialize star ratings when page loads
    document.addEventListener('DOMContentLoaded', function() {
        initializeSingleStarRating();
    });
</script>

</body>

</html>