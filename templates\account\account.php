<!DOCTYPE html>
<html lang="en">
<?php
        // Get session instance
        $session = $this->request->getSession();
        $currentLang = $session->read('siteSettings.language') ? strtolower($session->read('siteSettings.language')) : 'english';
        $country = $session->read('siteSettings.country') ? strtolower($session->read('siteSettings.country')) : 'Qatar';
?>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <?= $this->Html->meta('csrfToken', $this->request->getAttribute('csrfToken')); ?>
    <title><?= $session->read('siteSettings.site_title'); ?></title>
    <link rel="stylesheet" href="../../bundles/bootstrap/css/bootstrap.min.css" />
    <link rel="stylesheet" href="../../bundles/bootstrap/css/bootstrap.css" />
    <link rel="stylesheet" href="../../css/ozone.css" />
    <link rel="stylesheet" href="../../css/responsive.css" />
    <!-- For SVG format -->
    <link rel="icon" type="image/svg+xml" href="../../img/ozone/Ozonex-svg.svg">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Owl Stylesheets -->
    <link rel="stylesheet" href="../../css/owl.carousel.min.css">
    <link rel="stylesheet" href="../../css/owl.theme.default.min.css">
    <link href="https://cdn.jsdelivr.net/npm/nouislider@15.7.0/dist/nouislider.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <script src="../../js/jquery.min.js"></script>
    <script src="../../js/owl.carousel.js"></script>
    <style>
        .address_btn{
            color:#004225;
        }
        .accordion-collapse.collapse {
            transition: height 0.3s ease;
        }
        i .fa-star{
            color: #FF6200;
        }
        .profile-pic-wrapper {
        position: relative;
        display: inline-block;
        width: 150px;
        height: 150px;
    }

    .profile-pic-wrapper img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 50%;
    }

    .profile-overlay-text {
        position: absolute;
        bottom: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5); /* Semi-transparent overlay */
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        opacity: 0;
        border-radius: 50%;
        transition: opacity 0.3s ease;
        font-size: 14px;
        font-weight: bold;
    }

    .profile-pic-wrapper:hover .profile-overlay-text {
        opacity: 1;
    }

    /* Password visibility toggle styles */
    span.show-password-icon-account {
        position: absolute;
        top: 50%;
        right: 15px;
        transform: translateY(-50%);
        color: #6c757d;
        cursor: pointer;
        z-index: 10;
        font-size: 16px;
        transition: color 0.3s ease;
    }

    span.show-password-icon-account:hover {
        color: #495057;
    }

    .input-group.position-relative {
        position: relative;
    }
    </style>
</head>
<body>
  <!-- Leading Company Section -->
    <section class="myaccount">
        <div class="container">
            <div class="row py-5">
                <!-- Sidebar Navigation -->
                <div class="col-md-3 mb-4">
                    <div class="list-group">
                        <a class="list-group-item list-group-item-action active" data-bs-toggle="list" href="#profile"><?= __('My Profile') ?></a>
                        <a class="list-group-item list-group-item-action" data-bs-toggle="list" href="#billing-tab"><?= __('My Address') ?></a>
                        <a class="list-group-item list-group-item-action" data-bs-toggle="list" href="#orders-tab"><?= __('My Orders') ?></a>
                        <a class="list-group-item list-group-item-action" data-bs-toggle="list" href="#wishlist-tab"><?= __('My Wishlist') ?></a>
                        <a class="list-group-item list-group-item-action" data-bs-toggle="list" href="#changePassword"><?= __('Change Password') ?></a>
                        <a class="list-group-item list-group-item-action" href="<?= $this->Url->build(['controller' => 'Customer', 'action' => 'logout']) ?>"><?= __('Logout') ?></a>
                    </div>
                </div>

                <!-- Tab Content -->
                <div class="col-md-9">
                    <div class="tab-content">
                        <!-- Profile Tab -->
                        <div class="tab-pane fade show active" id="profile">
                            <div class="container ">
                                <h4><?= __('My Profile') ?></h4>
                                <hr />

                                <!-- Profile Form -->
                                    <div class="mb-3 text-center position-relative">
                                        <label for="profilePic" style="cursor: pointer;">
                                            <div class="profile-pic-wrapper">
                                                <img id="profileImagePreview" src="<?= $users->customer->profile_photo ?? '../assets/dp.png' ?>"
                                                    alt="Profile Picture" class="rounded-circle mb-3" width="150" height="150">
                                                <div class="profile-overlay-text"><?= __('Change Profile') ?></div>
                                            </div>
                                        </label>
                                    </div>       

                                    <form class="account-form" id="my_profile_form" method="post" action="<?= $this->Url->build(['controller' => 'Account','action' => 'updateMyAccount']); ?>" enctype="multipart/form-data">
                                    <?= $this->Form->hidden('_csrfToken', ['value' => $this->request->getAttribute('csrfToken')]); ?>
                                    
                                    <div class="row">
                                        <!-- Name -->
                                        <div class="col-md-6 mb-3">
                                            <label for="name" class="form-label"><?= __('Name') ?></label>
                                            <input type="text" class="form-control" id="name" name="name" value="<?= $users->first_name.' '.$users->last_name ?>" placeholder="<?= __('Enter your name') ?>" required>
                                        </div>

                                        <!-- Email -->
                                        <div class="col-md-6 mb-3">
                                            <label for="email" class="form-label"><?= __('Email Address') ?></label>
                                            <input type="email" class="form-control" id="email" name="email" value="<?= $users->email ?>" placeholder="<?= __('Enter your email') ?>" required>
                                        </div>
                                        <div class="text-center mb-3" id="emailErrorProfile" style="color:orange;display:none"></div>

                                        <!-- Phone -->
                                        <div class="col-md-6 mb-3">
                                            <label for="phone" class="form-label"><?= __('Phone Number') ?></label>
                                            <input type="tel" class="form-control" id="phone" name="phone" value="<?= $users->mobile_no ?>" placeholder="<?= __('Enter your phone number') ?>" required>
                                        </div>                                          
                                        <div class="col-md-6 mb-3">
                                            <label for="dob" class="form-label"><?= __('Date of Birth') ?></label>
                                            <input type="date" class="form-control" id="dob" name="dob" value="<?= $users->dob ?>" placeholder="<?= __('Select your date of birth') ?>">
                                        </div>
                                        
                                        <div class="text-center mb-3" id="phoneErrorProfile" style="color:orange;display:none"></div>
                                        <!-- Gender -->
                                        <div class="col-md-6 mb-3">
                                            <label for="gender" class="form-label"><?= __('Gender') ?></label>
                                            <select class="form-select" id="gender" name="gender">
                                                <option value=""><?= __('Select your Gender') ?></option>
                                                <option value="M" <?= isset($users->customer->gender) && $users->customer->gender == 'M' ? 'selected' : '' ?>><?= __('Male') ?></option>
                                                <option value="F" <?= isset($users->customer->gender) && $users->customer->gender == 'F' ? 'selected' : '' ?>><?= __('Female') ?></option>
                                                <option value="O" <?= isset($users->customer->gender) && $users->customer->gender == 'O' ? 'selected' : '' ?>><?= __('Other') ?></option>
                                                <option value="P" <?= isset($users->customer->gender) && $users->customer->gender == 'P' ? 'selected' : '' ?>><?= __('Prefer no to say') ?></option>
                                            </select>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="email" class="form-label"><?= __('Email (Login)') ?></label>
                                            <input type="email" class="form-control" id="email" value="<?= $users->email ?>"
                                                readonly>
                                        </div>

                                        <div class="col-md-6 mb-3 d-none">
                                            <label for="profile_photo" class="form-label"><?= __('Profile Photo') ?></label>
                                            <input class="form-control" type="file" id="profile_photo" name="profile_photo" accept="image/*"
                                            onchange="previewProfileImage(event)">
                                        </div>
                                    </div>
                                    <button type="submit" class="btn btn-primary"><?= __('Save Changes') ?></button>
                                </form>
                            </div>
                        </div>

                        <!-- Add Address Modal -->
                            <div class="modal fade" id="addAddressModal" tabindex="-1" aria-labelledby="addAddressModalLabel"
                                aria-hidden="true">
                                <div class="modal-dialog modal-lg">
                                    <form class="modal-content" method="post" action="<?= $this->Url->build(['controller' => 'Account','action' => 'addAddress']); ?>" enctype="multipart/form-data">
                                    <?= $this->Form->hidden('_csrfToken', ['value' => $this->request->getAttribute('csrfToken')]); ?>

                                        <div class="modal-header">
                                            <h5 class="modal-title"><?= __('Add New Address') ?></h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"
                                                aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <div class="row">
                                                
                                                <div class="col-md-6 mb-3">
                                                    <label for="address_type" class="form-label"><?= __('Address Type') ?> <span class="text-danger">*</span></label>
                                                    <select class="form-select" id="address_type" name="address_type" required>
                                                        <option value=""><?= __('Select Address Type') ?></option>
                                                        <option value="Home"><?= __('Home') ?></option>
                                                        <option value="Work"><?= __('Work') ?></option>
                                                    </select>
                                                </div>

                                                <div class="col-6 mb-3">
                                                    <label for="name" class="form-label"><?= __('Name') ?> <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control" id="name" name="name" required>
                                                </div>

                                                <div class="col-6 mb-3">
                                                    <label for="house_no" class="form-label"><?= __('House No') ?> <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control" id="house_no" name="house_no" required>
                                                </div>

                                                <!-- Address 1 -->
                                                <div class="col-6 mb-3">
                                                    <label for="address1" class="form-label"><?= __('Address 1') ?> <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control" id="address1" name="address1" required>
                                                </div>

                                                <!-- Address 2 -->
                                                <div class="col-6 mb-4">
                                                    <label for="address2" class="form-label"><?= __('Address 2') ?> <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control" id="address2" name="address2" required>
                                                </div>

                                                <div class="col-6 mb-3">
                                                    <label for="landmark" class="form-label"><?= __('Landmark') ?> <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control" id="landmark" name="landmark" required>
                                                </div>

                                                <!-- Country -->
                                                <div class="col-md-6 mb-3">
                                                    <label for="country" class="form-label"><?= __('Country') ?> <span class="text-danger">*</span></label>
                                                    <select class="form-select" id="country" name="country" required>
                                                        <option value=""><?= __('Select your country') ?></option>
                                                        <?php foreach ($countries as $id => $name): ?>
                                                            <option value="<?= h($id) ?>" <?= (!empty($users->customer->country_id) && $users->customer->country_id == $id) ? 'selected' : '' ?>>
                                                                <?= h($name) ?>
                                                            </option>
                                                        <?php endforeach; ?>
                                                    </select>
                                                </div>

                                                <!-- State -->
                                                <div class="col-md-6 mb-3">
                                                    <label for="state" class="form-label"><?= __('State') ?> <span class="text-danger">*</span></label>
                                                    <select class="form-select" id="state" name="state" required>
                                                        
                                                    </select>
                                                </div>

                                                <!-- City -->
                                                <div class="col-md-6 mb-3">
                                                    <label for="city" class="form-label"><?= __('City') ?> <span class="text-danger">*</span></label>
                                                    <select class="form-select" id="city" name="city" required>
                                                        
                                                    </select>
                                                </div>

                                                <!-- Zip Code -->
                                                <div class="col-md-6 mb-3">
                                                    <label for="zip" class="form-label"><?= __('Zip Code') ?> <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control" id="zip" name="zip" required>
                                                </div>

                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="submit" class="btn btn-primary"><?= __('Submit') ?></button>
                                        </div>
                                    </form>
                                </div>
                            </div>

                        <!-- Edit Address Modal -->
                            <div class="modal fade" id="editAddressModal" tabindex="-1" aria-labelledby="editAddressModalLabel"
                                aria-hidden="true">
                                <div class="modal-dialog modal-lg">
                                    <form class="modal-content" method="post" action="<?= $this->Url->build(['controller' => 'Account','action' => 'editAddress']); ?>" enctype="multipart/form-data">
                                    <?= $this->Form->hidden('_csrfToken', ['value' => $this->request->getAttribute('csrfToken')]); ?>
                                    
                                        <div class="modal-header">
                                            <h5 class="modal-title"><?= __('Edit Address') ?></h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"
                                                aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <div class="row">
                                                <input type="hidden" id="address_id" name="address_id" value="">
                                                <div class="col-md-6 mb-3">
                                                    <label for="address_type" class="form-label"><?= __('Address Type') ?></label>
                                                    <select class="form-select" id="address_type" name="address_type">
                                                        <option value=""><?= __('Select Address Type') ?></option>
                                                        <option value="Home"><?= __('Home') ?></option>
                                                        <option value="Work"><?= __('Work') ?></option>
                                                    </select>
                                                </div>

                                                <div class="col-6 mb-3">
                                                    <label for="name" class="form-label"><?= __('Name') ?></label>
                                                    <input type="text" class="form-control" id="name" name="name" required>
                                                </div>
                                                
                                                <div class="col-6 mb-3">
                                                    <label for="house_no" class="form-label"><?= __('House No') ?></label>
                                                    <input type="text" class="form-control" id="house_no" name="house_no" required>
                                                </div>

                                                <!-- Address 1 -->
                                                <div class="col-6 mb-3">
                                                    <label for="address1" class="form-label"><?= __('Address 1') ?></label>
                                                    <input type="text" class="form-control" id="address1" name="address1">
                                                </div>

                                                <!-- Address 2 -->
                                                <div class="col-6 mb-4">
                                                    <label for="address2" class="form-label"><?= __('Address 2') ?></label>
                                                    <input type="text" class="form-control" id="address2" name="address2">
                                                </div>
                                                
                                                <div class="col-6 mb-3">
                                                    <label for="landmark" class="form-label"><?= __('Landmark') ?></label>
                                                    <input type="text" class="form-control" id="landmark" name="landmark">
                                                </div>
                                                
                                                <!-- Country -->
                                                <div class="col-md-6 mb-3">
                                                    <label for="country" class="form-label"><?= __('Country') ?></label>
                                                    <select class="form-select" id="country" name="country">
                                                        <option value=""><?= __('Select your country') ?></option>
                                                        <?php foreach ($countries as $id => $name): ?>
                                                            <option value="<?= h($id) ?>" <?= (!empty($users->customer->country_id) && $users->customer->country_id == $id) ? 'selected' : '' ?>>
                                                                <?= h($name) ?>
                                                            </option>
                                                        <?php endforeach; ?>
                                                    </select>
                                                </div>

                                                <!-- State -->
                                                <div class="col-md-6 mb-3">
                                                    <label for="state" class="form-label"><?= __('State') ?></label>
                                                    <select class="form-select" id="state" name="state" required>
                                                        
                                                    </select>
                                                </div>

                                                <!-- City -->
                                                <div class="col-md-6 mb-3">
                                                    <label for="city" class="form-label"><?= __('City') ?></label>
                                                    <select class="form-select" id="city" name="city" required>
                                                        
                                                    </select>
                                                </div>
     
                                                <!-- Zip Code -->
                                                <div class="col-md-6 mb-3">
                                                    <label for="zip" class="form-label"><?= __('Zip Code') ?></label>
                                                    <input type="text" class="form-control" id="zip" name="zip">
                                                </div>

                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="submit" class="btn btn-primary"><?= __('Submit') ?></button>
                                        </div>
                                    </form>
                                </div>
                            </div>

                        <!-- Delete Address Modal -->
                            <div class="modal fade" id="deleteAddressModal" tabindex="-1" aria-labelledby="deleteAddressModalLabel"
                                aria-hidden="true">
                                <div class="modal-dialog modal-lg">
                                    <form class="modal-content" method="post" action="<?= $this->Url->build(['controller' => 'Account','action' => 'deleteAddress']); ?>" enctype="multipart/form-data">
                                    <?= $this->Form->hidden('_csrfToken', ['value' => $this->request->getAttribute('csrfToken')]); ?>
                                    
                                        <div class="modal-header">
                                            <h5 class="modal-title"><?= __('Delete Address') ?></h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"
                                                aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <p><?= __('Are you sure you want to delete this address? This action cannot be undone.') ?></p>
                                            <input type="hidden" id="address_id" name="address_id" value="">
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?= __('Cancel') ?></button>
                                            <button type="submit" class="btn btn-danger"><?= __('Submit') ?></button>
                                        </div>
                                    </form>
                                </div>
                            </div>

                        <!-- Billing Information Tab -->
                        <div class="tab-pane fade" id="billing-tab">
                            <div class="container ">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h4><?= __('My Address') ?></h4>
                                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addAddressModal"><i class="fas fa-plus"></i> <?= __('Add Address') ?></button>
                                </div>

                                <hr />

                                <div class="row">

                                    <?php if (!empty($customerAddresses)): ?>
                                        <?php foreach ($customerAddresses as $k => $address): ?>
                                        <div class="col-md-4 mb-3">
                                            <div class="card">
                                                <div class="card-header justify-content-between align-items-center d-flex">
                                                    <small><?= $address->type ?></small>
                                                    <div class="float-end">
                                                        <input type="hidden" id="csrf-token-address" value="<?= h($this->request->getAttribute('csrfToken')) ?>">
                                                    </div>
                                                </div>
                                                <div class="card-body">
                                                    <h5 class="card-title"><?= $address->name ?></h5>
                                                    <small><?= $address->house_no . ' ' . $address->address_line1 ?></small><br>
                                                    <small><?= $address->address_line2 . ' ' . $address->landmark ?></small><br>
                                                    <small><?= $address->city->city_name . ' ' . $address->state->state_name . ' ' . $address->zipcode ?></small><br>
                                                    <small><?= $address->country->name ?></small><br><br>

                                                    <a class="btn btn-link text-decoration-none" onclick="editAddress(<?= $address->id ?>)" title="Edit">Edit</a>|
                                                    <a class="btn btn-link text-decoration-none" onclick="deleteAddress(<?= $address->id ?>)" title="Delete">Remove</a>
                                                </div>
                                            </div>
                                        </div>

                                        <?php endforeach; ?>
                                        <?php else: ?>
                                            <div class="alert alert-info"><?= __('No Orders Found.') ?></div>
                                    <?php endif; ?>     

                                </div>
                            </div>
                        </div>

                        <!-- Orders Tab -->
                        <div class="tab-pane fade" id="orders-tab">
                            <div class="container" style="min-height: 65vh;">
                                <h4><?= __('My Orders') ?></h4>
                                <hr />

                                <!-- Order List -->
                                <div class="accordion" id="orderAccordion">
                                    <?php if (!empty($orders)): ?>
                                        <?php foreach ($orders as $k => $order): ?>
                                            <div class="accordion-item mb-3">
                                                <h2 class="accordion-header" id="headingOrder<?= $order->id ?>">
                                                    <button class="accordion-button <?= $k === 0 ? '' : 'collapsed' ?>" type="button" data-bs-toggle="collapse"
                                                        data-bs-target="#collapseOrder<?= $order->id ?>" aria-expanded="<?= $k === 0 ? 'true' : 'false' ?>" aria-controls="collapseOrder<?= $order->id ?>"><b>
                                                        <?= __('Order') ?> #<?= h($order->order_number ?? $order->id) ?></b> &nbsp;|&nbsp;
                                                        <span class="status-badge status-<?= strtolower($order->status) ?> ms-2"><?= h($order->status) ?></span>
                                                    </button>
                                                </h2>
                                                <div id="collapseOrder<?= $order->id ?>" class="accordion-collapse collapse <?= $k === 0 ? 'show' : '' ?>"
                                                    aria-labelledby="headingOrder<?= $order->id ?>" data-bs-parent="#orderAccordion">
                                                    <div class="accordion-body">
                                                        <div class="row mb-3">
                                                            <div class="col-md-6">
                                                                <p><strong><?= __('Order Date:') ?></strong> <?= h($order->order_date ? date('Y-m-d', strtotime($order->order_date)) : '') ?></p>
                                                                <p><strong><?= __('Estimated Delivery:') ?></strong> <?= h($order->delivery_date ?? '-') ?></p>
                                                            </div>
                                                        </div>
                                                        <?php if (!empty($order->order_items)): ?>
                                                            <ul class="list-group mb-3">
                                                                <?php foreach ($order->order_items as $item): ?>
                                                                    <li class="list-group-item">
                                                                        <div class="d-flex justify-content-between mb-3">
                                                                            <div>
                                                                                <strong><a class="text-decoration-none text-dark" target="_blank" href="/product/<?= h($item->product->id) ?>"><?= h($item->product_name ?? 'Product') ?></a>
                                                                                <a class="text-dark" target="_blank" href="/product/<?= h($item->product->id) ?>"><i class="mx-2 fas fa-eye"></i></a></strong><br>
                                                                                <?= __('Quantity:') ?> <?= h($item->quantity) ?>
                                                                            </div>
                                                                            <div><strong><?= $this->Price->setPriceFormat($item->price) ?></strong></div>
                                                                        </div>
                                                                        <?php if ($order->status == 'Delivered'): ?>
                                                                                <div>
                                                                                    <strong><?= __('Rate This Product') ?></strong>
                                                                                    <!-- Star Buttons -->
                                                                                    <button <?= !empty($item->product->reviews) ? 'disabled' : '' ?> type="button" class="btn btn" onclick="rateProduct(<?= h($item->product->id) ?>)" style="color:#FF6200">
                                                                                        <i class="far fa-star"></i><i class="far fa-star"></i><i class="far fa-star"></i><i class="far fa-star"></i><i class="far fa-star"></i>
                                                                                    </button>
                                                                                </div>
                                                                        <?php endif; ?>
                                                                    </li>
                                                                <?php endforeach; ?>
                                                            </ul>
                                                        <?php endif; ?>
                                                        <!-- Action Buttons -->
                                                        <div class="d-flex flex-wrap gap-2">
                                                            <?php if ($order->status == 'Pending' || $order->status == 'Processing' || $order->status == 'Approved'): ?>
                                                                <button class="btn btn-outline-danger btn-sm" onclick="cancelOrder(<?= $order->id ?>)"><?= __('Cancel Order') ?></button>
                                                            <?php endif; ?>
                                                            <?php
                                                                $returnDays = (int)($session->read('siteSettings.product_return_in_days') ?? 0);
                                                                ?>
                                                            <?php if ($order->status == 'Delivered' && strtotime($order->delivery_date) > strtotime("-{$returnDays} days")):  ?>
                                                                <button class="btn btn-primary btn-sm" onclick="returnOrder(<?= $order->id ?>)"><?= __('Request Return') ?> </button>
                                                            <?php endif; ?>
                                                            <a target="_blank" href="<?= $this->Url->build(['controller' => 'Account','action' => 'downloadInvoice', $order->id]); ?>" class="btn btn-outline-primary btn-sm"><?= __('Download Invoice (pdf)') ?></a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <div class="alert alert-info"><?= __('No Orders Found.') ?></div>
                                    <?php endif; ?>
                                </div>
                                <div id="loading" class="text-center my-3 d-none">
                                    <div class="spinner-border"></div>
                                </div>
                            </div>
                            
                            <!-- Rating Modal -->
                            <div class="modal fade" id="ratingModal" tabindex="-1" aria-labelledby="ratingModalLabel" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <form id="ratingForm">
                                            <div class="modal-header">
                                            <h5 class="modal-title" id="ratingModalLabel"><?= __('Rate and Review Product') ?></h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                            <!-- Star Rating -->
                                            <div class="mb-3 text-center">
                                                <label class="form-label d-block"><?= __('Your Rating') ?></label>
                                                <div id="starRating">
                                                <span class="star fs-3" data-value="1"><i class="far fa-star"></i></span>
                                                <span class="star fs-3" data-value="2"><i class="far fa-star"></i></span>
                                                <span class="star fs-3" data-value="3"><i class="far fa-star"></i></span>
                                                <span class="star fs-3" data-value="4"><i class="far fa-star"></i></span>
                                                <span class="star fs-3" data-value="5"><i class="far fa-star"></i></span>
                                                </div>
                                            </div>
                                            <!-- Review Textarea -->
                                            <div class="mb-3">
                                                <label for="reviewText" class="form-label"><?= __('Your Review') ?></label>
                                                <textarea class="form-control" id="reviewText" rows="4" placeholder="<?= __('Write your review here') ?>..."></textarea>
                                            </div>
                                            <input type="hidden" id="selectedRating" name="rating" value="0">
                                            </div>
                                            <div class="modal-footer">
                                            <button type="submit" class="btn btn-primary"><?= __('Submit Review') ?></button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <!-- Cancel Modal -->
                            <div class="modal fade" id="cancelModal" tabindex="-1" aria-labelledby="cancelModalLabel"
                                aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <form id="cancellationForm">
                                            <div class="modal-header">
                                                <h5 class="modal-title"><?= __('Cancel Order') ?></h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal"
                                                    aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <input type="hidden" id="cancel_order_id" name="order_id" value="">
                                                <div class="mb-3">
                                                    <label for="cancellation_reason" class="form-label"><?= __('Additional Comments (Optional)') ?></label>
                                                    <textarea class="form-control" id="cancellation_reason" name="reason" rows="3"
                                                        placeholder="<?= __('Please provide any additional details about your cancellation...') ?>"></textarea>
                                                </div>

                                                <div class="alert alert-warning">
                                                    <small><?= __('Note: This order can only be cancelled before shipment. Once submitted, your cancellation request will be reviewed by our team.') ?></small>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary"
                                                    data-bs-dismiss="modal"><?= __('Close') ?></button>
                                                <button type="submit" class="btn btn-danger"><?= __('Submit Cancellation Request') ?></button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <!-- Return Modal -->
                            <div class="modal fade" id="returnModal" tabindex="-1" aria-labelledby="returnModalLabel"
                                aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <form id="returnForm">
                                            <div class="modal-header">
                                                <h5 class="modal-title"><?= __('Request Return/Refund') ?></h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal"
                                                    aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <p><?= __('Request a return for this order. Please note that all returns must be within the allowed return period and are subject to admin approval.') ?></p>
                                                <div class="mb-3">
                                                    <label for="returnReason" class="form-label"><?= __('Reason for return') ?></label>
                                                    <textarea class="form-control" id="returnReason" rows="3"
                                                        required></textarea>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary"
                                                    data-bs-dismiss="modal"><?= __('Close') ?></button>
                                                <button type="submit" class="btn btn-warning"><?= __('Submit Request') ?></button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        <div id="scroll-sentinel"></div>
                        </div>
                        
                        <!-- wishlist Tab -->
                        <div class="tab-pane fade" id="wishlist-tab">
                            <div class="container">
                                <h4 class="mb-4"><?= __('My Wishlist') ?></h4>

                                <!-- Wishlist Grid -->
                                <div class="row row-cols-1 row-cols-md-3 g-4">
                                    <?php if (!empty($wishlistItems)): ?>
                                        <?php foreach ($wishlistItems as $wishlist): ?>
                                            <div class="col">
                                                <div class="card wishlist-card h-100">
                                                    <img src="<?= h($wishlist->product->product_image ?? '../../img/ozone/Meryl_Lounge-view.png') ?>" class="card-img-top"
                                                        alt="Product Image">
                                                    <div class="card-body d-flex flex-column">
                                                        <h5 class="card-title"><?= h($wishlist->product->product_name ?? 'Product Title') ?></h5>
                                                        <p class="card-text"><strong><?= __('Price:') ?></strong> <?= isset($wishlist->product->promotion_price) ? $this->Price->setPriceFormat($wishlist->product->promotion_price) : '' ?></p>
                                                        <!-- <p class="card-text"><strong><?= __('Added By:') ?></strong> <?= h($wishlist->product->added_by ?? 'Admin Name') ?></p> -->
                                                        <input type="hidden" id="csrf-token" value="<?= h($this->request->getAttribute('csrfToken')) ?>">
                                                        <div class="mt-auto">
                                                            <button class="btn btn-primary btn-sm w-100 mb-2" onclick="add_to_cart(<?= $wishlist->product->id ?>,<?= $users->customer->id ?>)"><?= __('Add to Cart') ?></button>
                                                            <button class="btn btn-outline-danger btn-sm w-100" onclick="remove_from_wishlist(<?= $wishlist->product->id ?>,<?= $users->customer->id ?>)"><?= __('Remove') ?></button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <div class="col">
                                            <div class="alert alert-info w-100"><?= __('No wishlist items found.') ?></div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Change Password Tab -->
                        <div class="tab-pane fade" id="changePassword">
                            <h4><?= __('Change Password') ?></h4>
                            <hr />
                            <form class="account-form" id="my_profile_form" method="post" action="<?= $this->Url->build(['controller' => 'Account','action' => 'changePassword']); ?>" enctype="multipart/form-data">
                                <?= $this->Form->hidden('_csrfToken', ['value' => $this->request->getAttribute('csrfToken')]); ?>

                                <div class="mb-3">
                                    <label for="currentPassword" class="form-label"><?= __('Current Password') ?></label>
                                    <div class="input-group position-relative">
                                        <input type="password" class="form-control" id="currentPassword" name="currentPassword"
                                            placeholder="<?= __('Enter current password') ?>" required>
                                        <span class="show-password-icon-account" onclick="toggleCurrentPasswordVisibility()">
                                            <i class="fa fa-eye"></i>
                                        </span>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="newPassword" class="form-label"><?= __('New Password') ?></label>
                                    <div class="input-group position-relative">
                                        <input type="password" class="form-control" id="newPassword" name="newPassword"
                                            placeholder="<?= __('Enter new password') ?>" required>
                                        <span class="show-password-icon-account" onclick="toggleNewPasswordVisibility()">
                                            <i class="fa fa-eye"></i>
                                        </span>
                                    </div>
                                </div>
                                <div class="text-center mb-3" id="passwordError" style="color:orange;display:none"></div>
                                <div class="mb-3">
                                    <label for="confirmPassword" class="form-label"><?= __('Confirm New Password') ?></label>
                                    <div class="input-group position-relative">
                                        <input type="password" class="form-control" id="confirmPassword" name="confirmPassword"
                                            placeholder="<?= __('Confirm new password') ?>" required>
                                        <span class="show-password-icon-account" onclick="toggleConfirmPasswordVisibility()">
                                            <i class="fa fa-eye"></i>
                                        </span>
                                    </div>
                                </div>
                                <div class="text-center mb-3 password_error" style="color:orange;display:none"><?= __('Confirm Password must be same as Password') ?></div>
                                <button id="update-password" type="submit" class="btn btn-success"><?= __('Update Password') ?></button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

<script>
    $(document).ready(function () {
        $('#addAddressModal').find('#country').on('change', function () {
            var countryId = $(this).val();
            if (countryId) {
                $.ajax({
                    url: '<?= $this->Url->build(['controller' => 'Account', 'action' => 'getStatesByCountry']) ?>',
                    type: 'POST',
                    data: { country_id: countryId },
                    headers: {
                        'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                    },
                    success: function (response) {
                        var $state = $('#addAddressModal').find('#state');
                        var $city = $('#addAddressModal').find('#city');

                        $state.empty().append('<option value=""><?= __('Select your State') ?></option>');
                        $city.empty().append('<option value=""><?= __('Select your City') ?></option>'); // reset cities

                        $.each(response.states, function (id, name) {
                            $state.append($('<option>', {
                                value: id,
                                text: name
                            }));
                        });
                    }
                });
            }
        });

        $('#editAddressModal').find('#country').on('change', function () {
            const countryId = $(this).val();
            const selectedStateId = $(this).data('state-id') || null;

            if (countryId) {
                $.ajax({
                    url: '<?= $this->Url->build(['controller' => 'Account', 'action' => 'getStatesByCountry']) ?>',
                    type: 'POST',
                    data: { country_id: countryId },
                    headers: {
                        'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                    },
                    success: function (response) {
                        var $state = $('#editAddressModal').find('#state');
                        var $city = $('#editAddressModal').find('#city');

                        $state.empty().append('<option value=""><?= __('Select your State') ?></option>');
                        $city.empty().append('<option value=""><?= __('Select your City') ?></option>');

                        $.each(response.states, function (id, name) {
                            $state.append($('<option>', {
                                value: id,
                                text: name
                            }));
                        });

                        if (selectedStateId) {
                            $state.val(selectedStateId).trigger('change');
                        }
                    }
                });
            }
        });

        $('#addAddressModal').find('#state').on('change', function () {
            var stateId = $(this).val();
            if (stateId) {
                $.ajax({
                    url: '<?= $this->Url->build(['controller' => 'Account', 'action' => 'getCitiesByState']) ?>',
                    type: 'POST',
                    data: { state_id: stateId },
                    headers: {
                        'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                    },
                    dataType: 'json',
                    success: function (response) {
                        var $city = $('#addAddressModal').find('#city');
                        $city.empty().append('<option value=""><?= __('Select your City') ?></option>');

                        $.each(response.cities, function (id, name) {
                            $city.append($('<option>', {
                                value: id,
                                text: name
                            }));
                        });
                    }
                });
            }
        });

        $('#editAddressModal').find('#state').on('change', function () {
            const stateId = $(this).val();
            const selectedCityId = $('#editAddressModal').find('#country').data('city-id') || null;

            if (stateId) {
                $.ajax({
                    url: '<?= $this->Url->build(['controller' => 'Account', 'action' => 'getCitiesByState']) ?>',
                    type: 'POST',
                    data: { state_id: stateId },
                    headers: {
                        'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                    },
                    dataType: 'json',
                    success: function (response) {
                        var $city = $('#editAddressModal').find('#city');
                        $city.empty().append('<option value=""><?= __('Select your City') ?></option>');

                        $.each(response.cities, function (id, name) {
                            $city.append($('<option>', {
                                value: id,
                                text: name
                            }));
                        });

                        if (selectedCityId) {
                            $city.val(selectedCityId);
                        }
                    }
                });
            }
        });
    });

    function cancelOrder(order_id) {
        // Set the order ID in the hidden field
        $('#cancel_order_id').val(order_id);

      

        // Show the modal
        $('#cancelModal').modal('show');

        // Remove any existing event handlers to prevent multiple bindings
        $('#cancellationForm').off('submit');

        $('#cancellationForm').on('submit', function (e) {
            e.preventDefault();

            // Validate form

            // Get form data
            const formData = {
                order_id: order_id,
                reason: $('#cancellation_reason').val(),
                product_id: 1 // This might need to be dynamic based on your needs
            };

            $.ajax({
                url: '<?= $this->Url->build(['controller' => 'Account', 'action' => 'cancelOrder']) ?>',
                type: 'POST',
                data: formData,
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(response) {
                    if (response.status == 'success') {
                        $('#cancelModal').modal('hide');
                        showToastMessage(response.message, 'success');
                        window.location.href = window.location.pathname + '#orders-tab';
                        window.location.reload();
                    } else {
                        showToastMessage(response.message, 'error');
                    }
                },
                error: function() {
                    showToastMessage('<?= __('An error occurred while cancelling the order.') ?>', 'error');
                }
            });
        });
    }

  

    function returnOrder(order_id) {
        $('#returnModal').modal('show');
        $('#returnForm').on('submit', function (e) {
            const returnReason = $('#returnReason').val();
            e.preventDefault();
            $.ajax({
                url: '<?= $this->Url->build(['controller' => 'Account', 'action' => 'returnOrder']) ?>',
                type: 'POST',
                data: { order_id: order_id, reason: returnReason },
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(response) {
                    if (response.status == 'success') { 
                        $('#returnModal').modal('hide');
                        showToastMessage(response.message, 'success');
                        window.location.href = window.location.pathname + '#orders-tab';
                        window.location.reload();
                    } else {
                        showToastMessage(response.message, 'error');
                    }
                },
                error: function() {
                    showToastMessage('An error occurred while returning the order.', 'error');
                }
            });
        });
    }

    function rateProduct(product_id) {
        $('#ratingModal').modal('show');
        $('#ratingForm').on('submit', function (e) {
            e.preventDefault();
            const rating = $('#selectedRating').val();
            const review = $('#reviewText').val();
            $.ajax({
                url: '<?= $this->Url->build(['controller' => 'Account', 'action' => 'rateProduct']) ?>',
                type: 'POST',
                data: { product_id: product_id, rating: rating, review: review },
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function (response) {
                    if (response.status == 'success') {
                        showToastMessage(response.message, 'success');
                        $('#ratingModal').modal('hide');
                    } else {
                        showToastMessage(response.message, 'error');
                    }
                },
                error: function (xhr, status, error) {
                    var response = JSON.parse(xhr.responseText);
                    toastr.warning(response.message || 'An error occurred', '', {
                        timeOut: 3000,
                        progressBar: true,
                    });
                }
            });
        });
    }

    let offset = 5;
    let loading = false;

    const observer = new IntersectionObserver(entries => {
        if (entries[0].isIntersecting && !loading) {
            loadMoreOrders();
        }
    }, {
        rootMargin: '100px',
    });

    observer.observe(document.querySelector('#scroll-sentinel'));

    function loadMoreOrders() {
        loading = true;
        document.querySelector('#loading').classList.remove('d-none');

        fetch(`/account/load-more-orders?offset=${offset}`)
            .then(response => response.text())
            .then(html => {
                setTimeout(() => {
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    const newOrders = doc.body.childNodes;

                    const container = document.querySelector('#orderAccordion');
                    newOrders.forEach(node => container.appendChild(node));

                    offset += 5;
                    loading = false;
                    document.querySelector('#loading').classList.add('d-none');

                    // Optional: Stop observing if no new content
                    if (newOrders.length === 0) {
                        observer.disconnect();
                    }
                }, 2000);
            })
            .catch(() => {
                loading = false;
                document.querySelector('#loading').classList.add('d-none');
            });
    }

    const stars = document.querySelectorAll('.star');
    const ratingInput = document.getElementById('selectedRating');

    stars.forEach((star, index) => {
        star.addEventListener('click', () => {
        ratingInput.value = star.dataset.value;
        stars.forEach(s => s.classList.remove('text-warning'));
        for (let i = 0; i <= index; i++) {
            stars[i].classList.add('text-warning');
        }
        });
    });

    document.addEventListener("DOMContentLoaded", function () {
        const hash = window.location.hash;
        if (hash) {
            setTimeout(function () {
                const triggerEl = document.querySelector(`a[href="${hash}"]`);
                if (triggerEl) {
                    const tab = new bootstrap.Tab(triggerEl);
                    tab.show();
                }
            }, 200);
        }
    });

    function deleteAddress(address_id) {
        $('#deleteAddressModal').find('#address_id').val(address_id);
        $('#deleteAddressModal').modal('show');
    }

    function editAddress(address_id) {
        const csrfToken = $('#csrf-token-address').val();
        $.ajax({
            url: '<?= $this->Url->build(['controller' => 'Account', 'action' => 'getAddressById']) ?>',
            type: 'POST',
            data: { address_id: address_id, _csrfToken: csrfToken },
            success: function(response) {
                if (response.success) {
                    $('#editAddressModal').find('#address_id').val(response.address.id);
                    $('#editAddressModal').find('#name').val(response.address.name);
                    $('#editAddressModal').find('#address_type').val(response.address.type);
                    $('#editAddressModal').find('#house_no').val(response.address.house_no);
                    $('#editAddressModal').find('#address1').val(response.address.address_line1);
                    $('#editAddressModal').find('#address2').val(response.address.address_line2);
                    $('#editAddressModal').find('#landmark').val(response.address.landmark);
                    $('#editAddressModal').find('#zip').val(response.address.zipcode);
                    $('#editAddressModal').find('#country').val(response.address.country_id).data('state-id', response.address.state_id)
                    .data('city-id', response.address.city_id).trigger('change');
                    $('#editAddressModal').modal('show');
                } else {
                    showToastMessage(response.message, 'error');
                }
            },
            error: function() {
                showToastMessage('<?= __('An error occurred while removing the item.') ?>', 'error');
            }
        });
    }

    function add_to_cart(product_id, customer_id) {
        const csrfToken = $('#csrf-token').val();
        $.ajax({
            url: '<?= $this->Url->build(['controller' => 'Account', 'action' => 'addToCartFromWishlist']) ?>',
            type: 'POST',
            data: { product_id: product_id, customer_id: customer_id, _csrfToken: csrfToken },
            success: function(response) {
                if (response.success) {
                    showToastMessage(response.message, 'success');
                    window.location.hash = '#wishlist-tab';
                    location.reload();
                } else {
                    showToastMessage(response.message, 'error');
                }
            },
            error: function() {
                showToastMessage('<?= __('An error occurred while adding the item to the cart.') ?>', 'error');
            }
        });
    }

    function remove_from_wishlist(product_id, customer_id) {
        const csrfToken = $('#csrf-token').val();
        $.ajax({
            url: '<?= $this->Url->build(['controller' => 'Account', 'action' => 'removeWishlistData']) ?>',
            type: 'POST',
            data: { product_id: product_id, customer_id: customer_id, _csrfToken: csrfToken },
            success: function(response) {
                if (response.success) {
                    showToastMessage(response.message, 'success');
                    window.location.hash = '#wishlist-tab';
                    location.reload();
                } else {
                    showToastMessage(response.message, 'error');
                }
            },
            error: function() {
                showToastMessage('<?= __('An error occurred while removing the item.') ?>', 'error');
            }
        });
    }

    $(".profile-overlay-text").on("click", function() {
        $("#profile_photo").click();
    });

    function previewProfileImage(event) {
        const reader = new FileReader();
        reader.onload = function () {
            document.getElementById('profileImagePreview').src = reader.result;
        };
        reader.readAsDataURL(event.target.files[0]);
    }
    
    function confirmDelete() {
        if (confirm('<?= __('Are you sure you want to delete your account? This action cannot be undone.') ?>')) {
            document.getElementById('deleteAccountForm').submit();
        }
    }

    $(document).ready(function() {
        // Check for toast message from session
        <?php
        $toastMessage = $session->read('toast_message');
        if ($toastMessage):
            // Clear the message from session after reading
            $session->delete('toast_message');
        ?>
        showToastMessage('<?= h($toastMessage['message']) ?>', '<?= h($toastMessage['type']) ?>');
        <?php endif; ?>
    });

    function showToastMessage(message, type) {
        // Create message container with enhanced styling
        const messageContainer = $(`
            <div class="alert alert-dismissible fade show" style="
                margin-bottom: 10px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                border-radius: 8px;
                border: none;
                animation: slideInRight 0.3s ease-out;
            "></div>
        `);

        // Set message type and styling
        const alertClass = type === 'success' ? 'alert-success' :
                          type === 'error' ? 'alert-danger' :
                          type === 'warning' ? 'alert-warning' : 'alert-info';

        messageContainer.addClass(alertClass);

        // Add message content with icon
        const icon = type === 'success' ? 'fas fa-check-circle' :
                    type === 'error' ? 'fas fa-exclamation-circle' :
                    type === 'warning' ? 'fas fa-exclamation-triangle' : 'fas fa-info-circle';

        messageContainer.html(`
            <div class="alert-body d-flex align-items-center">
                <i class="${icon} me-2"></i>
                <span><b>${message}</b></span>
                <button type="button" class="btn-close ms-auto" onclick="$(this).closest('.alert').fadeOut(300, function(){ $(this).remove(); })"></button>
            </div>
        `);

        // Add to container and show with animation
        $('#toast-message-container').append(messageContainer);

        // Auto hide after 4 seconds
        setTimeout(() => {
            messageContainer.fadeOut(300, function() {
                $(this).remove();
            });
        }, 4000);
    }

    $(document).ready(function() {
        $('#changePassword #newPassword').on('keyup', function () {
                var password = $(this).val();
                var errorMsg = '';

                if (password.length < 8) {
                    $('#passwordError').text('<?= __('Password must be at least 8 characters long.') ?>').show();
                    $('#send_code_btn').prop('disabled',true);
                    $('#update-password').prop('disabled',true);
                } else if (!/[A-Z]/.test(password)) {
                    $('#passwordError').text('<?= __('Password must include at least one uppercase letter.') ?>').show();
                    $('#send_code_btn').prop('disabled',true);
                    $('#update-password').prop('disabled',true);
                } else if (!/[a-z]/.test(password)) {
                    $('#passwordError').text('<?= __('Password must include at least one lowercase letter.') ?>').show();
                    $('#send_code_btn').prop('disabled',true);
                    $('#update-password').prop('disabled',true);
                } else if (!/[0-9]/.test(password)) {
                    $('#passwordError').text('<?= __('Password must include at least one number.') ?>').show();
                    $('#send_code_btn').prop('disabled',true);
                    $('#update-password').prop('disabled',true);
                } else if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
                    $('#passwordError').text('<?= __('Password must include at least one special character.') ?>').show();
                    $('#send_code_btn').prop('disabled',true);
                    $('#update-password').prop('disabled',true);
                }
                else{
                    $('#passwordError').text('').hide()
                    $('#send_code_btn').prop('disabled',false);
                    $('#update-password').prop('disabled',false);
                }
        });

        $('#changePassword #confirmPassword').on('keyup', function() {
                const password = $('#changePassword #newPassword').val();
                const confirmPassword = $('#changePassword #confirmPassword').val();
                if (password !== confirmPassword) {
                    $('.password_error').show();
                    $('#update-password').prop('disabled', true);
                } else {
                    $('.password_error').hide();
                    $('#update-password').prop('disabled', false);
                }
        });

        $('#my_profile_form').find('#email').on('keyup', function () {
                var email = $(this).val().trim();
                var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

                if (email === '') {
                    $('#emailErrorProfile').text('<?= __('Email is required.') ?>').show();
                    $('.verify-btn').prop('disabled',true);
                    $('#send_code_btn').prop('disabled',true);
                    $('.btn-login').prop('disabled',true);
                } else if (!emailRegex.test(email)) {
                    $('#emailErrorProfile').text('<?= __('Please enter a valid email address.') ?>').show();
                    $('.verify-btn').prop('disabled',true);
                    $('#send_code_btn').prop('disabled',true);
                    $('.btn-login').prop('disabled',true);
                } else {
                    $('#emailErrorProfile').text('').show();
                    $('.verify-btn').prop('disabled',false);
                    $('#send_code_btn').prop('disabled',false);
                    $('.btn-login').prop('disabled',false);
                }
        });

        $('#my_profile_form').find('#phone').on('keyup', function () {
            var phone = $(this).val().trim();
            var phoneRegex = /^[6-9]\d{9}$/;

            if (phone === '') {
                $('#phoneErrorProfile').text('<?= __('Phone number is required.') ?>').show();
                $('.verify-btn').prop('disabled', true);
                $('#send_code_btn').prop('disabled', true);
                $('.btn-login').prop('disabled', true);
            }
            //  else if (!phoneRegex.test(phone)) {
            //     $('#phoneErrorProfile').text('<?= __('Enter a valid 10-digit phone number.') ?>').show();
            //     $('.verify-btn').prop('disabled', true);
            //     $('#send_code_btn').prop('disabled', true);
            //     $('.btn-login').prop('disabled', true);
            // } 
            else {
                $('#phoneErrorProfile').text('').hide();
                $('.verify-btn').prop('disabled', false);
                $('#send_code_btn').prop('disabled', false);
                $('.btn-login').prop('disabled', false);
            }
        });
    });

    // Password visibility toggle functions
    function toggleCurrentPasswordVisibility() {
        const passwordField = document.getElementById('currentPassword');
        const icon = event.target;
        const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordField.setAttribute('type', type);

        // Toggle icon
        if (type === 'text') {
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    }

    function toggleNewPasswordVisibility() {
        const passwordField = document.getElementById('newPassword');
        const icon = event.target;
        const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordField.setAttribute('type', type);

        // Toggle icon
        if (type === 'text') {
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    }

    function toggleConfirmPasswordVisibility() {
        const passwordField = document.getElementById('confirmPassword');
        const icon = event.target;
        const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordField.setAttribute('type', type);

        // Toggle icon
        if (type === 'text') {
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    }
</script>

<script src="../../javascript/ozone.js"></script>
    <!-- Bootstrap JS -->
<script src="../../bundles/bootstrap/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script> -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"
        integrity="sha512-v2CJ7UaYy4JwqLDIrZUI/4hqeoQieOmAZNXBeQyjo21dadnwR+8ZaIJVT8EE2iyI61OV8e6M8PP2/4hpQINQ/g=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script src="https://cdn.jsdelivr.net/npm/nouislider@15.7.0/dist/nouislider.min.js"></script>
    <!-- vendors -->
<script src="../../carousel/assets/vendors/highlight.js"></script>
<script src="../../carousel/assets/js/app.js"></script>
<script src="https://cdn.jsdelivr.net/npm/nouislider@15.7.0/dist/nouislider.min.js"></script>

</body>

</html>