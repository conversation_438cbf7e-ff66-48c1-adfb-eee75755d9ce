<?php
/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\OrderItemReview $review
 */
?>

<div class="section-header">
    <ul class="breadcrumb breadcrumb-style">
        <li class="breadcrumb-item">
            <a href="<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'index']) ?>">
                <h4 class="page-title m-b-0"><?= __("Dashboard") ?></h4>
            </a>
        </li>
        <li class="breadcrumb-item"><?= $this->Html->link(__("Reviews"), ['action' => 'index']) ?></li>
        <li class="breadcrumb-item active"><?= __("View") ?></li>
    </ul>
</div>
<div class="section-body1">
    <div class="container-fluid">
        <?= $this->Flash->render() ?>
    </div>
</div>
<div class="section-body">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h4><?= __('Review Information') ?></h4>
                        <div class="card-header-action">
                            <!-- <?= $this->Html->link(__('Edit'), ['action' => 'edit', $review->id], ['class' => 'btn btn-warning']) ?>
                            <?= $this->Form->postLink(__('Delete'), ['action' => 'delete', $review->id], [
                                'class' => 'btn btn-danger',
                                'confirm' => __('Are you sure you want to delete this review?')
                            ]) ?> -->
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong><?= __('Review ID:') ?></strong>
                                <p class="text-muted">#<?= h($review->id) ?></p>
                            </div>
                            <div class="col-md-6">
                                <strong><?= __('Date Created:') ?></strong>
                                <p class="text-muted"><?= h($review->created->format('Y-m-d H:i:s')) ?></p>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <strong><?= __('Status:') ?></strong>
                                <p>
                                    <span class="badge badge-<?= $review->status === 'Active' ? 'success' : ($review->status === 'Inactive' ? 'warning' : 'danger') ?>">
                                        <?= h($review->status) ?>
                                    </span>
                                </p>
                            </div>
                            <div class="col-md-6">
                                <strong><?= __('Publish Status:') ?></strong>
                                <p>
                                    <span class="badge badge-<?= $review->publish_status === 'published' ? 'success' : ($review->publish_status === 'pending' ? 'warning' : 'danger') ?>">
                                        <?= h(ucfirst($review->publish_status)) ?>
                                    </span>
                                </p>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <strong><?= __('Rating:') ?></strong>
                                <div class="rating mb-3">
                                    <?php for ($i = 1; $i <= 5; $i++): ?>
                                        <i class="<?= $i <= $review->rating ? 'fas' : 'far' ?> fa-star text-warning" style="font-size: 1.5rem;"></i>
                                    <?php endfor; ?>
                                    <span class="ml-2 h5"><?= $review->rating ?>/5</span>
                                </div>
                            </div>
                        </div>

                        <?php if (!empty($review->review)): ?>
                            <div class="row">
                                <div class="col-md-12">
                                    <strong><?= __('Review Text:') ?></strong>
                                    <div class="alert alert-light mt-2">
                                        <p class="mb-0"><?= h($review->review) ?></p>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4><?= __('Customer Information') ?></h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong><?= __('Name:') ?></strong>
                                <p class="text-muted"><?= h($review->customer->user->first_name . ' ' . $review->customer->user->last_name) ?></p>
                            </div>
                            <div class="col-md-6">
                                <strong><?= __('Email:') ?></strong>
                                <p class="text-muted"><?= h($review->customer->user->email) ?></p>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <strong><?= __('Phone:') ?></strong>
                                <p class="text-muted"><?= h($review->customer->phone_number ?? 'N/A') ?></p>
                            </div>
                            <div class="col-md-6">
                                <strong><?= __('Customer ID:') ?></strong>
                                <p class="text-muted">#<?= h($review->customer_id) ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h4><?= __('Product Information') ?></h4>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($review->product->product_images)): ?>
                            <div class="text-center mb-3">
                                <img src="<?= $this->Media->getImageUrl($review->product->product_images[0]->image_name, 'products') ?>"
                                     alt="<?= h($review->product->name) ?>"
                                     class="img-fluid"
                                     style="max-height: 200px;">
                            </div>
                        <?php endif; ?>

                        <strong><?= __('Product Name:') ?></strong>
                        <p class="text-muted"><?= h($review->product->name) ?></p>

                        <strong><?= __('Product ID:') ?></strong>
                        <p class="text-muted">#<?= h($review->product_id) ?></p>

                        <?php if (!empty($review->product->sales_price)): ?>
                            <strong><?= __('Price:') ?></strong>
                            <p class="text-muted"><?= $this->Number->currency($review->product->sales_price) ?></p>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4><?= __('Order Information') ?></h4>
                    </div>
                    <div class="card-body">
                        <strong><?= __('Order Number:') ?></strong>
                        <p class="text-muted">#<?= h($review->order->order_number ?? $review->order_id) ?></p>

                        <strong><?= __('Order Date:') ?></strong>
                        <p class="text-muted"><?= h($review->order->order_date ? $review->order->order_date->format('Y-m-d') : 'N/A') ?></p>

                        <strong><?= __('Order Status:') ?></strong>
                        <p>
                            <span class="badge badge-<?= $review->order->status === 'Delivered' ? 'success' : 'info' ?>">
                                <?= h($review->order->status) ?>
                            </span>
                        </p>

                        <strong><?= __('Order Item ID:') ?></strong>
                        <p class="text-muted">#<?= h($review->order_item_id) ?></p>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h4><?= __('Quick Actions') ?></h4>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <?php if ($review->publish_status !== 'published'): ?>
                                <button type="button" class="btn btn-success" onclick="updateStatus('published')">
                                    <i class="fas fa-check"></i> <?= __('Publish Review') ?>
                                </button>
                            <?php endif; ?>

                            <?php if ($review->publish_status !== 'rejected'): ?>
                                <button type="button" class="btn btn-danger" onclick="updateStatus('rejected')">
                                    <i class="fas fa-times"></i> <?= __('Reject Review') ?>
                                </button>
                            <?php endif; ?>

                            <?php if ($review->publish_status !== 'pending'): ?>
                                <button type="button" class="btn btn-warning" onclick="updateStatus('pending')">
                                    <i class="fas fa-clock"></i> <?= __('Mark as Pending') ?>
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function updateStatus(status) {
    if (confirm('<?= __('Are you sure you want to update the publish status?') ?>')) {
        $.ajax({
            url: '<?= $this->Url->build(['action' => 'updatePublishStatus']) ?>',
            method: 'POST',
            data: {
                id: <?= $review->id ?>,
                status: status,
                _csrfToken: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.message);
                }
            },
            error: function() {
                alert('<?= __('An error occurred while updating the status.') ?>');
            }
        });
    }
}
</script>
