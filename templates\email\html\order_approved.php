<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Approved - <?= h($order_number) ?></title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
            color: #333;
        }
        .container {
            max-width: 600px;
            margin: 20px auto;
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .logo {
            margin-bottom: 20px;
        }
        .logo img {
            max-width: 150px;
            height: auto;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        .header p {
            margin: 10px 0 0 0;
            font-size: 16px;
            opacity: 0.9;
        }
        .content {
            padding: 40px 30px;
        }
        .greeting {
            font-size: 18px;
            color: #28a745;
            margin-bottom: 20px;
            font-weight: 600;
        }
        .message {
            font-size: 16px;
            line-height: 1.6;
            color: #555;
            margin-bottom: 30px;
        }
        .order-details {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            margin: 25px 0;
            border-left: 4px solid #28a745;
        }
        .order-details h3 {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 20px;
            font-weight: 600;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .detail-row:last-child {
            border-bottom: none;
        }
        .detail-label {
            font-weight: 500;
            color: #666;
        }
        .detail-value {
            font-weight: 600;
            color: #333;
        }
        .status-approved {
            color: #28a745;
            font-weight: 700;
            font-size: 18px;
        }
        .approval-info {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 20px;
            border-radius: 8px;
            margin: 25px 0;
        }
        .approval-info h3 {
            margin: 0 0 15px 0;
            color: #155724;
            font-size: 18px;
            display: flex;
            align-items: center;
        }
        .approval-info h3::before {
            content: "✅";
            margin-right: 10px;
            font-size: 20px;
        }
        .approval-info p {
            margin: 5px 0;
            line-height: 1.5;
        }
        .items-section {
            margin: 30px 0;
        }
        .items-section h3 {
            color: #333;
            font-size: 20px;
            margin-bottom: 20px;
            font-weight: 600;
        }
        .item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .item:last-child {
            border-bottom: none;
        }
        .item-info {
            flex: 1;
        }
        .item-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        .item-details {
            font-size: 14px;
            color: #666;
        }
        .item-total {
            font-weight: 600;
            color: #28a745;
            font-size: 16px;
        }
        .processing-info {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
            color: #856404;
        }
        .processing-info h3 {
            margin: 0 0 15px 0;
            color: #856404;
            font-size: 18px;
            display: flex;
            align-items: center;
        }
        .processing-info h3::before {
            content: "📦";
            margin-right: 10px;
            font-size: 20px;
        }
        .processing-info p {
            margin: 5px 0;
        }
        .delivery-info {
            background-color: #e8f5e8;
            border: 1px solid #c3e6c3;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
        }
        .delivery-info h3 {
            margin: 0 0 15px 0;
            color: #2d5a2d;
            font-size: 18px;
            display: flex;
            align-items: center;
        }
        .delivery-info h3::before {
            content: "🚚";
            margin-right: 10px;
            font-size: 20px;
        }
        .delivery-info p {
            margin: 5px 0;
            color: #2d5a2d;
        }
        .payment-info {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 25px 0;
            text-align: center;
        }
        .payment-info h3 {
            margin: 0 0 10px 0;
            font-size: 18px;
        }
        .payment-info p {
            margin: 0;
            font-size: 14px;
            opacity: 0.9;
        }
        .cta-section {
            text-align: center;
            margin: 30px 0;
        }
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            transition: transform 0.3s ease;
            margin: 0 10px;
        }
        .cta-button:hover {
            transform: translateY(-2px);
            color: white;
        }
        .cta-button.secondary {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        }
        .timeline {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
        }
        .timeline h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 18px;
        }
        .timeline-item {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px 0;
        }
        .timeline-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        .timeline-icon.completed {
            background-color: #28a745;
            color: white;
        }
        .timeline-icon.current {
            background-color: #ffc107;
            color: #333;
        }
        .timeline-icon.pending {
            background-color: #e9ecef;
            color: #666;
        }
        .timeline-text {
            flex: 1;
            color: #555;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }
        .footer p {
            margin: 5px 0;
            color: #666;
            font-size: 14px;
        }
        .footer .company-name {
            font-weight: 600;
            color: #28a745;
            font-size: 16px;
        }
        .social-links {
            margin: 20px 0;
        }
        .social-links a {
            display: inline-block;
            margin: 0 10px;
            color: #007bff;
            text-decoration: none;
        }
        @media (max-width: 600px) {
            .container {
                margin: 10px;
                border-radius: 8px;
            }
            .content {
                padding: 20px;
            }
            .header {
                padding: 20px;
            }
            .detail-row {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }
            .item {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            .cta-button {
                display: block;
                margin: 10px 0;
            }
            .timeline-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">
                <img src="https://ozone.com360degree.com/img/ozone/logo.png" alt="OZONEX Marketplace" width="150">
            </div>
            <h1><?= __('Order Processing!') ?></h1>
            <p><?= __('Your order is now being processed') ?></p>
        </div>

        <!-- Content -->
        <div class="content">
            <div class="greeting">
                <?= __('Great news,') ?> <?= h($customer_name) ?>!
            </div>

            <div class="message">
                <?= __('We\'re excited to let you know that your order is now being processed. Our team is working hard to prepare your items for shipment.') ?>
            </div>

            <!-- Order Details -->
            <div class="order-details">
                <h3><?= __('Order Summary') ?></h3>
                <div class="detail-row">
                    <span class="detail-label"><?= __('Order Number:') ?></span>
                    <span class="detail-value"><?= h($order_number) ?></span>
                </div>
                <div class="detail-row">
                    <span class="detail-label"><?= __('Order Date:') ?></span>
                    <span class="detail-value"><?= h($order_date) ?></span>
                </div>
                <div class="detail-row">
                    <span class="detail-label"><?= __('Payment Method:') ?></span>
                    <span class="detail-value"><?= h($payment_method) ?></span>
                </div>
                <div class="detail-row">
                    <span class="detail-label"><?= __('Order Status:') ?></span>
                    <span class="detail-value status-approved"><?= __('PROCESSING') ?></span>
                </div>
                <div class="detail-row">
                    <span class="detail-label"><?= __('Total Amount:') ?></span>
                    <span class="detail-value"><?= h($total_amount_formatted) ?></span>
                </div>
            </div>

            <!-- Processing Information -->
            <div class="approval-info">
                <h3><?= __('Processing Details') ?></h3>
                <p><strong><?= __('Processing Started:') ?></strong> <?= h($approval_date) ?></p>
                <p><?= __('Your order has been reviewed and is now being processed by our team. We\'re preparing your items for shipment.') ?></p>
                <p><?= __('You\'ll receive tracking information once your order ships.') ?></p>
            </div>

            <!-- Order Items -->
            <?php if (!empty($order_items)): ?>
            <div class="items-section">
                <h3><?= __('Your Approved Items') ?></h3>
                <?php foreach ($order_items as $item): ?>
                <div class="item">
                    <div class="item-info">
                        <div class="item-name"><?= h($item['product_name']) ?></div>
                        <div class="item-details">
                            <?= __('Quantity:') ?> <?= h($item['quantity']) ?> × <?= h($item['price_formatted']) ?>
                        </div>
                    </div>
                    <div class="item-total"><?= h($item['total_formatted']) ?></div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>

            <!-- Processing Information -->
            <div class="processing-info">
                <h3><?= __('What Happens Next?') ?></h3>
                <p><?= __('Our fulfillment team is now preparing your order for shipment.') ?></p>
                <p><?= __('Estimated processing time: 1-2 business days') ?></p>
                <p><?= __('You\'ll receive an email with tracking information once your order ships.') ?></p>
            </div>

            <!-- Order Timeline -->
            <div class="timeline">
                <h3><?= __('Order Progress') ?></h3>
                <div class="timeline-item">
                    <div class="timeline-icon completed">✓</div>
                    <div class="timeline-text"><?= __('Order Placed (Pending)') ?></div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-icon completed">✓</div>
                    <div class="timeline-text"><?= __('Order Confirmed') ?></div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-icon current">📦</div>
                    <div class="timeline-text"><?= __('Processing & Packaging') ?></div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-icon pending">🚚</div>
                    <div class="timeline-text"><?= __('Shipped') ?></div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-icon pending">🚛</div>
                    <div class="timeline-text"><?= __('Out for Delivery') ?></div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-icon pending">📍</div>
                    <div class="timeline-text"><?= __('Delivered') ?></div>
                </div>
            </div>

            <!-- Delivery Information -->
            <div class="delivery-info">
                <h3><?= __('Delivery Information') ?></h3>
                <!-- <p><strong><?= __('Estimated Delivery:') ?></strong> <?= h($estimated_delivery) ?></p> -->
                <p><strong><?= __('Delivery Address:') ?></strong></p>
                <p><?= h($delivery_address) ?></p>
                <?php if (!empty($order_notes)): ?>
                <p><strong><?= __('Order Notes:') ?></strong> <?= h($order_notes) ?></p>
                <?php endif; ?>
            </div>

            <!-- Payment Information -->
            <?php if ($payment_method === 'Cash on Delivery'): ?>
            <div class="payment-info">
                <h3><?= __('Payment Reminder') ?></h3>
                <p><?= __('You will pay') ?> <strong><?= h($total_amount_formatted) ?></strong> <?= __('in cash when your order is delivered.') ?></p>
            </div>
            <?php endif; ?>

            <div class="message">
                <?= __('Thank you for choosing OZONEX Marketplace. We appreciate your business and look forward to delivering your order soon!') ?>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p class="company-name"><?= __('OZONEX') ?></p>
            <p><?= __('Your order is on its way!') ?></p>
            <div class="social-links">
                <a href="#"><?= __('Track Order') ?></a> |
                <a href="#"><?= __('Contact Support') ?></a> |
                <a href="#"><?= __('Return Policy') ?></a>
            </div>
            <p><?= __('This email was sent to') ?> <?= h($customer_email) ?></p>
            <p><?= __('© 2024 OZONEX Marketplace. All rights reserved.') ?></p>
        </div>
    </div>
</body>
</html>
