<?php
/**
 * @var \App\View\AppView $this
 * @var iterable<\App\Model\Entity\User> $roles
 */
?>
<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/datatables.min.css') ?>">
<link rel="stylesheet"
    href="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/css/dataTables.bootstrap4.min.css') ?>">
<?php $this->end(); ?>
<div class="section-header">
    <ul class="breadcrumb breadcrumb-style ">
        <li class="breadcrumb-item">
            <a href="<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'index']) ?>">
                <h4 class="page-title m-b-0"><?= __("Dashboard") ?></h4>
            </a>
        </li>
        <li class="breadcrumb-item"><?= __("Users") ?></li>
        <li class="breadcrumb-item active"><?= __("Roles") ?></li>
    </ul>
</div>
<div class="section-body1">
    <div class="container-fluid">
        <?= $this->Flash->render() ?>
    </div>
</div>
<div class="section-body" id="list">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <h4><?= __("Roles") ?></h4>
                <div class="card-header-form">
                    <div class="input-group">
                        <input type="text" class="form-control search-control" placeholder="<?= __("Search") ?>"
                            id="customSearchBox" />
                        <div class="input-group-btn">
                            <button class="btn">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                        <?php if ($canAdd): ?>
                        <a href="<?= $this->Url->build(['controller' => 'Roles', 'action' => 'add']) ?>"
                            class="btn m-r-15">
                            <i class="fas fa-plus"></i>
                            <?= __("Add Role") ?>
                        </a>
                        <?php endif; ?>
                        <button class="btn menu-toggle" type="submit">
                            <i class="fas fa-filter"></i>
                            <?= __("Filter") ?>
                        </button>
                    </div>
                </div>
            </div>
            <div id="filter-body-container">
                <div class="input-group m-l-25">
                    <div class="d-flex">
                        <div class="form-group d-flex align-items-center m-l-20">
                        <?php echo $this->Form->control('status', [
                                    'type' => 'select',
                                    'options' => $status,
                                    'id' => 'filterStatus',
                                    'class' => 'form-control form-select',
                                    'label' => false,
                                    'empty' => __('Filter By Status'),
                                    'data' => ['bs-toggle' => 'dropdown'],
                                    'aria-expanded' => 'false'
                            ]) ?>
                        </div>
                        <div class="form-group ms-4">
                            <button class="btn btn-primary" id="filter">
                                <i class="fa fa-filter" aria-hidden="true"></i>
                            </button>
                            <button type="reset" class="btn btn-primary" onclick="resetFilters()"><i
                                    class="fas fa-redo-alt"></i></button>
                        </div>
                    </div>
                    <hr />
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped"
                        id="table-1">
                        <thead>
                            <tr>
                                <th>Id</th>
                                <th>Name</th>
                                <th>Description</th>
                                <th>No of Users</th>
                                <th id="status">Status</th>
                                <th class="actions">Actions</th>
                            </tr>
                        </thead>
                        <!-- <tbody>
                            <?php foreach ($roles as $role): ?>
                                <tr>
                                    <td><?= $this->Number->format($role->id) ?></td>
                                    <td><?= h($role->name) ?></td>
                                    <td><?= h($role->description ?: '-') ?></td>
                                    <td><a href="<?= $this->Url->build(['controller' => 'Users', 'action' => 'index', '?' => ['role' => $role->id]]) ?>"><?= h($role->user_count ?: '0') ?></a></td>
                                    <td>
                                    <?php
                                        $statusval = $statusMap[$role->status] ?? ['label' => 'Unknown', 'class' => 'col-red'];
                                        ?>
                                        <div class="badge-outline <?= $statusval['class'] ?>">
                                            <?= h($statusval['label']) ?>
                                        </div>
                                    </td>
                                    <td class="actions">
                                        <?php if ($canView): ?>
                                        <a href="<?= $this->Url->build(['controller' => 'Roles', 'action' => 'view', $role->id]) ?>"
                                            class="" data-toggle="tooltip" title="View"><i
                                                class="far fa-eye m-r-10"></i></a>
                                        <?php endif; ?>
                                        <?php if ($canEdit): ?>
                                        <a href="<?= $this->Url->build(['controller' => 'Roles', 'action' => 'edit', $role->id]) ?>"
                                            class="" data-toggle="tooltip" title="Edit"><i
                                                class="fas fa-pencil-alt m-r-10"></i></a>
                                        <?php endif; ?>
                                        <?php if ($canDelete): ?>
                                        <a href="<?= $this->Url->build(['controller' => 'Roles', 'action' => 'delete', $role->id]) ?>"
                                            class="delete-btn" data-toggle="tooltip" title="Delete"
                                            data-delete-confirmation="<?= addslashes($deleteConfirmationMessage); ?>"
                                            data-delete-warning="<?= addslashes($deleteWarningMessage); ?>"
                                            data-delete-fail="<?= addslashes($deleteFailMessage); ?>">
                                            <i class="far fa-trash-alt"></i>
                                        </a>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody> -->

                        <tbody>
                            <?php foreach ($roles as $role): ?>
                                <tr>
                                    <td><?= $this->Number->format($role->id) ?></td>
                                    <td><?= h($role->name) ?></td>
                                    <td><?= h($role->description ?: '-') ?></td>
                                    <td>
                                        <a href="<?= $this->Url->build(['controller' => 'Users', 'action' => 'index', '?' => ['role' => $role->id]]) ?>">
                                            <?= h($role->user_count ?: '0') ?>
                                        </a>
                                    </td>
                                    <td>
                                        <?php
                                            $statusval = $statusMap[$role->status] ?? ['label' => 'Unknown', 'class' => 'col-red'];
                                        ?>
                                        <div class="badge-outline <?= $statusval['class'] ?>">
                                            <?= h($statusval['label']) ?>
                                        </div>
                                    </td>
                                   <td class="actions">
                                        <?php if ($canView): ?>
                                            <a href="<?= $this->Url->build(['controller' => 'Roles', 'action' => 'view', $role->id]) ?>"
                                                data-toggle="tooltip" title="View"><i class="far fa-eye m-r-10"></i></a>
                                        <?php endif; ?>

                                        <?php if ($canEdit): ?>
                                            <?php if ($role->id == 4): ?>
                                                <a href="javascript:void(0)" class="disabled" data-toggle="tooltip" title="Edit Disabled">
                                                    <i class="fas fa-pencil-alt m-r-10 text-muted"></i>
                                                </a>
                                            <?php else: ?>
                                                <a href="<?= $this->Url->build(['controller' => 'Roles', 'action' => 'edit', $role->id]) ?>"
                                                    data-toggle="tooltip" title="Edit"><i class="fas fa-pencil-alt m-r-10"></i></a>
                                            <?php endif; ?>
                                        <?php endif; ?>

                                        <?php if ($canDelete): ?>
                                            <?php if ($role->id == 4): ?>
                                                <a href="javascript:void(0)" class="disabled" data-toggle="tooltip" title="Delete Disabled">
                                                    <i class="far fa-trash-alt text-muted"></i>
                                                </a>
                                            <?php else: ?>
                                                <a href="<?= $this->Url->build(['controller' => 'Roles', 'action' => 'delete', $role->id]) ?>"
                                                    class="delete-btn" data-toggle="tooltip" title="Delete"
                                                    data-delete-confirmation="<?= addslashes($deleteConfirmationMessage); ?>"
                                                    data-delete-warning="<?= addslashes($deleteWarningMessage); ?>"
                                                    data-delete-fail="<?= addslashes($deleteFailMessage); ?>">
                                                    <i class="far fa-trash-alt"></i>
                                                </a>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </td>

                                </tr>
                            <?php endforeach; ?>
                        </tbody>

                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
</div>
<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/datatables/datatables.min.js') ?>"></script>
<script
    src="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/js/dataTables.bootstrap4.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js'); ?>"></script>
<script src="<?= $this->Url->webroot('js/delete.js'); ?>"></script>
<script src="<?= $this->Url->webroot('js/filter.js') ?>"></script>
<script>
    var paginationCount = <?= json_encode($paginationCount) ?>;
    var table = $("#table-1").DataTable({
        columnDefs: [
            { orderable: false, targets: -1 } // Make the last column non-sortable
        ],
        dom: 'rtip',
        pageLength: paginationCount,
        drawCallback: function() {
            var api = this.api();
            api.column(0, {search: 'applied', order: 'applied'}).nodes().each(function(cell, i) {
                cell.innerHTML = i + 1;
            });
        }
    });

    // table.column(4).search('Active', true, false, false).draw();

    $('#customSearchBox').on('keyup', function () {
        table.search(this.value).draw();
    });

    function resetFilters() {
        $('#customSearchBox').val('');
        $('#filterStatus').val('');
        table.search('').columns().search('').draw();
        table.column(4).search('Active', true, false, false).draw();
        table.draw();
    }

    $('#filter').on('click', function (event) {
        event.preventDefault();
        var status = $("#filterStatus").val();
        var statusMap = <?= json_encode($status) ?>;
        var statusVal = statusMap[status] !== undefined ? statusMap[status] : '';
        
        if (statusVal == '') {
            table.column(4).search('Active', true, false, false).draw();
        } else {
            table.column(4).search(statusVal, true, false, false).draw();
        }
        table.draw();
    });

    // $('#filterStatus').on('change', function () {
    //     var filterValue = this.value === 'A' ? 'Active' : this.value === 'I' ? 'Inactive' : '';
    //     var statusColumn = table.column('#status');
    //     statusColumn.search(filterValue).draw();
    // });
    function performSearch() {
        var filterStatus = $('#filterStatus').val();

        $.ajax({
            url: '<?= $this->Url->build(['controller' => 'Roles', 'action' => 'filterSearch']) ?>',
            type: 'GET',
            data: {
                filterStatus: filterStatus
            },
            success: function (response) {
                console.log(response);
                table.clear().rows.add(response.data).draw();
            },
            error: function (xhr, status, error) {
                console.log('Error:', error);
            }
        });
    }
</script>
<?php $this->end(); ?>