<?php
declare(strict_types=1);

namespace App\Controller;
use Cake\Core\Configure;

/**
 * SiteSettings Controller
 *
 * @property \App\Model\Table\SiteSettingsTable $SiteSettings
 */
class SiteSettingsController extends AppController
{
    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
    }

    public function beforeFilter(\Cake\Event\EventInterface $event)
    {
        parent::beforeFilter($event);
    }

    public function index()
    {
        $query = $this->SiteSettings->find();
        $siteSettings = $this->paginate($query);
        
        $title = "Site Settings";
        $this->set(compact('siteSettings', 'title'));
    }

    /**
     * View method
     *
     * @param string|null $id Site Setting id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $siteSetting = $this->SiteSettings->get($id, contain: []);
        $this->set(compact('siteSetting'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function add()
    {

        $siteSetting = $this->SiteSettings->newEmptyEntity();
        if ($this->request->is('post')) {
            $siteSetting = $this->SiteSettings->patchEntity($siteSetting, $this->request->getData());
            if ($this->SiteSettings->save($siteSetting)) {
                $this->Flash->success(__('The site setting has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The site setting could not be saved. Please, try again.'));
        }
        $this->set(compact('siteSetting'));
    }

    private function generatePaginationOptions($start, $end, $step)
    {
        $options = [];
        for ($i = $start; $i <= $end; $i += $step) {
            $options[$i] = $i;
        }
        return $options;
    }

    /**
     * Edit method
     *
     * @param string|null $id Site Setting id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {

        $paginationCounts = $this->generatePaginationOptions(5, 100, 5);

        $siteSetting = $this->SiteSettings->get($id, contain: []);
        if ($this->request->is(['patch', 'post', 'put'])) {
            $siteSetting = $this->SiteSettings->patchEntity($siteSetting, $this->request->getData());
            if ($this->SiteSettings->save($siteSetting)) {
                $this->Flash->success(__('The site setting has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The site setting could not be saved. Please, try again.'));
        }
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';

        $title = "Site Settings | Edit";
        $this->set(compact('siteSetting', 'paginationCounts', 'title','currencySymbol'));
    }

    /**
     * Delete method
     *
     * @param string|null $id Site Setting id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $siteSetting = $this->SiteSettings->get($id);
        if ($this->SiteSettings->delete($siteSetting)) {
            $this->Flash->success(__('The site setting has been deleted.'));
        } else {
            $this->Flash->error(__('The site setting could not be deleted. Please, try again.'));
        }

        return $this->redirect(['action' => 'index']);
    }
}
