<?php
/**
 * Single Review Modal Component
 * @var \App\View\AppView $this
 */
?>

<!-- Single Review Modal -->
<div class="modal fade" id="reviewModal" tabindex="-1" aria-labelledby="reviewModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form id="singleReviewForm">
                <div class="modal-header">
                    <h5 class="modal-title" id="reviewModalLabel"><?= __('Rate and Review Product') ?></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-3">
                        <h6 id="productName"></h6>
                    </div>
                    <!-- Star Rating -->
                    <div class="mb-3 text-center">
                        <label class="form-label d-block"><?= __('Your Rating') ?></label>
                        <div id="singleStarRating">
                            <span class="star fs-3" data-value="1"><i class="far fa-star"></i></span>
                            <span class="star fs-3" data-value="2"><i class="far fa-star"></i></span>
                            <span class="star fs-3" data-value="3"><i class="far fa-star"></i></span>
                            <span class="star fs-3" data-value="4"><i class="far fa-star"></i></span>
                            <span class="star fs-3" data-value="5"><i class="far fa-star"></i></span>
                        </div>
                    </div>
                    <!-- Review Textarea -->
                    <div class="mb-3">
                        <label for="singleReviewText" class="form-label"><?= __('Your Review (Optional)') ?></label>
                        <textarea class="form-control" id="singleReviewText" rows="4" placeholder="<?= __('Write your review here') ?>..."></textarea>
                    </div>
                    <input type="hidden" id="singleOrderId" name="order_id">
                    <input type="hidden" id="singleOrderItemId" name="order_item_id">
                    <input type="hidden" id="singleProductId" name="product_id">
                    <input type="hidden" id="singleSelectedRating" name="rating" value="0">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?= __('Cancel') ?></button>
                    <button type="submit" class="btn btn-primary"><?= __('Submit Review') ?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Initialize star rating functionality
    initializeSingleStarRating();

    // Handle form submission
    $('#singleReviewForm').on('submit', function(e) {
        e.preventDefault();

        const rating = $('#singleSelectedRating').val();
        const reviewText = $('#singleReviewText').val().trim();
        const productId = $('#singleProductId').val();

        // Validation
        if (!rating || rating < 1) {
            Swal.fire({
                icon: 'warning',
                title: '<?= __('Rating Required') ?>',
                text: '<?= __('Please select a rating before submitting.') ?>',
                confirmButtonText: '<?= __('OK') ?>'
            });
            return;
        }

        if (!productId) {
            Swal.fire({
                icon: 'error',
                title: '<?= __('Error') ?>',
                text: '<?= __('Product information is missing.') ?>',
                confirmButtonText: '<?= __('OK') ?>'
            });
            return;
        }

        // Show loading
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.text();
        submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> <?= __('Submitting...') ?>');

        // Submit review
        $.ajax({
            url: '<?= $this->Url->build(['controller' => 'Cart', 'action' => 'addReview']) ?>',
            method: 'POST',
            data: {
                product_id: productId,
                rating: rating,
                review: reviewText,
                _csrfToken: $('meta[name="csrfToken"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    Swal.fire({
                        icon: 'success',
                        title: '<?= __('Success!') ?>',
                        text: response.message,
                        confirmButtonText: '<?= __('OK') ?>'
                    }).then(() => {
                        $('#reviewModal').modal('hide');
                        // Optionally reload the page to show the updated review status
                        location.reload();
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: '<?= __('Error') ?>',
                        text: response.message,
                        confirmButtonText: '<?= __('OK') ?>'
                    });
                }
            },
            error: function() {
                Swal.fire({
                    icon: 'error',
                    title: '<?= __('Error') ?>',
                    text: '<?= __('An error occurred while submitting your review. Please try again.') ?>',
                    confirmButtonText: '<?= __('OK') ?>'
                });
            },
            complete: function() {
                submitBtn.prop('disabled', false).text(originalText);
            }
        });
    });
});

function initializeSingleStarRating() {
    const stars = document.querySelectorAll('#singleStarRating .star');
    const ratingInput = document.getElementById('singleSelectedRating');

    stars.forEach((star, index) => {
        star.addEventListener('click', function() {
            const rating = index + 1;
            ratingInput.value = rating;

            // Update star display
            stars.forEach((s, i) => {
                const icon = s.querySelector('i');
                if (i < rating) {
                    icon.className = 'fas fa-star';
                    s.style.color = '#ffc107';
                } else {
                    icon.className = 'far fa-star';
                    s.style.color = '#ccc';
                }
            });
        });

        star.addEventListener('mouseenter', function() {
            const rating = index + 1;

            // Highlight stars on hover
            stars.forEach((s, i) => {
                if (i < rating) {
                    s.style.color = '#ffc107';
                } else {
                    s.style.color = '#ccc';
                }
            });
        });
    });

    // Reset to selected rating on mouse leave
    document.getElementById('singleStarRating').addEventListener('mouseleave', function() {
        const selectedRating = parseInt(ratingInput.value) || 0;

        stars.forEach((s, i) => {
            const icon = s.querySelector('i');
            if (i < selectedRating) {
                icon.className = 'fas fa-star';
                s.style.color = '#ffc107';
            } else {
                icon.className = 'far fa-star';
                s.style.color = '#ccc';
            }
        });
    });
}
</script>
