<?php
declare(strict_types=1);

namespace App\Controller;

use App\Utility\RecentlyViewedHelper;
use Cake\Controller\Controller;
use Cake\Core\Configure;
use Cake\Http\Exception\NotFoundException;
use Cake\ORM\TableRegistry;
use Cake\Routing\Router;
use Cake\Utility\Text;

/**
 * Users Controller
 */
class HomeController extends Controller
{
    protected $Cities;
    protected $Categories;
    protected $Users;
    protected $Customers;
    protected $OtpVerifications;
    protected $Banners;
    protected $BannerAds;
    protected $Offers;
    protected $Widgets;
    protected $Products;
    protected $ApiRequestLogs;
    protected $PaymentMethods;
    protected $Carts;
    protected $CartItems;
    protected $WidgetCategoryMappings;
    protected $Orders;
    protected $OrderItems;
    protected $SiteSettings;
    protected $Wishlists;
    protected $Reviews;
    protected $Brands;
    protected $CustomerAddresses;
    protected $ReviewImages;
    protected $ContentPages;
    protected $Invoices;
    protected $Transactions;
    protected $FaqCategories;
    protected $Faqs;
    protected $Wallets;
    protected $OrderCancellationCategories;
    protected $OrderCancellations;
    protected $OrderReturnCategories;
    protected $OrderReturns;
    protected $ProductImages;
    protected $OrderReturnImages;
    protected $supportCategories;
    protected $Contacts;
    protected $identity;
    protected $WebsiteFunctions;

    public function initialize(): void
    {
        parent::initialize();

        $this->loadComponent('WebsiteFunction');
        $this->loadComponent('Global');
        $this->loadComponent('Media');
        $this->loadComponent('CustomPaginator');
        $this->loadComponent('Flash');
        $this->viewBuilder()->setLayout('website');

        $guestToken = $this->request->getSession()->read('cartId') ?? null; // 7d1c54ba-05d8-4a5b-bad4-ed4381486858

        $this->identity = $this->request->getSession()->read('Auth.User');
        // Generate Guest Token if not logged in and no guest-token exists
        if (!$this->identity && !$guestToken) {
            $guestToken = Text::uuid();
            $this->request->getSession()->write('cartId', $guestToken);
        }

        $this->Cities = $this->fetchTable('Cities');
        $this->Categories = $this->fetchTable('Categories');
        $this->Users = $this->fetchTable('Users');
        $this->Customers = $this->fetchTable('Customers');
        $this->OtpVerifications = $this->fetchTable('OtpVerifications');
        $this->Banners = $this->fetchTable('Banners');
        $this->BannerAds = $this->fetchTable('BannerAds');
        $this->Offers = $this->fetchTable('Offers');
        $this->Widgets = $this->fetchTable('Widgets');
        $this->Products = $this->fetchTable('Products');
        $this->Reviews = $this->fetchTable('Reviews');
        $this->Brands = $this->fetchTable('Brands');
        $this->CustomerAddresses = $this->fetchTable('CustomerAddresses');
        $this->ApiRequestLogs = $this->fetchTable('ApiRequestLogs');
        $this->PaymentMethods = $this->fetchTable('PaymentMethods');
        $this->Carts = $this->fetchTable('Carts');
        $this->CartItems = $this->fetchTable('CartItems');
        $this->WidgetCategoryMappings = $this->fetchTable('WidgetCategoryMappings');
        $this->Orders = $this->fetchTable('Orders');
        $this->OrderItems = $this->fetchTable('OrderItems');
        $this->SiteSettings = $this->fetchTable('SiteSettings');
        $this->Wishlists = $this->fetchTable('Wishlists');
        $this->ReviewImages = $this->fetchTable('ReviewImages');
        $this->ContentPages = $this->fetchTable('ContentPages');
        $this->Transactions = $this->fetchTable('Transactions');
        $this->Invoices = $this->fetchTable('Invoices');
        $this->FaqCategories = $this->fetchTable('FaqCategories');
        $this->Faqs = $this->fetchTable('Faqs');
        $this->Wallets = $this->fetchTable('Wallets');
        $this->OrderCancellationCategories = $this->fetchTable('OrderCancellationCategories');
        $this->OrderCancellations = $this->fetchTable('OrderCancellations');
        $this->OrderReturnCategories = $this->fetchTable('OrderReturnCategories');
        $this->OrderReturns = $this->fetchTable('OrderReturns');
        $this->ProductImages = $this->fetchTable('ProductImages');
        $this->OrderReturnImages = $this->fetchTable('OrderReturnImages');
        $this->supportCategories = $this->fetchTable('SupportCategories');
    }
    public function beforeRender(\Cake\Event\EventInterface $event)
    {
        parent::beforeRender($event);
    }





    public function home()
    {
        $language = $this->request->getSession()->read('siteSettings.language') ?? 'English';      
        $widgets = $this->Widgets->homeWidgets($language);

        foreach ($widgets as $widget) {
            $products = [];

            $categoryInfo = $this->Widgets->getWidgetAllocatedCategoryIds($widget['id']);


            $categoryIds = array_column($categoryInfo, 'category_id');


            switch ($widget['widget_type']) {
                    case 'Featured':
                        $widget_type = 'featured';
                        $products = $this->Products->getFeaturedProducts($widget['no_of_product'], $categoryIds, $widget['product_preference'], $language);
                        break;
                    case 'Better Life':
                        $widget_type = 'better_life';
                        $products = $this->Products->getFeaturedProducts($widget['no_of_product'], $categoryIds, $widget['product_preference'], $language);
                        break;
                    case 'Banner':
                        $widget_type = 'banner';
                        $products = $this->Products->getFeaturedProducts($widget['no_of_product'], $categoryIds, $widget['product_preference'], $language);
                        break;
                    case 'Latest Offers':
                        $widget_type = 'latest_offers';
                        $products = $this->Products->getFeaturedProducts($widget['no_of_product'], $categoryIds, $widget['product_preference'], $language);
                        break;
                    case 'General Section':
                        $widget_type = 'general_section';
                        $products = $this->Products->getFeaturedProducts($widget['no_of_product'], $categoryIds, $widget['product_preference'], $language);
                        break;
                    case 'Customer Story':
                        $widget_type = 'customer_story';
                        $products = $this->Products->getFeaturedProducts($widget['no_of_product'], $categoryIds, $widget['product_preference'], $language);
                        break;
            }
            $modifiedProducts = [];
            $userId = null;
            if ($this->request->getSession()->read('Auth.User')) {
                $userId = $this->request->getSession()->read('Auth.User')['id'];
            }
          
            foreach ($products as $product) {
                 if(empty($product['product_image'])){
                    $product['product_image'] = '../../img/no-image.jpg';
                }else{
                    $product['product_image'] = $this->Media->getCloudFrontURL($product['product_image']);
                }
               
            }

            $response[$widget_type][] = [
                'widget' => $widget,
                'products' => $products,
                'category' => $categoryInfo,
            ];
        }

        $widgets = $response ?? [];

        $this->set(compact('widgets'));
        $this->viewBuilder()->setTemplatePath('Home');
        $this->render('index');
    }

    public function ajaxLoadMoreProduct()
    {
        // Fetch POST data instead of GET data
        $categoryId = $this->request->getData('category_id');
        $brandId = $this->request->getData('brand_id');
        $minPrice = $this->request->getData('min_price') ?: 0;
        $maxPrice = $this->request->getData('max_price') ?: 99999;
        $sortBy = $this->request->getData('sortBy');
        $page = $this->request->getData('page') ?: 1;
        $limit = $this->request->getData('limit') ?: 20; // Default limit

        $selectedBrands = $this->request->getData('brands') ? explode(',', $this->request->getData('brands')) : [];

        // Ensure $brandId is added to the array if not already present
        if (!in_array($brandId, $selectedBrands)) {
            $selectedBrands[] = $brandId;
        }

        $brandsString = implode('--', $selectedBrands);
        $price = $minPrice || $maxPrice ? "$minPrice--$maxPrice" : null;

        // API call for category list (unchanged)
        $apiCategoryListUrl = Configure::read('Settings.SITE_URL') . 'api/v1.0/categories.json';
        $categoryList = $this->callApi($apiCategoryListUrl);
        $categoryList = $categoryList->result->data;

        if (empty($categoryId)) {
            $categoryId = reset($categoryList)->id;
        }

        $categoryName = $this->Categories->find()->select(['name'])->where(['id' => $categoryId])->first()->name;

        // Brand List (unchanged)
        $BrandsList = $this->Brands->find()->select(['id', 'name'])->where('status', ['A'])->toArray();
        foreach ($BrandsList as &$brand) {
            if (!empty($brand['web_banner'])) {
                $brand['web_banner'] = $this->Media->getCloudFrontURL($brand['web_banner']);
            }
            if (!empty($brand['brand_logo'])) {
                $brand['brand_logo'] = $this->Media->getCloudFrontURL($brand['brand_logo']);
            }
        }
        unset($brand);

        if (empty($brandId)) {
            $brandId = reset($BrandsList)->id;
        }
        $brandDetailPage = $this->Brands->find()->where(['id' => $brandId])->where('status', ['A'])->first();
        $activeFilters['Brands'] = $brandDetailPage->name;

        // Process the brand banners and logos
        if ($brandDetailPage) {
            if (!empty($brandDetailPage->web_banner)) {
                $brandDetailPage->web_banner = $this->Media->getCloudFrontURL($brandDetailPage->web_banner);
            }
            if (!empty($brandDetailPage->brand_logo)) {
                $brandDetailPage->brand_logo = $this->Media->getCloudFrontURL($brandDetailPage->brand_logo);
            }
            if (!empty($brandDetailPage->mobile_banner)) {
                $brandDetailPage->mobile_banner = $this->Media->getCloudFrontURL($brandDetailPage->mobile_banner);
            }
        }

        // Construct API parameters
        $params = "?sort=$sortBy&price=$price&brand=$brandsString&page=$page&limit=$limit";
        $apiProductListUrl = Configure::read('Settings.SITE_URL') . "api/v1.0/productList/$categoryId.json$params";
        $productList = $this->callApi($apiProductListUrl);
        $productList = $productList->result->data->products;


        $userId = null;
        if ($this->request->getSession()->read('Auth.User')) {
            $userId = $this->request->getSession()->read('Auth.User')['id'];
        }

        if($productList){
            foreach ($productList as &$product) {
                $product->thumb_image = $this->Media->getCloudFrontURL($product->thumb_image);
                $product->whishlist = $userId === null ? false : $this->Wishlists->whishListCheckSingle($userId, $product->id);
            }
        }

        // Build active filters for response (unchanged)
        $activeFilters = [];
        if ($this->request->getData('category')) {
            $activeFilters['Category'] = $this->request->getData('category');
        }
        if ($this->request->getData('rating')) {
            $activeFilters['Rating'] = $this->request->getData('rating') . ' Star Rating';
        }
        if ($this->request->getData('brands')) {
            $brandArr = explode(',', $this->request->getData('brands'));
            $selectedBrandList = $this->Brands->find()->select(['name'])->where('status', ['A'])->where(['id IN' => $brandArr])->all()->toArray();
            $BrandNames = array_map(fn ($brand) => $brand->name, $selectedBrandList);
            $activeFilters['Brands'] = $BrandNames;
        }
        if ($this->request->getData('min_price') || $this->request->getData('max_price')) {
            $minPrice = $this->request->getData('min_price') ?: '0';
            $maxPrice = $this->request->getData('max_price') ?: '99999';
            $activeFilters['Price'] = "$minPrice - $maxPrice";
        }
        if ($this->request->getData('sortBy')) {
            $activeFilters['sortBy'] = $this->request->getData('sortBy') ?: '';
        }
        if ($this->request->getData('rating')) {
            $activeFilters['rating'] = $this->request->getData('rating') ?: '';
        }

        // Return the response as JSON
        $this->response = $this->response->withType('application/json');

        return $this->response->withStringBody(json_encode([
            'status' => 'success',
            'message' => 'Records fetched successfully!',
            'data' => $productList,
            'filters' => $activeFilters,
            'page' => $page,
        ]));
    }
   public function ajaxLoadMoreStoreProduct()
    {
        // Fetch POST data instead of GET data
        $categoryId = $this->request->getData('category_id');
        $brandId = $this->request->getData('brand_id');
        $minPrice = $this->request->getData('min_price') ?: 0;
        $maxPrice = $this->request->getData('max_price') ?: 99999;
        $sortBy = $this->request->getData('sortBy');
        $page = $this->request->getData('page') ?: 1;
        $limit = $this->request->getData('limit') ?: 20; // Default limit

        $selectedBrands = $this->request->getData('brands') ? explode(',', $this->request->getData('brands')) : [];

        // Ensure $brandId is added to the array if not already present
        if (!in_array($brandId, $selectedBrands)) {
            $selectedBrands[] = $brandId;
        }

        $brandsString = implode('--', $selectedBrands);
        $price = $minPrice || $maxPrice ? "$minPrice--$maxPrice" : null;

        // API call for category list (unchanged)
        $apiCategoryListUrl = Configure::read('Settings.SITE_URL') . 'api/v1.0/categories.json';
        $categoryList = $this->callApi($apiCategoryListUrl);
        $categoryList = $categoryList->result->data;

        if (empty($categoryId)) {
            $categoryId = reset($categoryList)->id;
        }

        $categoryName = $this->Categories->find()->select(['name'])->where(['id' => $categoryId])->first()->name;

        // Brand List (unchanged)
        $BrandsList = $this->Brands->find()->select(['id', 'name'])->where('status', ['A'])->toArray();
        foreach ($BrandsList as &$brand) {
            if (!empty($brand['web_banner'])) {
                $brand['web_banner'] = $this->Media->getCloudFrontURL($brand['web_banner']);
            }
            if (!empty($brand['brand_logo'])) {
                $brand['brand_logo'] = $this->Media->getCloudFrontURL($brand['brand_logo']);
            }
        }
        unset($brand);

        if (empty($brandId)) {
            $brandId = reset($BrandsList)->id;
        }
        $brandDetailPage = $this->Brands->find()->where(['id' => $brandId])->where('status', ['A'])->first();
        $activeFilters['Brands'] = $brandDetailPage->name;

        // Process the brand banners and logos
        if ($brandDetailPage) {
            if (!empty($brandDetailPage->web_banner)) {
                $brandDetailPage->web_banner = $this->Media->getCloudFrontURL($brandDetailPage->web_banner);
            }
            if (!empty($brandDetailPage->brand_logo)) {
                $brandDetailPage->brand_logo = $this->Media->getCloudFrontURL($brandDetailPage->brand_logo);
            }
            if (!empty($brandDetailPage->mobile_banner)) {
                $brandDetailPage->mobile_banner = $this->Media->getCloudFrontURL($brandDetailPage->mobile_banner);
            }
        }

        // Construct API parameters
        $params = "?sort=$sortBy&price=$price&brand=$brandsString&page=$page&limit=$limit";
        $apiProductListUrl = Configure::read('Settings.SITE_URL') . "api/v1.0/productList/$categoryId.json$params";
        $productList = $this->callApi($apiProductListUrl);
        $productList = $productList->result->data->products;

        $userId = null;
        if ($this->request->getSession()->read('Auth.User')) {
            $userId = $this->request->getSession()->read('Auth.User')['id'];
        }

        if($productList){
            foreach ($productList as &$product) {
                $product->whishlist = $userId === null ? false : $this->Wishlists->whishListCheckSingle($userId, $product->id);
            }
        }
        // Build active filters for response (unchanged)
        $activeFilters = [];
        if ($this->request->getData('category')) {
            $activeFilters['Category'] = $this->request->getData('category');
        }
        if ($this->request->getData('rating')) {
            $activeFilters['Rating'] = $this->request->getData('rating') . ' Star Rating';
        }
        if ($this->request->getData('brands')) {
            $brandArr = explode(',', $this->request->getData('brands'));
            $selectedBrandList = $this->Brands->find()->select(['name'])->where('status', ['A'])->where(['id IN' => $brandArr])->all()->toArray();
            $BrandNames = array_map(fn ($brand) => $brand->name, $selectedBrandList);
            $activeFilters['Brands'] = $BrandNames;
        }
        if ($this->request->getData('min_price') || $this->request->getData('max_price')) {
            $minPrice = $this->request->getData('min_price') ?: '0';
            $maxPrice = $this->request->getData('max_price') ?: '99999';
            $activeFilters['Price'] = "$minPrice - $maxPrice";
        }
        if ($this->request->getData('sortBy')) {
            $activeFilters['sortBy'] = $this->request->getData('sortBy') ?: '';
        }
        if ($this->request->getData('rating')) {
            $activeFilters['rating'] = $this->request->getData('rating') ?: '';
        }

        // Return the response as JSON
        $this->response = $this->response->withType('application/json');

        return $this->response->withStringBody(json_encode([
            'status' => 'success',
            'message' => 'Records fetched successfully!',
            'data' => $productList,
            'filters' => $activeFilters,
            'page' => $page,
        ]));
    }

    /**
     * AJAX endpoint for homepage featured products tab filtering
     */
    public function ajaxHomeFeaturedProducts()
    {
        $this->request->allowMethod(['post']);

        $categoryId = $this->request->getData('category_id');
        $limit = (int)$this->request->getData('limit', 6);

        // Get language from session
        $language = $this->request->getSession()->read('siteSettings.language') ?? 'English';
  
        // Fetch featured products for the given category
        $products = $this->Products->getHomepageFeaturedProducts($categoryId, $limit, $language);

            foreach ($products as $product) {
                 if(empty($product['product_image'])){
                    $product['product_image'] = '../../img/no-image.jpg';
                }else{
                    $product['product_image'] = $this->Media->getCloudFrontURL($product['product_image']);
                }
               
            }

        // Add image URLs if needed (already handled in model)
        return $this->response
            ->withType('application/json')
            ->withStringBody(json_encode([
                'status' => 'success',
                'data' => $products
            ]));
    }

    public function productList($categoryUrlKey = null)
    {
        // Get brands for filter display
        $brands = $this->Brands->GetBrands();
        $subCategoriesLevel = $this->Categories->subCategoriesLevel();

        // Process brand filter from URL
        $brandsFilter = '';
        if (!empty($this->request->getQuery('brands'))) {
            $brandIds = explode(',', $this->request->getQuery('brands'));
            $brandsFilter = implode('--', $brandIds);
        }

        // Category Start
        $categories = $this->Categories->getSingleProductCategory();
        $selectedCategory = $this->getfirstCategory($categories, $categoryUrlKey);
        //    dd($selectedCategory);
        // category End

        // sort by dropdwon section
        $sortBy = $this->request->getQuery('sort', 'popularity');
        // sort by end
        $params = [
            'category' => $selectedCategory['categoryId'], // $this->request->getQuery('category_id', 1),
            'sort' => $sortBy, // $this->request->getQuery('sortBy', 'newest'),
            'brand' => $brandsFilter,
            'price' => $this->request->getQuery('min_price', '0') . '--' . $this->request->getQuery('max_price', '99999'),
            'price_discount' => null,
            'rating' => $this->request->getQuery('rating'),
            'page' => $this->request->getQuery('page', 1),
            'limit' => $this->request->getQuery('limit', 6),
            'attribute_filter' => $this->request->getQuery('attributes', []),
            'filtercategory' => '', // explode(',', $this->request->getQuery('filtercategory', ''))
            'product_brands' => 0 // explode(',', $this->request->getQuery('product_brands', ''))
        ];
       
        $productList = $this->Products->optimizedProductList($params);
        
      
        $productList['params'] = $params;

        // Add wishlist information for both logged-in and guest users
        $session = $this->request->getSession();
        $identity = $session->read('Auth.User');
        $guestToken = $session->read('GuestToken');
        $customerId = null;

        // Get customer ID if user is logged in
        if ($identity && isset($identity->id) && !empty($identity->id)) {
            $user = $this->Users->find()
                ->contain(['Customers' => function ($q) {
                    return $q->select(['id']);
                }])
                ->select(['Users.id'])
                ->where(['Users.status' => 'A', 'Users.id' => $identity->id])
                ->first();

            if ($user && $user->customer) {
                $customerId = $user->customer->id;
            }
        }

        if (!empty($productList['data'])) {
            // Load Wishlists table
           
            foreach ($productList['data'] as &$product) {
                // Check wishlist for both customer and guest users
                $product->whishlist = $this->Wishlists->isInWishlist($customerId, $guestToken, $product->id);
                $product->thumb_image = $this->Media->getCloudFrontURL($product->thumb_image);
            }
        }

        // Check if this is an AJAX request for lazy loading
        if ($this->request->is('ajax')) {
            return $this->response
                ->withType('application/json')
                ->withStringBody(json_encode([
                    'status' => 'success',
                    'data' => $productList['data'],
                    'current_page' => $productList['current_page'],
                    'next_page' => $productList['next_page'],
                    'total_count' => $productList['total_count'],
                    'available_brands' => $productList['available_brands'] ?? [],
                    'params' => $params
                ]));
        }

        // If selectedCategory is empty, use the first category's url_key
        if (empty($selectedCategory['selectedCategory']) && !empty($categories)) {
            $firstCategory = reset($categories);
            if (isset($firstCategory->url_key)) {
                $firstCategory = $this->getfirstCategory($categories, $firstCategory->url_key);
            }
        }else{
                $firstCategory = $this->getfirstCategory($categories, $categoryUrlKey);
        }
        // $selectedCategory is not empty, continue as normal
        $this->set(compact('firstCategory','productList', 'categories', 'categoryUrlKey', 'selectedCategory', 'sortBy', 'brands'));
        $this->viewBuilder()->setTemplatePath('Home');
        $this->render('list');
    }

    // used in 1. product list page.
    private function getfirstCategory($categories, $categoryUrlKey = null)
    {
        $selectedCategory = null;

        foreach ($categories as $category) {
            if ($categoryUrlKey && strtolower($category->url_key) == strtolower($categoryUrlKey)) {
                $selectedCategory = $category;
                break;
            }
        }
        if (!$selectedCategory) {
            $selectedCategory = array_values(array_map(fn($category) => $category->id, $categories));
            return ['categoryId' => $selectedCategory, 'selectedCategory' => null];
        }
        // if (!$selectedCategory) {
        //     $airConditioner = array_filter($categories, function ($category) {
        //         return strtolower($category->name) === 'air conditioner';
        //     });

        //     $selectedCategory = !empty($airConditioner) ? reset($airConditioner) : reset($categories);
        // }

        if (!$selectedCategory && !empty($categories)) {
            $selectedCategory = $categories->first();
        }

        if (!$selectedCategory && !empty($categories)) {
            $selectedCategory = reset($categories);
        }

        return ['categoryId' => [$selectedCategory->id], 'selectedCategory' => $selectedCategory];
    }

    /**
     * AJAX endpoint for lazy loading products
     */
    public function ajaxLoadMoreProducts()
    {
        $this->request->allowMethod(['post', 'get']);

        // Get filter parameters from request
        $categoryId = $this->request->getData('category_id', $this->request->getQuery('category_id', 1));
        $page = (int)$this->request->getData('page', $this->request->getQuery('page', 1));
        $limit = (int)$this->request->getData('limit', $this->request->getQuery('limit', 20));
        $sortBy = $this->request->getData('sortBy', $this->request->getQuery('sortBy', 'newest'));
        $minPrice = $this->request->getData('min_price', $this->request->getQuery('min_price', 0));
        $maxPrice = $this->request->getData('max_price', $this->request->getQuery('max_price', 99999));
        $rating = $this->request->getData('rating', $this->request->getQuery('rating'));
        // Process brand filter
        $brandsFilter = '';
        $brands = $this->request->getData('brands', $this->request->getQuery('brands', ''));
        if (!empty($brands)) {
            $brandIds = explode(',', $brands);
            $brandsFilter = implode('--', $brandIds);
        }

        // Get attribute filters
        $attributeFilters = $this->request->getData('attributes', $this->request->getQuery('attributes', ''));

        // Build parameters for optimizedProductList
        $params = [
            'category' => $categoryId,
            'sort' => $sortBy,
            'brand' => $brandsFilter,
            'price' => $minPrice . '--' . $maxPrice,
            'price_discount' => null,
            'rating' => $rating,
            'page' => $page,
            'limit' => $limit,
            'attribute_filter' => $attributeFilters,
            'filtercategory' => $this->request->getData('filtercategory', $this->request->getQuery('filtercategory', ''))
        ];

        // Get products using the optimizedProductList method
        $productList = $this->Products->optimizedProductList($params);

        // Add wishlist information for both logged-in and guest users
        $session = $this->request->getSession();
        $identity = $session->read('Auth.User');
        $guestToken = $session->read('GuestToken');
        $customerId = null;

        // Get customer ID if user is logged in
        if ($identity && isset($identity->id) && !empty($identity->id)) {
            $user = $this->Users->find()
                ->contain(['Customers' => function ($q) {
                    return $q->select(['id']);
                }])
                ->select(['Users.id'])
                ->where(['Users.status' => 'A', 'Users.id' => $identity->id])
                ->first();

            if ($user && $user->customer) {
                $customerId = $user->customer->id;
            }
        }

        if (!empty($productList['data'])) {
            foreach ($productList['data'] as &$product) {
                // Check wishlist for both customer and guest users
                $product->thumb_image = $this->Media->getCloudFrontURL($product->thumb_image);
                $product->whishlist = $this->Wishlists->isInWishlist($customerId, $guestToken, $product->id);
            }
        }

        // Store the params in the productList array
        $productList['params'] = $params;

        // Return JSON response
        return $this->response
            ->withType('application/json')
            ->withStringBody(json_encode([
                'status' => 'success',
                'message' => 'Products loaded successfully',
                'data' => $productList['data'],
                'current_page' => $productList['current_page'],
                'next_page' => $productList['next_page'],
                'total_count' => $productList['total_count'],
                'available_brands' => $productList['available_brands'] ?? [],
                'filtered_count' => $productList['filtered_count'],
                'params' => $params
            ]));
    }


    public function product($product = null)
    {
        if (!$product) {
            return $this->redirect(['controller' => 'Website', 'action' => 'home']);
        }

        // check country ?country from url
        $country = $this->request->getQuery('country');
        $language = $this->request->getQuery('language') ?? 'eng';
        if (strtolower($language) === 'english') {
            $language = 'eng';
        }
        if(isset($country) && !empty($country)) {
             $settings = $this->WebsiteFunction->setSiteSettings($country, $language);
        }

		$productData = $this->Products->findProductByIdOrUrlKey($product);
        if ($productData) {
            $product = $productData->id;
        }

        // Get session data for both logged-in and guest users
        $session = $this->request->getSession();
        $identity = $session->read('Auth.User');
        $guestToken = $session->read('GuestToken');
        $customerId = null;
        $userId = null;

        // Get customer ID if user is logged in
        if ($identity && isset($identity->id) && !empty($identity->id)) {
            $userId = $identity->id;

            // Use the same pattern as in other controllers
            $user = $this->Users->find()
                ->contain(['Customers'])
                ->where(['Users.status' => 'A'])
                ->where(['Users.id' => $userId])
                ->first();

            if ($user && $user->customer) {
                $customerId = $user->customer->id;
            }
        }

        // Get product data with cart and wishlist information
        $productData = $this->Products->productView($product, $customerId, $guestToken);

        if (!$productData) {
            return $this->redirect('/');
        }

        if(!empty($productData->catalogue))
        {
            $productData->catalogue = $this->Media->getCloudFrontURL($productData->catalogue);
        }
        // Process main product images
        if(!empty($productData->product_images))
        {
            foreach ($productData->product_images as &$image) {
                if (!empty($image->image)) {
                    $image->image = $this->Media->getCloudFrontURL($image->image);
                }
                if (!empty($image->video)) {
                    $image->video = $this->Media->getCloudFrontURL($image->video);
                }
            }
            unset($image); // Break the reference to avoid issues
        }

        // Process main product image
        if(!empty($productData->product_image))
        {
            $productData->product_image = $this->Media->getCloudFrontURL($productData->product_image);
        }

        // Process related products images
        if(!empty($productData->related_products))
        {
            foreach ($productData->related_products as &$related_product) {
                if (!empty($related_product->id)) {
                    $image = $this->ProductImages->getDefaultProductImage($related_product->id);
                    $related_product->product_image = $image ? $this->Media->getCloudFrontURL($image) : '';
                }
            }
            unset($related_product); // Break the reference to avoid issues
        }

        $this->set(compact('productData', 'customerId'));
        $this->viewBuilder()->setTemplatePath('Home');
        $this->render('product');
    }

    /**
     * AJAX endpoint to fetch product reviews with filters and pagination
     */
    public function ajaxProductReviews()
    {
        $this->request->allowMethod(['post', 'get']);
        $productId = $this->request->getData('product_id', $this->request->getQuery('product_id'));
        $sortBy = $this->request->getData('sortBy', $this->request->getQuery('sortBy', 'newest'));
        $page = (int)$this->request->getData('page', $this->request->getQuery('page', 1));
        $limit = (int)$this->request->getData('limit', $this->request->getQuery('limit', 2));

        if (!$productId) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => 'Product ID is required',
                    'data' => [],
                ]));
        }

        $result = $this->review($productId, $sortBy, $page, $limit);

        // If $result is a Cake\Http\Response, decode its body
        if ($result instanceof \Cake\Http\Response) {
            $body = $result->getBody();
            $body->rewind();
            $result = json_decode($body->getContents(), true);
        }

        $reviews = [];
        $pagination = [];
        if (isset($result['data']['items'])) {
            $reviews = $result['data']['items'];
            $pagination = $result['data']['pagination'] ?? [];
        }

        return $this->response->withType('application/json')
            ->withStringBody(json_encode([
                'status' => 'success',
                'data' => $reviews,
                'pagination' => $pagination,
            ]));
    }

    public function review($id, $sortBy = 'newest', $page = 1, $limit = 2)
    {
        $rating_reviews = $this->Reviews->webProductReviews($id, $sortBy, $page, $limit);
        $this->paginate = [
            'limit' => $limit,
            'order' => ['Reviews.created' => 'DESC'],
        ];

        try {
            $reviews = $this->CustomPaginator->paginate($rating_reviews, [
                'limit' => $limit,
                'page' => $page,
            ]);

            if (!empty($reviews['items'])) {
                foreach ($reviews['items'] as &$rating_review) {
                    if ($rating_review['Customers']['profile_photo']) {
                        $rating_review['Customers']['profile_photo'] = $this->Media->getCloudFrontURL($rating_review['Customers']['profile_photo']);
                    }
                }
                unset($rating_review);
            }
            $result = [
                'status' => __('success'),
                'code' => 200,
                'data' => $reviews, // includes 'items' and 'pagination'
            ];

            if ($this->request->is('ajax')) {
                $this->response = $this->response->withType('application/json')
                    ->withStringBody(json_encode($result));
                return $this->response;
            }

            return $result;
        } catch (NotFoundException $e) {
            $result = [
                'status' => __('error'),
                'code' => 404,
                'message' => __('No reviews found.'),
                'data' => [],
            ];

            if ($this->request->is('ajax')) {
                $this->response = $this->response->withType('application/json')
                    ->withStringBody(json_encode($result));
                return $this->response;
            }

            return $result;
        }
    }

    public function contactUs()
    {
        if ($this->request->is('post')) {
            $this->autoRender = false;
            $data = $this->request->getData();

            // Validate required fields
            if (empty($data['name']) || empty($data['email']) || empty($data['message']) || empty($data['inquiry_type'])) {
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode([
                        'status' => 'error',
                        'message' => __('Please fill all required fields.')
                    ]));
            }

            // Save to contacts table
            $this->Contacts = $this->fetchTable('Contacts');
            $contact = $this->Contacts->newEmptyEntity();
            $contact->name = $data['name'];
            $contact->email = $data['email'];
            $contact->phone = $data['phone'] ?? '';
            $contact->inquiry_type = $data['inquiry_type'];
            $contact->message = $data['message'];
            $supportEmail = $this->getRequest()->getSession()->read('siteSettings.support_email');
            $adminEmails = Configure::read('Settings.ADMIN_EMAILS');
            $to = $supportEmail;
            $cc = $supportEmail;
            $subject = 'New Contact Inquiry from ' . $data['name'];
            $template = 'contact_us';
            $viewVars = [
                'customer_name' => $data['name'],
                'customer_email' => $data['email'],
                'customer_phone' => $data['phone'] ?? '',
                'inquiry_type' => $data['inquiry_type'],
                'message' => $data['message'],
            ];

            $this->Global->send_email($to, null, $subject, $template, $viewVars, null, $cc);
           
            if ($this->Contacts->save($contact)) {
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode([
                        'status' => 'success',
                        'message' => __('Thank you for your message! We will get back to you soon.')
                    ]));
            } else { 
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode([
                        'status' => 'error',
                        'message' => __('Unable to submit your message. Please try again.')
                    ]));
            }
        }

        $this->viewBuilder()->setTemplatePath('Home');
        $this->render('contact_us');
    }

    /**
     * Send email notification for contact form submission
     *
     * @param array $data Form data
     * @return bool
     */
    private function sendContactEmail($data)
    {
        try {
            // Validate required fields
            if (empty($data['first_name']) || empty($data['last_name']) ||
                empty($data['email']) || empty($data['subject']) || empty($data['message'])) {
                return false;
            }

            // Get site settings for email configuration
            $supportEmail = $this->request->getSession()->read('siteSettings.support_email');
            $siteName = $this->request->getSession()->read('siteSettings.site_title');

            if (empty($supportEmail)) {
                return false;
            }

            // Prepare email content
            $subject = sprintf('[%s] New Contact Inquiry - %s', $siteName, $data['subject']);

            $message = sprintf(
                "New contact inquiry received:\n\n" .
                "Name: %s %s\n" .
                "Email: %s\n" .
                "Phone: %s\n" .
                "Subject: %s\n" .
                "Message:\n%s\n\n" .
                "Submitted on: %s\n" .
                "IP Address: %s",
                $data['first_name'],
                $data['last_name'],
                $data['email'],
                $data['phone'] ?: 'Not provided',
                $data['subject'],
                $data['message'],
                date('Y-m-d H:i:s'),
                $this->request->clientIp()
            );

            // Send email using PHP's mail function
            $headers = [
                'From: ' . $data['email'],
                'Reply-To: ' . $data['email'],
                'X-Mailer: PHP/' . phpversion(),
                'Content-Type: text/plain; charset=UTF-8'
            ];

            return mail($supportEmail, $subject, $message, implode("\r\n", $headers));

        } catch (\Exception $e) {
            // Log error but don't fail the form submission
            error_log('Contact form email error: ' . $e->getMessage());
            return false;
        }
    }

    public function logout()
    {
        $this->request->getSession()->delete('Auth.User');

        return $this->redirect('/website/home/');
    }
    public function deletelogout()
    {
        $this->request->getSession()->delete('Auth.User');

        return $this->redirect('/customer/login/');
    }

    public function callApi($apiUrl)
    {
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_URL => $apiUrl,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
        ]);
        $response = curl_exec($curl);
        curl_close($curl);

        return json_decode($response);
    }

    public function search()
    {
        $this->autoRender = false;
        $this->response = $this->response->withType('application/json');

        $query = $this->request->getQuery('q');
        $products = $this->Products->searchWebAPI($query);

        // Convert products to array format for consistent data structure
        $formattedProducts = [];
        foreach ($products as $product) {
            // Convert entity to array
            $productArray = $product->toArray();

            // Add additional fields
            $productArray['rating'] = $this->Reviews->getAverageRating($product->id);
            $productArray['total_review'] = $this->Reviews->getTotalReviews($product->id);
            $productArray['discount'] = $this->Products->getDiscount($product->id);
            $productArray['slug_url'] = $product->url_key;  // Include slug_url in response
            $productArray['url'] = $product->url_key;  // Also include url field for frontend compatibility

            // Format categories
            $formattedCategories = [];
            if (!empty($product->product_categories)) {
                foreach ($product->product_categories as $productCategory) {
                    $formattedCategories[] = [
                        'id' => $productCategory->id,
                        'product_id' => $productCategory->product_id,
                        'category_id' => $productCategory->category_id,
                        'level' => $productCategory->level,
                        'category_name' => $productCategory->category->name
                    ];
                }
            }
            $productArray['product_categories'] = $formattedCategories;

            // Set product image
            $productArray['product_image'] = '';
            $image = $this->ProductImages->getDefaultProductImage($product->id);
            if ($image) {
                $productArray['product_image'] = $this->Media->getCloudFrontURL($image);
            } else {
                $productArray['product_image'] = $this->Media->getCloudFrontURL($image);
            }

            $formattedProducts[] = $productArray;
        }
        return $this->response->withStringBody(json_encode(['products' => $formattedProducts]));
    }

    public function cmsPage($slug = null)
    {

        if (empty($slug)) {
            $path = $this->request->getPath();
            if (strpos($path, '/cms/') === 0) {
                $slug = substr($path, 5); // Remove '/cms/' from the beginning
            }
        }

        if (empty($slug)) {
            throw new NotFoundException(__('Page not found'));
        }

        $page = $this->ContentPages->find()
        ->where([
            'ContentPages.status' => 'A',
            'ContentPages.published' => 'Yes',
            'OR' => [
                'ContentPages.url_key' => $slug,
                'ContentPages.title' => $slug
            ]
        ])
        ->first();

        if (empty($page)) {
            throw new NotFoundException(__('Page not found'));
        }

        // Set meta tags for SEO
        if (!empty($page->seo_title)) {
            $this->set('meta_title', $page->seo_title);
        }
        if (!empty($page->meta_description)) {
            $this->set('meta_description', $page->meta_description);
        }
        if (!empty($page->seo_keyword)) {
            $this->set('meta_keywords', $page->seo_keyword);
        }

        $this->set(compact('page'));
        $this->viewBuilder()
            ->setTemplatePath('Website')
            ->setTemplate('cms_page');
    }

    public function creditPayment($productId = null)
    {
        // Redirect if productId is empty
        if (empty($productId) OR empty($this->identity)) {
            return $this->redirect(['controller' => 'Website', 'action' => 'Home']); // Change as needed
        }
        $information = null; //$this->ProductPaymentSettings->getCreditPartners($productId);

        if (empty($information)) {
            return $this->redirect(['controller' => 'Website', 'action' => 'Home']); // Change as needed
        }

        if(isset($information[0]['product']['product_price'])){
            $information[0]['product']['promotion_price'] = $this->Products->getProductPrice($productId);
        }

        $this->set(compact('information'));
        $this->viewBuilder()
            ->setTemplatePath('Home')
            ->setTemplate('paybycredit');
    }

    public function shopList()
    {
        $showrooms = null;

        $this->set(compact('showrooms'));
        $this->viewBuilder()
            ->setTemplatePath('Website')
            ->setTemplate('shops');
    }

    public function getSiteSettings()
    {
        $this->autoRender = false;
        $this->response = $this->response->withType('application/json');

        if ($this->request->is('post')) {
            $data = $this->request->getData();

            $lang = strtolower($data['lang'] ?? 'eng');
            $lang = ($lang === 'english') ? 'eng' : $lang;

            $country = $data['country'] ?? 'Qatar';
            

            // Use the WebsiteFunction component to set and get settings
            $settings = $this->WebsiteFunction->setSiteSettings($country, $lang);

            return $this->response->withStringBody(json_encode([
                'status' => 'success',
                'settings' => $settings
            ]));
        }

        return $this->response->withStringBody(json_encode([
            'status' => 'error',
            'message' => 'Invalid request method'
        ]));
    }

    public function aboutUs()
    {
        // Get language from session
        $language = $this->request->getSession()->read('siteSettings.language');
        $language = $language == 'English' ? 'English' : 'Arabic';

        // Fetch CMS content for About Us page
        $cmsContent = $this->ContentPages->find()
            ->where([
                'ContentPages.status' => 'A',
                'ContentPages.published' => 'Yes',
                'ContentPages.url_key' => 'about'
            ])
            ->first();

        if (empty($cmsContent)) {
            throw new NotFoundException(__('About Us page not found'));
        }

        // Set content based on language preference
        if ($language == 'Arabic' && !empty($cmsContent->content_ar)) {
            $cmsContent->content = $cmsContent->content_ar;
        }

        // Set SEO meta tags
        if (!empty($cmsContent->meta_title)) {
            $this->set('meta_title', $cmsContent->meta_title);
        }
        if (!empty($cmsContent->meta_description)) {
            $this->set('meta_description', $cmsContent->meta_description);
        }
        if (!empty($cmsContent->meta_keyword)) {
            $this->set('meta_keywords', $cmsContent->meta_keyword);
        }

        // Set page title based on language
        if ($language == 'Arabic' && !empty($cmsContent->title_ar)) {
            $pageTitle = $cmsContent->title_ar;
        } else {
            $pageTitle = $cmsContent->title ?? 'About Us';
        }
        $this->set('pageTitle', $pageTitle);
        
        $this->set(compact('cmsContent'));
        $this->viewBuilder()->setTemplatePath('Home');
        $this->render('about_us');
    }
     

     public function termsConditions()
    {
        // Get language from session
        $language = $this->request->getSession()->read('siteSettings.language');
        $language = $language == 'English' ? 'English' : 'Arabic';
        
        // Fetch CMS content for About Us page
        $cmsContent = $this->ContentPages->find()
            ->where([
                'ContentPages.status' => 'A',
                'ContentPages.published' => 'Yes',
                'ContentPages.url_key' => 'terms'
            ])
            ->first();

        if (empty($cmsContent)) {
            throw new NotFoundException(__('Terms & Conditions page not found'));
        }

        // Set content based on language preference
        if ($language == 'Arabic' && !empty($cmsContent->content_ar)) {
            $cmsContent->content = $cmsContent->content_ar;
        }

        // Set SEO meta tags
        if (!empty($cmsContent->meta_title)) {
            $this->set('meta_title', $cmsContent->meta_title);
        }
        if (!empty($cmsContent->meta_description)) {
            $this->set('meta_description', $cmsContent->meta_description);
        }
        if (!empty($cmsContent->meta_keyword)) {
            $this->set('meta_keywords', $cmsContent->meta_keyword);
        }

        // Set page title based on language
        if ($language == 'Arabic' && !empty($cmsContent->title_ar)) {
            $pageTitle = $cmsContent->title_ar;
        } else {
            $pageTitle = $cmsContent->title ?? 'Terms & ';
        }
        $this->set('pageTitle', $pageTitle);

        $this->set(compact('cmsContent'));
        $this->viewBuilder()->setTemplatePath('Home');
        $this->render('terms_condition');
    }

     public function privacyPolicies()
    {
        // Get language from session
        $language = $this->request->getSession()->read('siteSettings.language');
        $language = $language == 'English' ? 'English' : 'Arabic';

        // Fetch CMS content for About Us page
        $cmsContent = $this->ContentPages->find()
            ->where([
                'ContentPages.status' => 'A',
                'ContentPages.published' => 'Yes',
                'ContentPages.url_key' => 'policies'
            ])
            ->first();

        if (empty($cmsContent)) {
            throw new NotFoundException(__('Privacy Policies page not found'));
        }

        // Set content based on language preference
        if ($language == 'Arabic' && !empty($cmsContent->content_ar)) {
            $cmsContent->content = $cmsContent->content_ar;
        }

        // Set SEO meta tags
        if (!empty($cmsContent->meta_title)) {
            $this->set('meta_title', $cmsContent->meta_title);
        }
        if (!empty($cmsContent->meta_description)) {
            $this->set('meta_description', $cmsContent->meta_description);
        }
        if (!empty($cmsContent->meta_keyword)) {
            $this->set('meta_keywords', $cmsContent->meta_keyword);
        }

        // Set page title based on language
        if ($language == 'Arabic' && !empty($cmsContent->title_ar)) {
            $pageTitle = $cmsContent->title_ar;
        } else {
            $pageTitle = $cmsContent->title ?? 'Terms & ';
        }
        $this->set('pageTitle', $pageTitle);

        $this->set(compact('cmsContent'));
        $this->viewBuilder()->setTemplatePath('Home');
        $this->render('privacy_policies');
    }
      public function refundPolicies()
    {
        // Get language from session
        $language = $this->request->getSession()->read('siteSettings.language');
        $language = $language == 'English' ? 'English' : 'Arabic';

        // Fetch CMS content for About Us page
        $cmsContent = $this->ContentPages->find()
            ->where([
                'ContentPages.status' => 'A',
                'ContentPages.published' => 'Yes',
                'ContentPages.url_key' => 'refund'
            ])
            ->first();

        if (empty($cmsContent)) {
            throw new NotFoundException(__('Refund Policies page not found'));
        }

        // Set content based on language preference
        if ($language == 'Arabic' && !empty($cmsContent->content_ar)) {
            $cmsContent->content = $cmsContent->content_ar;
        }

        // Set SEO meta tags
        if (!empty($cmsContent->meta_title)) {
            $this->set('meta_title', $cmsContent->meta_title);
        }
        if (!empty($cmsContent->meta_description)) {
            $this->set('meta_description', $cmsContent->meta_description);
        }
        if (!empty($cmsContent->meta_keyword)) {
            $this->set('meta_keywords', $cmsContent->meta_keyword);
        }

        // Set page title based on language
        if ($language == 'Arabic' && !empty($cmsContent->title_ar)) {
            $pageTitle = $cmsContent->title_ar;
        } else {
            $pageTitle = $cmsContent->title ?? 'Refund & ';
        }
        $this->set('pageTitle', $pageTitle);

        $this->set(compact('cmsContent'));
        $this->viewBuilder()->setTemplatePath('Home');
        $this->render('refund_policies');
    }

    /**
     * Check if user has already reviewed a product
     */
    public function checkProductReview()
    {
        $this->request->allowMethod(['post', 'ajax']);

        $productId = $this->request->getData('product_id');
        $customerId = $this->request->getData('customer_id');

        if (!$productId || !$customerId) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'success' => false,
                'message' => __('Invalid request data.')
            ]));
        }

        // Additional validation: verify the customer ID belongs to the logged-in user
        $session = $this->request->getSession();
        $account = $session->read('Auth.User');

        if (!$account) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'success' => false,
                'message' => __('Please login to check review status.')
            ]));
        }

        // Get user with customer relationship to verify customer ID
        $usersTable = $this->fetchTable('Users');
        $user = $usersTable->find()
            ->contain(['Customers'])
            ->where(['Users.status' => 'A'])
            ->where(['Users.id' => $account->id])
            ->first();

        if (!$user || !$user->customer || $user->customer->id != $customerId) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'success' => false,
                'message' => __('Invalid customer information.')
            ]));
        }

        // Load OrderItemReviews table
        $orderItemReviewsTable = $this->fetchTable('OrderItemReviews');

        // Check if review exists for this product and customer
        $existingReview = $orderItemReviewsTable->find()
            ->where([
                'product_id' => $productId,
                'customer_id' => $customerId
            ])
            ->first();

        return $this->response->withType('application/json')->withStringBody(json_encode([
            'success' => true,
            'hasReview' => !empty($existingReview)
        ]));
    }


}

