<?php
declare(strict_types=1);

namespace App\Controller;

use App\Controller\AppController;
use Cake\Http\Exception\NotFoundException;
use Cake\Http\Exception\MethodNotAllowedException;

/**
 * OrderItemReviews Controller
 *
 * @property \App\Model\Table\OrderItemReviewsTable $OrderItemReviews
 */
class OrderItemReviewsController extends AppController
{
    protected $OrderItemReviews;
    protected $Products;
    protected $Customers;
    protected $Orders;

    public function initialize(): void
    {
        parent::initialize();
        
        $this->OrderItemReviews = $this->fetchTable('OrderItemReviews');
        $this->Products = $this->fetchTable('Products');
        $this->Customers = $this->fetchTable('Customers');
        $this->Orders = $this->fetchTable('Orders');
        
        $this->loadComponent('Global');
        $this->loadComponent('Flash');
        
        $this->viewBuilder()->setLayout('admin');
    }

    /**
     * Index method - List all reviews
     *
     * @return \Cake\Http\Response|null|void Renders view
     */
    public function index()
    {
        // Get filter parameters
        $status = $this->request->getQuery('status');
        $publishStatus = $this->request->getQuery('publish_status');
        $rating = $this->request->getQuery('rating');
        $search = $this->request->getQuery('search');
        $dateFrom = $this->request->getQuery('date_from');
        $dateTo = $this->request->getQuery('date_to');

        // Build query with filters
        $query = $this->OrderItemReviews->find()
            ->contain([
                'Customers' => ['Users'],
                'Products',
                'Orders',
                'OrderItems'
            ])
            ->order(['OrderItemReviews.created' => 'DESC']);

        // Apply filters
        if (!empty($status)) {
            $query->where(['OrderItemReviews.status' => $status]);
        }

        if (!empty($publishStatus)) {
            $query->where(['OrderItemReviews.publish_status' => $publishStatus]);
        }

        if (!empty($rating)) {
            $query->where(['OrderItemReviews.rating' => $rating]);
        }

        if (!empty($search)) {
            $query->where([
                'OR' => [
                    'Products.name LIKE' => '%' . $search . '%',
                    'OrderItemReviews.review LIKE' => '%' . $search . '%',
                    'Users.first_name LIKE' => '%' . $search . '%',
                    'Users.last_name LIKE' => '%' . $search . '%',
                    'Orders.order_number LIKE' => '%' . $search . '%'
                ]
            ]);
        }

        if (!empty($dateFrom)) {
            $query->where(['OrderItemReviews.created >=' => $dateFrom . ' 00:00:00']);
        }

        if (!empty($dateTo)) {
            $query->where(['OrderItemReviews.created <=' => $dateTo . ' 23:59:59']);
        }

        // Paginate results
        $this->paginate = [
            'limit' => 25,
            'order' => ['OrderItemReviews.created' => 'DESC']
        ];

        $reviews = $this->paginate($query);

        // Get statistics
        $stats = [
            'total' => $this->OrderItemReviews->find()->count(),
            'pending' => $this->OrderItemReviews->find()->where(['publish_status' => 'pending'])->count(),
            'published' => $this->OrderItemReviews->find()->where(['publish_status' => 'published'])->count(),
            'rejected' => $this->OrderItemReviews->find()->where(['publish_status' => 'rejected'])->count(),
            'average_rating' => $this->OrderItemReviews->find()
                ->select(['avg_rating' => $this->OrderItemReviews->find()->func()->avg('rating')])
                ->where(['status' => 'Active', 'publish_status' => 'published'])
                ->first()->avg_rating ?? 0
        ];

        $this->set(compact('reviews', 'stats', 'status', 'publishStatus', 'rating', 'search', 'dateFrom', 'dateTo'));
    }

    /**
     * View method - View single review
     *
     * @param string|null $id Review id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $review = $this->OrderItemReviews->get($id, [
            'contain' => [
                'Customers' => ['Users'],
                'Products',
                'Orders',
                'OrderItems'
            ]
        ]);

        $this->set(compact('review'));
    }

    /**
     * Edit method - Edit review details
     *
     * @param string|null $id Review id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {
        $review = $this->OrderItemReviews->get($id, [
            'contain' => [
                'Customers' => ['Users'],
                'Products',
                'Orders'
            ]
        ]);

        if ($this->request->is(['patch', 'post', 'put'])) {
            $review = $this->OrderItemReviews->patchEntity($review, $this->request->getData());
            
            if ($this->OrderItemReviews->save($review)) {
                $this->Flash->success(__('The review has been updated successfully.'));
                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The review could not be updated. Please, try again.'));
        }

        $statusOptions = [
            'Active' => __('Active'),
            'Inactive' => __('Inactive'),
            'Deleted' => __('Deleted')
        ];

        $publishStatusOptions = [
            'pending' => __('Pending'),
            'published' => __('Published'),
            'rejected' => __('Rejected')
        ];

        $this->set(compact('review', 'statusOptions', 'publishStatusOptions'));
    }

    /**
     * Delete method - Delete review
     *
     * @param string|null $id Review id.
     * @return \Cake\Http\Response|null|void Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $review = $this->OrderItemReviews->get($id);
        
        if ($this->OrderItemReviews->delete($review)) {
            $this->Flash->success(__('The review has been deleted successfully.'));
        } else {
            $this->Flash->error(__('The review could not be deleted. Please, try again.'));
        }

        return $this->redirect(['action' => 'index']);
    }

    /**
     * Bulk publish reviews
     */
    public function bulkPublish()
    {
        $this->request->allowMethod(['post']);
        $ids = $this->request->getData('ids');
        
        if (empty($ids)) {
            $this->Flash->error(__('No reviews selected.'));
            return $this->redirect(['action' => 'index']);
        }

        $count = $this->OrderItemReviews->updateAll(
            ['publish_status' => 'published'],
            ['id IN' => $ids]
        );

        $this->Flash->success(__('Successfully published {0} reviews.', $count));
        return $this->redirect(['action' => 'index']);
    }

    /**
     * Bulk reject reviews
     */
    public function bulkReject()
    {
        $this->request->allowMethod(['post']);
        $ids = $this->request->getData('ids');
        
        if (empty($ids)) {
            $this->Flash->error(__('No reviews selected.'));
            return $this->redirect(['action' => 'index']);
        }

        $count = $this->OrderItemReviews->updateAll(
            ['publish_status' => 'rejected'],
            ['id IN' => $ids]
        );

        $this->Flash->success(__('Successfully rejected {0} reviews.', $count));
        return $this->redirect(['action' => 'index']);
    }

    /**
     * Quick publish/reject action
     */
    public function updatePublishStatus()
    {
        $this->request->allowMethod(['post', 'ajax']);
        
        $id = $this->request->getData('id');
        $status = $this->request->getData('status');
        
        if (!$id || !in_array($status, ['pending', 'published', 'rejected'])) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'success' => false,
                'message' => __('Invalid data provided.')
            ]));
        }

        $review = $this->OrderItemReviews->get($id);
        $review->publish_status = $status;
        
        if ($this->OrderItemReviews->save($review)) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'success' => true,
                'message' => __('Review status updated successfully.')
            ]));
        }

        return $this->response->withType('application/json')->withStringBody(json_encode([
            'success' => false,
            'message' => __('Failed to update review status.')
        ]));
    }
}
