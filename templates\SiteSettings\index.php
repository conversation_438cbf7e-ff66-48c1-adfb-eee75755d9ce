<?php
/**
 * @var \App\View\AppView $this
 * @var iterable<\App\Model\Entity\SiteSetting> $siteSettings
 */
?>
<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/datatables.min.css') ?>">
<link rel="stylesheet"
    href="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/css/dataTables.bootstrap4.min.css') ?>">
<?php $this->end(); ?>
<div class="section-header">
    <ul class="breadcrumb breadcrumb-style ">
        <li class="breadcrumb-item">
            <a href="<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'index']) ?>">
                <h4 class="page-title m-b-0"><?= __("Dashboard") ?></h4>
            </a>
        </li>
        <li class="breadcrumb-item"><?= __("Site Settings") ?></li>
        <li class="breadcrumb-item active"><?= __("Global Settings") ?></li>
    </ul>
</div>
<div class="section-body1">
    <div class="container-fluid">
        <?= $this->Flash->render() ?>
    </div>
</div>
<div class="section-body" id="list">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <h4><?= __("Global Settings") ?></h4>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped" id="table-1">
                    <thead>
                        <tr>
                            <th><?= __("Id") ?></th>
                            <th><?= __("Site Title") ?></th>
                            <th><?= __("Country") ?></th>
                            <th><?= __("State") ?></th>
                            <th><?= __("City") ?></th>
                            <th><?= __("Zip Code") ?></th>
                            <th><?= __("Support Email") ?></th>
                            <th><?= __("Admin Email") ?></th>
                            <th class="actions"><?= __("Actions") ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($siteSettings as $siteSetting): ?>
                            <tr>
                                <td><?= h($siteSetting->id) ?></td>
                                <td><?= h($siteSetting->site_title) ?></td>
                                <td><?= h($siteSetting->country) ?></td>
                                <td><?= h($siteSetting->state) ?></td>
                                <td><?= h($siteSetting->city) ?></td>
                                <td><?= h($siteSetting->zipcode) ?></td>
                                <td><?= h($siteSetting->support_email) ?></td>
                                <td><?= h($siteSetting->admin_email) ?></td>
                                <td class="actions">
                                    <?php if ($canEdit): ?>
                                        <a href="<?= $this->Url->build(['controller' => 'SiteSettings', 'action' => 'edit', $siteSetting->id]) ?>"
                                            class="" data-toggle="tooltip" title="Edit"><i
                                            class="fas fa-pencil-alt m-r-10"></i></a>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/datatables/datatables.min.js') ?>"></script>
<script
    src="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/js/dataTables.bootstrap4.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script>

    var table = $("#table-1").DataTable({
        columnDefs: [
            { orderable: false, targets: -1 } 
        ],
        dom: 'rtip',
        info: false,
        paging: false,
        searching: false,
    });

    $('#customSearchBox').on('keyup', function () {
        table.search(this.value).draw();
    });

    document.addEventListener('DOMContentLoaded', function () {
        const filterButton = document.querySelector('.btn.menu-toggle');
        const filterBodyContainer = document.getElementById('filter-body-container');

        filterButton.addEventListener('click', function (event) {
            event.preventDefault(); // Prevent form submission if button is inside a form
            if (filterBodyContainer.classList.contains('showing')) {
                // If currently showing, trigger hiding animation
                filterBodyContainer.classList.remove('showing');
                filterBodyContainer.classList.add('hiding');
                filterBodyContainer.addEventListener('animationend', function () {
                    filterBodyContainer.classList.remove('hiding');
                    filterBodyContainer.style.display = 'none'; // Ensure it's hidden after animation
                }, { once: true });
            } else {
                // If currently hidden, trigger showing animation
                filterBodyContainer.style.display = 'block'; // Ensure it's visible before animation
                filterBodyContainer.classList.add('showing');
            }
        });
    });
</script>
<?php $this->end(); ?>