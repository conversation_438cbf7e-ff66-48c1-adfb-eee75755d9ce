<?php

/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Offer $offer
 */
?>
<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/bootstrap-tagsinput/dist/bootstrap-tagsinput.css') ?>">
<style>
    .content {
        color: black;
    }
</style>
<?php $this->end(); ?>
<div class="section-header d-flex justify-content-between align-items-center mb-3">
    <ul class="breadcrumb breadcrumb-style mb-0">
        <li class="breadcrumb-item">
            <a href="<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'index']) ?>">
                <h4 class="page-title m-b-0"><?= __('Dashboard') ?></h4>
            </a>
        </li>
        <li class="breadcrumb-item">
            <a href="<?= $this->Url->build(['controller' => 'Offers', 'action' => 'index']) ?>">
                <?= __('Coupons And Discounts') ?>
            </a>
        </li>
        <li class="breadcrumb-item active">
            <?= __('View') ?>
        </li>
    </ul>
    <a href="javascript:void(0);" class="d-flex align-items-center" id="back-button-mo" onclick="history.back();">
        <span class="rotate me-2">➥</span><small style="font-weight: bold"><?= __('BACK') ?></small>
    </a>
</div>

<h6 class="m-l-10 p-t-10 p-b-10" style="color: black;"> <?= __('View Coupon And Discount') ?></h6>

<div class="section-body">
    <div class="row">
        <div class="col-12 col-md-6 col-lg-12">
            <div class="card">
                <div class="card-body">
                    <div class="form-group row">
                        <label for="offer-name" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Offer Name ') ?>
                        </label>
                        <div class="col-sm-5 ps-5">
                            <p id="offer-name" name="offer-name"><?php echo !empty($offer->offer_name) ? h($offer->offer_name) : '-'; ?></p>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="offer-code" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Coupon Code ') ?>
                        </label>
                        <div class="col-sm-5 ps-5">
                            <p id="offer-code" name="offer-code"><?php echo !empty($offer->offer_code) ? h($offer->offer_code) : '-'; ?></p>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="offer-description" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Coupon Description ') ?>
                        </label>
                        <div class="col-sm-5 ps-5">
                            <p id="offer-description" name="offer-description"><?php echo !empty($offer->offer_description) ? h($offer->offer_description) : '-'; ?></p>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="offer-start-date" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Start Date & Time') ?>
                        </label>
                        <div class="col-sm-5 ps-5">
                            <p id="offer-start-date" name="offer-start-date"><?php echo !empty($offer->offer_start_date) ? h($offer->offer_start_date->format($dateFormat . ' ' . $timeFormat)) : '-'; ?></p>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="offer-end-date" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('End Date & Time') ?>
                        </label>
                        <div class="col-sm-5 ps-5">
                            <p id="offer-end-date" name="offer-end-date"><?php echo !empty($offer->offer_end_date) ? h($offer->offer_end_date->format($dateFormat . ' ' . $timeFormat)) : '-'; ?></p>
                        </div>
                    </div>

                

                    <div class="form-group row">
                        <label for="offer-type" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Offer Type ') ?>
                        </label>
                        <div class="col-sm-5 ps-5">
                            <p id="offer-type" name="offer-type"><?php echo !empty($offer->offer_type) ? h($offerType[$offer->offer_type]) : '-'; ?></p>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="discount" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Discount Amount / Percentage ') ?>
                        </label>
                        <div class="col-sm-5 ps-5">
                            <p id="discount" name="discount">
                                <?php
                                echo isset($offer->discount) && $offer->discount !== null
                                    ? h(number_format($offer->discount, 2, $decimalSeparator, $thousandSeparator) . ' ' . ($offer->offer_type == "Percentage" ? "%" : h($currencySymbol)))
                                    : '-';
                                ?>
                            </p>

                        </div>
                    </div>
                    <?php if ($offer->offer_type == "Percentage") { ?>
                        <div class="form-group row">
                            <label for="max-discount-perc-amount" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Maximum Amount for Percent Discount ') ?>
                            </label>
                            <div class="col-sm-5 ps-5">
                                <p>
                                    <?php
                                    echo isset($offer->max_amt_per_disc_value) && $offer->max_amt_per_disc_value !== null
                                        ? h(number_format($offer->max_amt_per_disc_value, 2, $decimalSeparator, $thousandSeparator) . ' ' .  h($currencySymbol))
                                        : '-';
                                    ?>
                                </p>

                            </div>
                        </div>
                    <?php } ?>

                    <div class="form-group row">
                        <label for="min-cart-value" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Minimum Cart Amount') ?>
                        </label>
                        <div class="col-sm-5 ps-5">
                            <p id="min-cart-value" name="min-cart-value">
                                <?php
                                echo isset($offer->min_cart_value) && $offer->min_cart_value !== null
                                    ? h(number_format($offer->min_cart_value, 2, $decimalSeparator, $thousandSeparator) . ' ' . $currencySymbol)
                                    : '-';
                                ?>
                            </P>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="terms-conditions" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Terms & Conditions') ?>
                        </label>
                        <div class="col-sm-5 ps-5">
                            <p id="terms-conditions" name="terms-conditions"><?php echo !empty($offer->terms_conditions) ? h($offer->terms_conditions) : '-'; ?></p>
                        </div>
                    </div>

                    <!-- <div class="form-group row">
                        <label for="free-shipping" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Free Shipping') ?>
                        </label>
                        <div class="col-sm-5 ps-5">
                            <p id="free-shipping" name="free-shipping"><?php echo ($offer->free_shipping == 1) ? 'Yes' : 'No'; ?></p>
                        </div>
                    </div> -->

                    <div class="form-group row">
                        <label for="country" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Country') ?>
                        </label>
                        <div class="col-sm-5 ps-5">
                            <p id="country" name="country"><?php echo !empty($selectedCountryName) ? h($selectedCountryName) : '-'; ?></p>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="status" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Status') ?>
                        </label>
                        <div class="col-sm-5 ps-5">
                            <p id="status" name="status"><?php echo !empty($offer->status) ? h($statuses[$offer->status]) : '-'; ?></p>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>