<?php
namespace App\View\Helper;

use Cake\View\Helper;

class <PERSON><PERSON><PERSON><PERSON><PERSON> extends Helper
{
    protected $metaData = [];

    public function set(array $data): void
    {
        $this->metaData = $data;
    }

    public function render(): string
    {
        $output = '';
        
        // Use $seo data from controller if available, otherwise use manually set data
        $seoData = $this->getView()->get('seo') ?? $this->metaData;

        if (!empty($seoData['title'])) {
            $output .= '<title>' . h($seoData['title']) . "</title>\n";
        }

        if (!empty($seoData['meta_title'])) {
            $output .= '<meta name="title" content="' . h($seoData['meta_title']) . "\">\n";
        }

        if (!empty($seoData['description'])) {
            $output .= '<meta name="description" content="' . h($seoData['description']) . "\">\n";
        }

        if (!empty($seoData['keywords'])) {
            $output .= '<meta name="keywords" content="' . h($seoData['keywords']) . "\">\n";
        }

        if (!empty($seoData['robots'])) {
            $output .= '<meta name="robots" content="' . h($seoData['robots']) . "\">\n";
        }

        // Open Graph meta tags
        if (!empty($seoData['og_title'])) {
            $output .= '<meta property="og:title" content="' . h($seoData['og_title']) . "\">\n";
        }

        if (!empty($seoData['og_description'])) {
            $output .= '<meta property="og:description" content="' . h($seoData['og_description']) . "\">\n";
        }
        if (!empty($seoData['google_analytics_script'])) {
            // Output raw script without escaping, as it's trusted HTML
            $output .= $seoData['google_analytics_script'] . "\n";
        }

        return $output;
    }
}
