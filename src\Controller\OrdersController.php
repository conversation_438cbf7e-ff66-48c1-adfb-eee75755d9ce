<?php

declare(strict_types=1);

namespace App\Controller;

use App\Controller\AppController;
use Cake\Core\Configure;
use DateTime;
use Mpdf\Mpdf;
use Cake\Http\Response;

/**
 * Orders Controller
 *
 * @property \App\Model\Table\OrdersTable $Orders
 */
class OrdersController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    protected \App\Model\Table\CustomersTable $Customers;
    protected \App\Model\Table\ProductsTable $Products;
    protected \App\Model\Table\UsersTable $Users;
    protected \App\Model\Table\ShowroomsTable $Showrooms;
    protected \App\Model\Table\TransactionsTable $Transactions;
    protected \App\Model\Table\CitiesTable $Cities;
    protected \App\Model\Table\MunicipalitiesTable $Municipalities;
    protected \App\Model\Table\ShowroomUsersTable $ShowRoomUsers;
    protected \App\Model\Table\CustomerAddressesTable $CustomerAddresses;
    protected \App\Model\Table\ShowroomUsersTable $ShowroomUsers;
    protected \App\Model\Table\SiteSettingsTable $SiteSettings;
    protected \App\Model\Table\ShipmentsTable $Shipments;
    protected \App\Model\Table\ShipmentOrdersTable $ShipmentOrders;
    protected \App\Model\Table\OrderCancellationsTable $OrderCancellations;
    protected \App\Model\Table\OrderCancellationCategoriesTable $OrderCancellationCategories;
    protected \App\Model\Table\OrderItemsTable $OrderItems;
    protected \App\Model\Table\OrderReturnsTable $OrderReturns;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');
        $this->Customers = $this->fetchTable('Customers');
        $this->Products = $this->fetchTable('Products');
        //$this->Showrooms = $this->fetchTable('Showrooms');
        $this->Transactions = $this->fetchTable('Transactions');
        $this->Cities = $this->fetchTable('Cities');
        //$this->Municipalities = $this->fetchTable('Municipalities');
        //$this->ShowRoomUsers = $this->fetchTable('ShowroomUsers');
        $this->CustomerAddresses = $this->fetchTable('CustomerAddresses');
        $this->Users = $this->fetchTable('Users');
        //$this->ShowroomUsers = $this->fetchTable('ShowroomUsers');
        $this->SiteSettings = $this->fetchTable('SiteSettings');
        //$this->Shipments = $this->fetchTable('Shipments');
       // $this->ShipmentOrders = $this->fetchTable('ShipmentOrders');
        $this->OrderCancellations = $this->fetchTable('OrderCancellations');
        $this->OrderCancellationCategories = $this->fetchTable('OrderCancellationCategories');
        $this->OrderItems = $this->fetchTable('OrderItems');
        $this->OrderReturns = $this->fetchTable('OrderReturns');
        $this->States = $this->fetchTable('States');
        $this->Countries = $this->fetchTable('Countries');
    }

    public function index()
    {

        $ShowroomManagerRoleId = Configure::read('Constants.SHOWROOM_MANAGER_ROLE_ID');
        $ShowroomSupervisorRoleId = Configure::read('Constants.SHOWROOM_SUPERVISOR_ROLE_ID');

        $user = $this->Authentication->getIdentity();
        $this->storeBased = $this->hasPermission($this->request->getParam('controller'), 'store');

        $OrderDetails = $this->Orders->getOrderDetails();
        if (!empty($user)) {
            if ($user->role_id == $ShowroomManagerRoleId && $this->storeBased) {
                $OrderDetails = $this->Orders->getOrderDetails($user->id, 'Showroom_Manager');
            } elseif ($user->role_id == $ShowroomSupervisorRoleId && $this->storeBased) {
                $OrderDetails = $this->Orders->getOrderDetails($user->id, 'Showroom_Supervisor');
            }
        }

        $orders = $OrderDetails->all();

        $orderstatuses = Configure::read('Constants.ORDER_STATUSES');
        $paymentstatuses = Configure::read('Constants.PAYMENT_STATUSES');
        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');

    
    
        $this->set(compact('orders', 'orderstatuses', 'paymentstatuses', 'dateFormat', 'timeFormat'));
    }

    /**
     * View method
     *
     * @param string|null $id Order id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        // $order = $this->Orders->get($id, contain: ['Customers', 'CustomerAddresses', 'Offers', 'Showrooms', 'OrderItems', 'Returns', 'Shipments', 'Transactions']);

        $order = $this->Orders->get($id, [
            'contain' => [
                'Customers' => ['Users'],
               // 'CustomerAddresses' => ['Cities', 'Municipalities'],
               'CustomerAddresses' => ['Cities'],
                'Countries',
                'Offers',
                // 'Showrooms' => ['Cities'],
                'OrderItems' => [
                    'Products' => [
                        'ProductImages' => function ($q) {
                            return $q->where(['image_default' => 1, 'status' => 'A']);
                        }
                    ],
                    'ProductVariants' => [
                        'ProductVariantImages' => function ($q) {
                            return $q->where(['image_default' => 1, 'status' => 'A']);
                        }
                    ]
                ],
                'OrderReturns',
                'OrderCancellations',
                'Transactions'
            ]
        ]);

        // Assign the CloudFront URL to product images
        foreach ($order->order_items as $item) {
            if (!empty($item->product->product_images)) {
                foreach ($item->product->product_images as $image) {
                    $image->image = $this->Media->getCloudFrontURL($image->image);
                }
            }
            if (!empty($item->product_variant->product_variant_images)) {
                foreach ($item->product_variant->product_variant_images as $image) {
                    $image->image = $this->Media->getCloudFrontURL($image->image);
                }
            }
        }

        $orderStatusMap = Configure::read('Constants.ORDER_STATUSES_MAP');
        $orderStatusProgress  = Configure::read('Constants.ORDER_STATUS_PROGRESS_COLOR');
        $orderStatusProgressBar  = Configure::read('Constants.ORDER_STATUS_PROGRESS_BAR');

        $paymentStatusProgress  = Configure::read('Constants.PAYMENT_STATUS_PROGRESS_COLOR');
        $paymentStatusProgressBar  = Configure::read('Constants.PAYMENT_STATUS_PROGRESS_BAR');

        $shippedStatusProgress  = Configure::read('Constants.SHIPPED_STATUS_PROGRESS_COLOR');
        $shippedStatusProgressBar  = Configure::read('Constants.SHIPPED_STATUS_PROGRESS_BAR');

        $deliveryStatusProgress  = Configure::read('Constants.DELIVERY_STATUS_PROGRESS_COLOR');
        $deliveryStatusProgressBar  = Configure::read('Constants.DELIVERY_STATUS_PROGRESS_BAR');

        $orderStatus = Configure::read('Constants.ORDER_STATUSES');

        // Get currency information based on order's country_id using reusable method
        $currencyInfo = $this->Orders->getOrderCurrencyInfo($order->country_id);
        
        $currencySymbol = $currencyInfo['symbol'];
        $currencyCode = $currencyInfo['code'];

       
        $orderItemCount = $this->Orders->OrderItems->find()
            ->where(['order_id' => $id])
            ->count();
           $OrderShipment="";
        // $OrderShipment = $this->ShipmentOrders->find()
        //     ->select([
        //         // 'Shipments.shipment_status',
        //         'Shipments.delivery_status'
        //     ])->contain('Shipments')
        //     ->where(['ShipmentOrders.order_id' => $id])
        //     ->order(['ShipmentOrders.id' => 'DESC']) // Sort by latest record
        //     ->first(); // Get only the last record


        // $this->log('test'.$orderStatusProgress,'debug');

        $orderCancellationCategories = $this->OrderCancellationCategories->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])->all()->toArray();

        if (empty($orderCancellationCategories)) {
            $orderCancellationCategories = ['' => 'No Categories available'];
        }


        //$this->set(compact('order', 'currencySymbol', 'currencyCode', 'orderItemCount', 'orderStatus'));
       
    $this->set(compact('order', 'currencySymbol', 'currencyCode', 'orderItemCount', 'orderStatusMap', 'orderStatus', 'orderStatusProgress', 'orderStatusProgressBar', 'paymentStatusProgressBar', 'paymentStatusProgress', 'shippedStatusProgress', 'shippedStatusProgressBar', 'deliveryStatusProgress', 'deliveryStatusProgressBar', 'OrderShipment', 'orderCancellationCategories'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    // public function add()
    // {
    //     $order = $this->Orders->newEmptyEntity();
    //     $uniqueOrderId = $this->Orders->generateUniqueOrderNum('Admin');
    //     $customerList = $this->Customers->find()
    //         ->select([
    //             'customer_id' => 'Customers.id',
    //             'full_name' => $this->Customers->Users->find()
    //                 ->select(['full_name' => 'CONCAT(Users.first_name, " ", Users.last_name)'])
    //                 ->where(['Users.id = Customers.user_id', 'Users.status' => 'A']),
    //             'phone' => $this->Customers->Users->find()
    //                 ->func()
    //                 ->concat(['+', 'Users.country_code' => 'identifier', 'Users.mobile_no' => 'identifier']),
    //             'email' => 'Users.email'
    //         ])
    //         // ->select(['phone' => 'Customers.phone_number'])
    //         ->contain(['Users'])
    //         ->where(['Users.user_type' => 'Customer', 'Users.status' => 'A'])
    //         ->all();

    //     $customers = [];
    //     foreach ($customerList as $customer) {
    //         $customers[$customer->customer_id] = $customer->full_name . ' (' . $customer->phone . ')' . '(' . $customer->email . ')';
    //     }
    //     $products = $this->Products->find('list', [
    //         'keyField' => 'id',
    //         'valueField' => 'name'
    //     ])
    //         ->where(['Products.status' => 'A'])
    //         ->order(['Products.name' => 'ASC'])
    //         ->all()
    //         ->toArray();

    //     if (empty($products)) {
    //         $products = ['' => 'No products available'];
    //     }
    //     $SALES_PERSON_ROLE_ID = Configure::read('Constants.SALES_PERSON_ROLE_ID');
    //     $user = $this->Authentication->getIdentity();
    //     $this->storeBased = $this->hasPermission($this->request->getParam('controller'), 'store');
    //     $ShowroomManagerRoleId = Configure::read('Constants.SHOWROOM_MANAGER_ROLE_ID');
    //     $ShowroomSupervisorRoleId = Configure::read('Constants.SHOWROOM_SUPERVISOR_ROLE_ID');
    //     $IsShowroomanager = '';
    //     $IsShowroomsupervisor = '';
    //     $loggedInShowroomId = '';
    //     if (!empty($user)) {
    //         if ($user->role_id == $ShowroomManagerRoleId && $this->storeBased) {
    //             $IsShowroomanager = $user->id;
    //             $showrooms = $this->Showrooms->find()
    //                 ->select(['id'])
    //                 ->where(['Showrooms.showroom_manager' => $IsShowroomanager, 'Showrooms.status' => 'A'])
    //                 ->toArray();

    //             $showroomIds = !empty($showrooms) ? array_column($showrooms, 'id') : [];
    //             $showroom_salesperson = $this->ShowroomUsers->find()
    //                 ->select(['user_id'])
    //                 ->where(['ShowroomUsers.showroom_id IN' => $showroomIds, 'ShowroomUsers.status' => 'A'])
    //                 ->toArray();

    //             $salespersonIds = !empty($showroom_salesperson) ? array_column($showroom_salesperson, 'user_id') : [];

    //             $loggedInShowroomId = !empty($showrooms) ? $showroomIds[0] : '';

    //             // Get active salespersons only if there are valid salesperson IDs


    //             // Apply whereInList() only if salespersonIds is not empty
    //             if (!empty($salespersonIds)) {
    //                 $salespersonQuery = $this->Users->find('list', [
    //                     'keyField' => 'id',
    //                     'valueField' => function ($row) {
    //                         return $row->first_name . ' ' . $row->last_name;
    //                     }
    //                 ])
    //                     ->where(['Users.status' => 'A', 'Users.role_id' => $SALES_PERSON_ROLE_ID])
    //                     ->order(['Users.first_name' => 'ASC']);
    //                 $salespersonQuery->whereInList('Users.id', $salespersonIds);
    //                 $salesperson = $salespersonQuery->toArray();
    //             } else {
    //                 $salesperson = [];
    //             }
    //         } else if ($user->role_id == $ShowroomSupervisorRoleId && $this->storeBased) {
    //             $IsShowroomsupervisor = $user->id;
    //             $showrooms = $this->Showrooms->find()
    //                 ->select(['id'])
    //                 ->where(['Showrooms.showroom_supervisor' => $IsShowroomsupervisor, 'Showrooms.status' => 'A'])
    //                 ->toArray();

    //             $showroomIds = !empty($showrooms) ? array_column($showrooms, 'id') : [];
    //             $showroom_salesperson = $this->ShowroomUsers->find()
    //                 ->select(['user_id'])
    //                 ->where(['ShowroomUsers.showroom_id IN' => $showroomIds, 'ShowroomUsers.status' => 'A'])
    //                 ->toArray();

    //             $salespersonIds = !empty($showroom_salesperson) ? array_column($showroom_salesperson, 'user_id') : [];
    //             $loggedInShowroomId = !empty($showrooms) ? $showroomIds[0] : '';
    //             // Get active salespersons only if there are valid salesperson IDs


    //             // Apply whereInList() only if salespersonIds is not empty
    //             if (!empty($salespersonIds)) {
    //                 $salespersonQuery = $this->Users->find('list', [
    //                     'keyField' => 'id',
    //                     'valueField' => function ($row) {
    //                         return $row->first_name . ' ' . $row->last_name;
    //                     }
    //                 ])
    //                     ->where(['Users.status' => 'A', 'Users.role_id' => $SALES_PERSON_ROLE_ID])
    //                     ->order(['Users.first_name' => 'ASC']);
    //                 $salespersonQuery->whereInList('Users.id', $salespersonIds);
    //                 $salesperson = $salespersonQuery->toArray();
    //             } else {
    //                 $salesperson = [];
    //             }
    //         } else {
    //             $salesperson = $this->Users->find('list', [
    //                 'keyField' => 'id',
    //                 'valueField' => function ($row) {
    //                     return $row->first_name . ' ' . $row->last_name;
    //                 }
    //             ])
    //                 ->where(['Users.status' => 'A', 'Users.role_id' => $SALES_PERSON_ROLE_ID])
    //                 ->order(['Users.first_name' => 'ASC'])
    //                 ->all()
    //                 ->toArray();
    //         }
    //     } else {
    //         $salesperson = $this->Users->find('list', [
    //             'keyField' => 'id',
    //             'valueField' => function ($row) {
    //                 return $row->first_name . ' ' . $row->last_name;
    //             }
    //         ])
    //             ->where(['Users.status' => 'A', 'Users.role_id' => $SALES_PERSON_ROLE_ID])
    //             ->order(['Users.first_name' => 'ASC'])
    //             ->all()
    //             ->toArray();
    //     }


    //     if (empty($salesperson)) {
    //         $salesperson = ['' => 'No sales person available'];
    //     }



    //     $IsShowroomanager = '';
    //     $IsShowroomsupervisor = '';
    //     if (!empty($user)) {
    //         if ($user->role_id == $ShowroomManagerRoleId && $this->storeBased) {
    //             $IsShowroomanager = $user->id;
    //             $showrooms = $this->Showrooms->find('list', [
    //                 'keyField' => 'id',
    //                 'valueField' => 'name'
    //             ])->where(['Showrooms.showroom_manager' => $IsShowroomanager, 'status' => 'A'])->toArray();
    //         } else if ($user->role_id == $ShowroomSupervisorRoleId && $this->storeBased) {
    //             $IsShowroomsupervisor = $user->id;
    //             $showrooms = $this->Showrooms->find('list', [
    //                 'keyField' => 'id',
    //                 'valueField' => 'name'
    //             ])->where(['Showrooms.showroom_supervisor' => $IsShowroomsupervisor, 'status' => 'A'])->toArray();
    //         } else {
    //             $showrooms = $this->Showrooms->find('list', [
    //                 'keyField' => 'id',
    //                 'valueField' => 'name'
    //             ])->where(['status' => 'A'])->toArray();
    //         }
    //     } else {
    //         $showrooms = $this->Showrooms->find('list', [
    //             'keyField' => 'id',
    //             'valueField' => 'name'
    //         ])->where(['status' => 'A'])->toArray();
    //     }

    //     if (empty($showrooms)) {
    //         $showrooms = ['' => 'No showrooms available'];
    //     }
    //     $cities = $this->Cities->find('list', [
    //         'keyField' => 'id',
    //         'valueField' => 'city_name'
    //     ])
    //         //->where(['Cities.status' => 'A'])
    //         ->all()
    //         ->toArray();

    //     if (empty($cities)) {
    //         $cities = ['' => 'No cities available'];
    //     }

    //     $municipalities = $this->Municipalities->find('list', [
    //         'keyField' => 'id',
    //         'valueField' => 'name'
    //     ])->all()->toArray();

    //     if (empty($municipalities)) {
    //         $municipalities = ['' => 'No Municipalities available'];
    //     }
    //     $statuses = Configure::read('Constants.ORDER_STATUSES');
    //     $shipping_methods = Configure::read('Constants.SHIPPING_METHODS');
    //     $payment_statuses = Configure::read('Constants.PAYMENT_STATUSES');
    //     $currencyConfig = Configure::read('Settings.Currency.format');
    //     $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
    //     $Loyalty_Redeem = Configure::read('Constants.REDEEM_LOYALTY');
    //     $ShowroomManagerRoleId = Configure::read('Constants.SHOWROOM_MANAGER_ROLE_ID');

    //     $showroomPreSel = '';
    //     $IsShowroomanager = '';
    //     if (!empty($user)) {
    //         if ($user->role_id == $ShowroomManagerRoleId && $this->storeBased) {
    //             $IsShowroomanager = $user->id;
    //             $showroomPreSel = $this->ShowRoomUsers->find()
    //                 ->select(['showroom_id'])
    //                 ->where(['user_id' => $user->id, 'status' => 'A'])
    //                 ->first();

    //             $showroomPreSel = $showroomPreSel ? $showroomPreSel->showroom_id : '';
    //         }
    //     }
    //     $ABIDJAN_CITY_ID = Configure::read('Constants.ABIDJAN_CITY_ID');

    //     $this->set(compact('order', 'customers', 'products', 'uniqueOrderId', 'statuses', 'showrooms', 'shipping_methods', 'payment_statuses', 'currencySymbol', 'cities', 'Loyalty_Redeem', 'showroomPreSel', 'IsShowroomanager', 'municipalities', 'ABIDJAN_CITY_ID', 'salesperson', 'loggedInShowroomId'));

    //     if ($this->request->is('post')) {
    //         $order = $this->Orders->patchEntity($order, $this->request->getData(), [
    //             'associated' => [
    //                 'OrderItems' => [
    //                     'associated' => ['OrderItemAttributes']
    //                 ],
    //                 'Transactions'
    //             ]
    //         ]);

    //         $data = $this->request->getData();

    //         $order['order_type'] = 'Showroom';
    //         $order->transactions[0]['invoice_number'] = $this->Transactions->generateUniqueInvoiceNum();
    //         $order->transactions[0]['transaction_number'] = $this->Transactions->generateUniqueTransactionNum();
    //         $order->transactions[0]['transaction_date'] = date('Y-m-d H:i:s');
    //         $order->transactions[0]['amount'] = $order->total_amount;
    //         $order->transactions[0]['payment_method'] = $order->payment_method;
    //         $order['delivery_date'] = $this->CalculateEstimatedDelivery($data['delivery_mode_type']);
    //         $errors = $order->getErrors();
    //         foreach ($order->order_items as &$item) {
    //             if (!empty($item['order_item_attributes'])) {
    //                 $item['order_item_attributes'] = array_filter($item['order_item_attributes'], function ($attribute) {
    //                     return !empty($attribute['product_attribute_id']); // Keep only attributes with a value
    //                 });
    //             }
    //         }

    //         if ($order['order_type'] == 'Showroom' && $order['delivery_mode'] == 'delivery') {

    //             $ShowroomManagerRoleId = Configure::read('Constants.SHOWROOM_MANAGER_ROLE_ID');
    //             $ShowroomSupervisorRoleId = Configure::read('Constants.SHOWROOM_SUPERVISOR_ROLE_ID');

    //             $user = $this->Authentication->getIdentity();
    //             if (!empty($user)) {
    //                 $UserId = $user->id;
    //                 if ($user->role_id == $ShowroomManagerRoleId) {
    //                     $order['showroom_id'] = $this->Showrooms
    //                         ->find()
    //                         ->select(['id'])
    //                         ->where([
    //                             'Showrooms.showroom_manager' => $UserId,
    //                             'Showrooms.status' => 'A'
    //                         ])
    //                         ->first()
    //                         ->id;
    //                 } elseif ($user->role_id == $ShowroomSupervisorRoleId) {
    //                     $order['showroom_id'] = $this->Showrooms
    //                         ->find()
    //                         ->select(['id'])
    //                         ->where([
    //                             'Showrooms.showroom_supervisor' => $UserId,
    //                             'Showrooms.status' => 'A'
    //                         ])
    //                         ->first()
    //                         ->id;
    //                 }
    //             }
    //         }

    //         if ($this->Orders->save($order, [
    //             'associated' => [
    //                 'OrderItems' => ['OrderItemAttributes'],
    //                 'Transactions'
    //             ]
    //         ])) {
    //             $response = ['success' => true, 'message' => 'The Order has been saved.'];
    //             $this->response = $this->response->withType('application/json');
    //             $this->response = $this->response->withStringBody(json_encode($response));
    //             $this->Flash->success(__('The order has been saved.'));
    //             return $this->response;
    //             // return $this->redirect(['action' => 'index']);
    //         } else {
    //             $this->log('data: ' . json_encode($order->getErrors()), 'debug');
    //             $this->Flash->error(__('The order could not be saved. Please, try again.'));
    //         }
    //     }
    //     // $customerAddresses = $this->Orders->CustomerAddresses->find('list', limit: 200)->all();
    //     // $offers = $this->Orders->Offers->find('list', limit: 200)->all();
    //     // $showrooms = $this->Orders->Showrooms->find('list', limit: 200)->all();
    //     //$this->set(compact('order', 'customers', 'customerAddresses', 'offers', 'showrooms'));
    // }

    /**
     * Edit method
     *
     * @param string|null $id Order id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    // public function edit($id = null)
    // {
    //     $order = $this->Orders->get($id, [
    //         'contain' => [
    //             'Customers' => [
    //                 'Users'
    //             ],
    //             'CustomerAddresses' => [
    //                 'Cities',
    //                 // 'Municipalities'
    //             ],
    //             'OrderItems' => [
    //                 'Products' => [
    //                     'ProductVariants',
    //                     'ProductAttributes'
    //                 ],
    //                 'ProductVariants',
    //                 // 'OrderItemAttributes' => [
    //                 //     'ProductAttributes'
    //                 // ]
    //             ]
    //         ]
    //     ]);
    //     $orderItems = $order->order_items;

    //     $customerList = $this->Customers->find()
    //         ->select([
    //             'customer_id' => 'Customers.id',
    //             'full_name' => $this->Customers->Users->find()
    //                 ->select(['full_name' => 'CONCAT(Users.first_name, " ", Users.last_name)'])
    //                 ->where(['Users.id = Customers.user_id', 'Users.status' => 'A']),
    //             'phone' => $this->Customers->Users->find()
    //                 ->func()
    //                 ->concat(['+', 'Users.country_code' => 'identifier', 'Users.mobile_no' => 'identifier']),
    //             'email' => 'Users.email'
    //         ])
    //         // ->select(['phone' => 'Customers.phone_number'])
    //         ->contain(['Users'])
    //         ->where(['Users.user_type' => 'Customer', 'Users.status' => 'A'])
    //         ->all();

    //     $customers = [];
    //     foreach ($customerList as $customer) {
    //         $customers[$customer->customer_id] = $customer->full_name . ' (' . $customer->phone . ')' . '(' . $customer->email . ')';
    //     }
    //     $products = $this->Products->find('list', [
    //         'keyField' => 'id',
    //         'valueField' => 'name'
    //     ])
    //         ->where(['Products.status' => 'A'])
    //         ->order(['Products.name' => 'ASC'])
    //         ->all()
    //         ->toArray();

    //     if (empty($products)) {
    //         $products = ['' => 'No products available'];
    //     }

    //     $SALES_PERSON_ROLE_ID = Configure::read('Constants.SALES_PERSON_ROLE_ID');
    //     $user = $this->Authentication->getIdentity();
    //     $this->storeBased = $this->hasPermission($this->request->getParam('controller'), 'store');
    //     $ShowroomManagerRoleId = Configure::read('Constants.SHOWROOM_MANAGER_ROLE_ID');
    //     $ShowroomSupervisorRoleId = Configure::read('Constants.SHOWROOM_SUPERVISOR_ROLE_ID');
    //     $IsShowroomanager = '';
    //     $IsShowroomsupervisor = '';
    //     if (!empty($user)) {
    //         if ($user->role_id == $ShowroomManagerRoleId && $this->storeBased) {
    //             $IsShowroomanager = $user->id;
    //             $showrooms = $this->Showrooms->find()
    //                 ->select(['id'])
    //                 ->where(['Showrooms.showroom_manager' => $IsShowroomanager, 'Showrooms.status' => 'A'])
    //                 ->toArray();

    //             $showroomIds = !empty($showrooms) ? array_column($showrooms, 'id') : [];
    //             $showroom_salesperson = $this->ShowroomUsers->find()
    //                 ->select(['user_id'])
    //                 ->where(['ShowroomUsers.showroom_id IN' => $showroomIds, 'ShowroomUsers.status' => 'A'])
    //                 ->toArray();

    //             $salespersonIds = !empty($showroom_salesperson) ? array_column($showroom_salesperson, 'user_id') : [];

    //             // Get active salespersons only if there are valid salesperson IDs


    //             // Apply whereInList() only if salespersonIds is not empty
    //             if (!empty($salespersonIds)) {
    //                 $salespersonQuery = $this->Users->find('list', [
    //                     'keyField' => 'id',
    //                     'valueField' => function ($row) {
    //                         return $row->first_name . ' ' . $row->last_name;
    //                     }
    //                 ])
    //                     ->where(['Users.status' => 'A', 'Users.role_id' => $SALES_PERSON_ROLE_ID])
    //                     ->order(['Users.first_name' => 'ASC']);
    //                 $salespersonQuery->whereInList('Users.id', $salespersonIds);
    //                 $salesperson = $salespersonQuery->toArray();
    //             } else {
    //                 $salesperson = [];
    //             }
    //         } else if ($user->role_id == $ShowroomSupervisorRoleId && $this->storeBased) {
    //             $IsShowroomsupervisor = $user->id;
    //             $showrooms = $this->Showrooms->find()
    //                 ->select(['id'])
    //                 ->where(['Showrooms.showroom_supervisor' => $IsShowroomsupervisor, 'Showrooms.status' => 'A'])
    //                 ->toArray();

    //             $showroomIds = !empty($showrooms) ? array_column($showrooms, 'id') : [];
    //             $showroom_salesperson = $this->ShowroomUsers->find()
    //                 ->select(['user_id'])
    //                 ->where(['ShowroomUsers.showroom_id IN' => $showroomIds, 'ShowroomUsers.status' => 'A'])
    //                 ->toArray();

    //             $salespersonIds = !empty($showroom_salesperson) ? array_column($showroom_salesperson, 'user_id') : [];

    //             // Get active salespersons only if there are valid salesperson IDs


    //             // Apply whereInList() only if salespersonIds is not empty
    //             if (!empty($salespersonIds)) {
    //                 $salespersonQuery = $this->Users->find('list', [
    //                     'keyField' => 'id',
    //                     'valueField' => function ($row) {
    //                         return $row->first_name . ' ' . $row->last_name;
    //                     }
    //                 ])
    //                     ->where(['Users.status' => 'A', 'Users.role_id' => $SALES_PERSON_ROLE_ID])
    //                     ->order(['Users.first_name' => 'ASC']);
    //                 $salespersonQuery->whereInList('Users.id', $salespersonIds);
    //                 $salesperson = $salespersonQuery->toArray();
    //             } else {
    //                 $salesperson = [];
    //             }
    //         } else {
    //             $salesperson = $this->Users->find('list', [
    //                 'keyField' => 'id',
    //                 'valueField' => function ($row) {
    //                     return $row->first_name . ' ' . $row->last_name;
    //                 }
    //             ])
    //                 ->where(['Users.status' => 'A', 'Users.role_id' => $SALES_PERSON_ROLE_ID])
    //                 ->order(['Users.first_name' => 'ASC'])
    //                 ->all()
    //                 ->toArray();
    //         }
    //     } else {
    //         $salesperson = $this->Users->find('list', [
    //             'keyField' => 'id',
    //             'valueField' => function ($row) {
    //                 return $row->first_name . ' ' . $row->last_name;
    //             }
    //         ])
    //             ->where(['Users.status' => 'A', 'Users.role_id' => $SALES_PERSON_ROLE_ID])
    //             ->order(['Users.first_name' => 'ASC'])
    //             ->all()
    //             ->toArray();
    //     }


    //     if (empty($salesperson)) {
    //         $salesperson = ['' => 'No sales person available'];
    //     }



    //     $IsShowroomanager = '';
    //     $IsShowroomsupervisor = '';
    //     // if (!empty($user)) {
    //     //     if ($user->role_id == $ShowroomManagerRoleId) {
    //     //         $IsShowroomanager = $user->id;
    //     //         $showrooms = $this->Showrooms->find('list', [
    //     //             'keyField' => 'id',
    //     //             'valueField' => 'name'
    //     //         ])->where(['Showrooms.showroom_manager' => $IsShowroomanager, 'status' => 'A'])->toArray();
    //     //     } else if ($user->role_id == $ShowroomSupervisorRoleId) {
    //     //         $IsShowroomsupervisor = $user->id;
    //     //         $showrooms = $this->Showrooms->find('list', [
    //     //             'keyField' => 'id',
    //     //             'valueField' => 'name'
    //     //         ])->where(['Showrooms.showroom_supervisor' => $IsShowroomsupervisor, 'status' => 'A'])->toArray();
    //     //     } else {
    //     //         $showrooms = $this->Showrooms->find('list', [
    //     //             'keyField' => 'id',
    //     //             'valueField' => 'name'
    //     //         ])->where(['status' => 'A'])->toArray();
    //     //     }
    //     // } else {
    //     //     $showrooms = $this->Showrooms->find('list', [
    //     //         'keyField' => 'id',
    //     //         'valueField' => 'name'
    //     //     ])->where(['status' => 'A'])->toArray();
    //     // }

    //     // Initialize showrooms if not set
    //     if (!isset($showrooms) || empty($showrooms)) {
    //         $showrooms = ['' => 'No showrooms available'];
    //     }
    //     $cities = $this->Cities->find('list', [
    //         'keyField' => 'id',
    //         'valueField' => 'city_name'
    //     ])
    //         //->where(['Cities.status' => 'A'])
    //         ->all()
    //         ->toArray();

    //     if (empty($cities)) {
    //         $cities = ['' => 'No cities available'];
    //     }

    //     // $municipalities = $this->Municipalities->find('list', [
    //     //     'keyField' => 'id',
    //     //     'valueField' => 'name'
    //     // ])->all()->toArray();

    //     if (empty($municipalities)) {
    //         $municipalities = ['' => 'No Municipalities available'];
    //     }
    //     $statuses = Configure::read('Constants.ORDER_STATUSES');
    //     $shipping_methods = Configure::read('Constants.SHIPPING_METHODS');
    //     $payment_statuses = Configure::read('Constants.PAYMENT_STATUSES');
    //     $currencyConfig = Configure::read('Settings.Currency.format');
    //     $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
    //     $Loyalty_Redeem = Configure::read('Constants.REDEEM_LOYALTY');
    //     $ShowroomManagerRoleId = Configure::read('Constants.SHOWROOM_MANAGER_ROLE_ID');

    //     $showroomPreSel = '';
    //     $IsShowroomanager = '';
    //     if (!empty($user)) {
    //         if ($user->role_id == $ShowroomManagerRoleId && $this->storeBased) {
    //             $IsShowroomanager = $user->id;
    //             $showroomPreSel = $this->ShowRoomUsers->find()
    //                 ->select(['showroom_id'])
    //                 ->where(['user_id' => $user->id, 'status' => 'A'])
    //                 ->first();

    //             $showroomPreSel = $showroomPreSel ? $showroomPreSel->showroom_id : '';
    //         }
    //     }
    //     $ABIDJAN_CITY_ID = Configure::read('Constants.ABIDJAN_CITY_ID');
    //     // $uniqueOrderId = $order['order_number'];
    //     // $customerList = $this->Customers->find()
    //     //     ->select([
    //     //         'customer_id' => 'Customers.id',
    //     //         'full_name' => $this->Customers->Users->find()
    //     //             ->select(['full_name' => 'CONCAT(Users.first_name, " ", Users.last_name)'])
    //     //             ->where(['Users.id = Customers.user_id', 'Users.status' => 'A'])
    //     //     ])
    //     //     ->select(['phone' => 'Customers.phone_number'])
    //     //     ->contain(['Users'])
    //     //     ->all();

    //     // $customers = [];
    //     // foreach ($customerList as $customer) {
    //     //     if ($customer->phone) {
    //     //         $formattedPhone = preg_replace('/(\d{3})(\d{3})(\d{4})/', '$1-$2-$3', $customer->phone);
    //     //     } else {
    //     //         $formattedPhone = '';
    //     //     }

    //     //     $customers[$customer->customer_id] = $customer->full_name . ' (' . $formattedPhone . ')';
    //     // }
    //     // $products = $this->Products->find('list', [
    //     //     'keyField' => 'id',
    //     //     'valueField' => 'name'
    //     // ])
    //     //     ->where(['Products.status' => 'A'])
    //     //     ->all()
    //     //     ->toArray();

    //     // if (empty($products)) {
    //     //     $products = ['' => 'No products available'];
    //     // }
    //     // $showrooms = $this->Showrooms->find('list', [
    //     //     'keyField' => 'id',
    //     //     'valueField' => 'name'
    //     // ])
    //     //     ->where(['Showrooms.status' => 'A'])
    //     //     ->all()
    //     //     ->toArray();

    //     // if (empty($showrooms)) {
    //     //     $showrooms = ['' => 'No showrooms available'];
    //     // }
    //     // $cities = $this->Cities->find('list', [
    //     //     'keyField' => 'id',
    //     //     'valueField' => 'name'
    //     // ])
    //     //     //->where(['Cities.status' => 'A'])
    //     //     ->all()
    //     //     ->toArray();

    //     // if (empty($cities)) {
    //     //     $cities = ['' => 'No products available'];
    //     // }
    //     // $statuses = Configure::read('Constants.ORDER_STATUSES');
    //     // $shipping_methods = Configure::read('Constants.SHIPPING_METHODS');
    //     // $payment_statuses = Configure::read('Constants.PAYMENT_STATUSES');
    //     // $currencyConfig = Configure::read('Settings.Currency.format');
    //     // $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';

    //     $this->set(compact('order', 'customers', 'products', 'statuses', 'showrooms', 'shipping_methods', 'payment_statuses', 'currencySymbol', 'cities', 'Loyalty_Redeem', 'showroomPreSel', 'IsShowroomanager', 'municipalities', 'ABIDJAN_CITY_ID', 'salesperson', 'orderItems'));

    //     if ($this->request->is(['patch', 'post', 'put'])) {
    //         $order = $this->Orders->patchEntity($order, $this->request->getData());
    //         if ($this->Orders->save($order)) {
    //             $this->Flash->success(__('The order has been saved.'));

    //             return $this->redirect(['action' => 'index']);
    //         }
    //         $this->Flash->error(__('The order could not be saved. Please, try again.'));
    //     }
    //     // $customers = $this->Orders->Customers->find('list', limit: 200)->all();
    //     // $customerAddresses = $this->Orders->CustomerAddresses->find('list', limit: 200)->all();
    //     // $offers = $this->Orders->Offers->find('list', limit: 200)->all();
    //     // $showrooms = $this->Orders->Showrooms->find('list', limit: 200)->all();
    //     // $this->set(compact('order', 'customers', 'customerAddresses', 'offers', 'showrooms'));
    // }

    /**
     * Delete method
     *
     * @param string|null $id Order id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $order = $this->Orders->get($id);
        $response = ['success' => false, 'message' => 'The order could not be cancelled. Please, try again.'];
        if ($order) {
            if ($this->Orders->delete($order)) {
                $response = ['success' => true, 'message' => 'The order has been marked as Cancelled.'];
            } else {
                $response = ['success' => false, 'message' => 'The order could not be cancelled. Please, try again.'];
            }
        } else {
            $response = ['success' => false, 'message' => 'The order does not exist.'];
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }

    public function exportToCsv()
    {
        $OrderDetails = $this->Orders->getOrderDetails();
        $orderstatuses = Configure::read('Constants.ORDER_STATUSES');
        $paymentstatuses = Configure::read('Constants.PAYMENT_STATUSES');
        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');
        $header = ['ID', 'Order Number', 'Customer Name', 'Total Amount', 'Quantity', 'Order Status', 'Payment Method', 'Payment Status', 'Date & Time'];

        $this->response = $this->response->withType('text/csv');
        $this->response = $this->response->withDownload('orders_export.csv');
        $output = fopen('php://output', 'w');

        fputcsv($output, $header);

        foreach ($OrderDetails as $order) {
            fputcsv($output, [
                $order->id,
                (string)$order->order_number,
                $order->full_name . ' Customer Mo. No. ' . $order->phone_number,
                $order->total_amount,
                $order->quantity,
                isset($orderstatuses[$order->status]) ? $orderstatuses[$order->status] : '',
                $order->payment_method,
                isset($paymentstatuses[$order->transaction_status]) ? $paymentstatuses[$order->transaction_status] : '',
                $order->order_date ? $order->order_date->format($dateFormat . ' ' . $timeFormat) : ''
            ]);
        }

        fclose($output);
        return $this->response;
    }

    public function exportToExcel()
    {

        $orders = $this->Orders->getOrderDetails();
        $orderstatuses = Configure::read('Constants.ORDER_STATUSES');
        $paymentstatuses = Configure::read('Constants.PAYMENT_STATUSES');
        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');

        $this->response = $this->response->withType('application/vnd.ms-excel');
        $this->response = $this->response->withDownload('orders_export.xls');

        $output = '<table border="1">';
        $output .= '<tr>';
        $output .= '<th>ID</th>';
        $output .= '<th>Order Number</th>';
        $output .= '<th>Customer Name</th>';
        $output .= '<th>Total Amount</th>';
        $output .= '<th>Quantity</th>';
        $output .= '<th>Order Status</th>';
        $output .= '<th>Payment Method</th>';
        $output .= '<th>Payment Status</th>';
        $output .= '<th>Date & Time</th>';
        $output .= '</tr>';

        foreach ($orders as $order) {
            $output .= '<tr>';
            $output .= '<td>' . h($order->id) . '</td>';
            $output .= '<td>' . h((string)$order->order_number) . '</td>';
            $output .= '<td>' . h($order->full_name . ' Customer Mo. No. ' . $order->phone_number) . '</td>';
            $output .= '<td>' . h($order->total_amount) . '</td>';
            $output .= '<td>' . h($order->quantity) . '</td>';
            $output .= '<td>' . h(isset($orderstatuses[$order->status]) ? $orderstatuses[$order->status] : '') . '</td>';
            $output .= '<td>' . h($order->payment_method) . '</td>';
            $output .= '<td>' . h(isset($paymentstatuses[$order->transaction_status]) ? $paymentstatuses[$order->transaction_status] : '') . '</td>';
            $output .= '<td>' . h($order->order_date ? $order->order_date->format($dateFormat . ' ' . $timeFormat) : '') . '</td>';
            $output .= '</tr>';
        }

        $output .= '</table>';

        echo $output;

        return $this->response;
    }

    public function getCustomerOrders()
    {
        $customer_id = $this->request->getQuery('customer_id');

        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');

        $orders = $this->Orders->find()
            ->where(['Orders.customer_id' => $customer_id])
            ->select([
                'id' => 'Orders.id',
                'order_number' => 'Orders.order_number',
                'total_amount' => 'Orders.total_amount',
                'status' => 'Orders.status',
                'payment_method' => 'Orders.payment_method',
                'order_date' => 'Orders.order_date',
                'quantity' => $this->Orders->OrderItems->find()
                    ->func()
                    ->sum('OrderItems.quantity'),
                'payment_status' => 'Transactions.payment_status',
            ])
            ->leftJoinWith('OrderItems')
            ->leftJoinWith('Transactions')
            ->group('Orders.id')
            ->enableAutoFields(false)
            ->toArray();

        foreach ($orders as &$order) {
            $orderDate = $order['order_date'];
            $formattedDate = $orderDate->format($dateFormat . ' ' . $timeFormat);
            $order['order_date'] = $formattedDate;
        }

        $response = ['orders' => $orders];

        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode($response));

        return $this->response;
    }

    public function donwloadInvoice($id = null)
    {
        $order = $this->Orders->get($id, [
            'contain' => [
                'Customers' => ['Users'],
                'CustomerAddresses' => ['Cities'],
                'Countries',
                'Offers',
               // 'Showrooms' => ['Cities'],
                'OrderItems' => [
                    'Products' => [
                        'ProductImages' => function ($q) {
                            return $q->where(['image_default' => 1, 'status' => 'A']);
                        }
                    ],
                    'ProductVariants' => [
                        'ProductVariantImages' => function ($q) {
                            return $q->where(['image_default' => 1, 'status' => 'A']);
                        }
                    ]
                ],
                //'Transactions'
            ]
        ]);

        $customer_care_no = $this->SiteSettings->find()
            ->select(['customer_support_no'])
            ->first()->customer_support_no;
        $customer_care_email = $this->SiteSettings->find()
            ->select(['support_email'])
            ->first()->support_email;
        $call_center_no = $this->SiteSettings->find()
            ->select(['contact_no'])
            ->first()->contact_no;
        $whatsapp_no = $this->SiteSettings->find()
            ->select(['contact_no'])
            ->first()->contact_no;
        $after_sales_no = $this->SiteSettings->find()
            ->select(['contact_no'])
            ->first()->contact_no;

        // Assign the CloudFront URL to product images
        foreach ($order->order_items as $item) {
            if (!empty($item->product->product_images)) {
                foreach ($item->product->product_images as $image) {
                    $image->image = $this->Media->getCloudFrontURL($image->image);
                }
            }
            if (!empty($item->product_variant->product_variant_images)) {
                foreach ($item->product_variant->product_variant_images as $image) {
                    $image->image = $this->Media->getCloudFrontURL($image->image);
                }
            }
        }

        $orderStatusMap = Configure::read('Constants.ORDER_STATUSES_MAP');
        $orderStatusProgress  = Configure::read('Constants.ORDER_STATUS_PROGRESS_COLOR');
        $orderStatusProgressBar  = Configure::read('Constants.ORDER_STATUS_PROGRESS_BAR');

        $paymentStatusProgress  = Configure::read('Constants.PAYMENT_STATUS_PROGRESS_COLOR');
        $paymentStatusProgressBar  = Configure::read('Constants.PAYMENT_STATUS_PROGRESS_BAR');

        $shippedStatusProgress  = Configure::read('Constants.SHIPPED_STATUS_PROGRESS_COLOR');
        $shippedStatusProgressBar  = Configure::read('Constants.SHIPPED_STATUS_PROGRESS_BAR');

        $deliveryStatusProgress  = Configure::read('Constants.DELIVERY_STATUS_PROGRESS_COLOR');
        $deliveryStatusProgressBar  = Configure::read('Constants.DELIVERY_STATUS_PROGRESS_BAR');

        $orderStatus = Configure::read('Constants.ORDER_STATUSES');

        // Get currency information based on order's country_id using reusable method
        $currencyInfo = $this->Orders->getOrderCurrencyInfo($order->country_id);
        $currencySymbol = $currencyInfo['symbol'];
        $currencyCode = $currencyInfo['code'];

        // Format order amounts with proper currency for invoice
        

        $orderItemCount = $this->Orders->OrderItems->find()
            ->where(['order_id' => $id])
            ->count();

        // $this->log('test'.$orderStatusProgress,'debug');

    
        $this->set(compact('order', 'currencySymbol', 'currencyCode', 'currencyInfo', 'orderItemCount', 'orderStatusMap', 'orderStatus', 'orderStatusProgress', 'orderStatusProgressBar', 'paymentStatusProgressBar', 'paymentStatusProgress', 'shippedStatusProgress', 'shippedStatusProgressBar', 'deliveryStatusProgress', 'deliveryStatusProgressBar', 'customer_care_no', 'customer_care_email', 'call_center_no', 'whatsapp_no', 'after_sales_no'));
        $this->render('invoice');
    }

    function CalculateEstimatedDelivery($deliveryMode)
    {
        $now = new DateTime();
        $currentHour = (int)$now->format('H');

        $siteSettings = $this->SiteSettings->find()
            ->select(['express_delivery_order_cutoff_time'])
            ->first();
        $express_delivery_order_cutoff_time = (int)$siteSettings->express_delivery_order_cutoff_time;

        if ($deliveryMode === 'express') {
            if ($currentHour >= $express_delivery_order_cutoff_time) {
                $now->modify('+1 day'); // Move to next day if after cutoff
            }
        } else {
            $now->modify('+2 days'); // Standard delivery in 48 hours
        }

        return $now->format('Y-m-d');
    }

    public function printInvoicePdf()
    {
        // Increase execution time and memory limit for PDF generation
        ini_set('max_execution_time', 300); // 5 minutes
        ini_set('memory_limit', '512M');

        $this->request->allowMethod(['post']); // Only allow POST requests
        $htmlContent = $this->request->getData('html');

        if (empty($htmlContent)) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => 'error',
                'message' => 'No HTML content received'
            ]));
        }

        try {
            // Initialize MPDF with optimized settings
            $mpdf = new Mpdf([
                'mode' => 'utf-8',
                'format' => 'A4',
                'orientation' => 'P',
                'margin_left' => 15,
                'margin_right' => 15,
                'margin_top' => 16,
                'margin_bottom' => 16,
                'margin_header' => 9,
                'margin_footer' => 9,
                'tempDir' => TMP . 'mpdf'
            ]);

            // Only load essential CSS to reduce processing time
            $essentialCss = '
                body { font-family: Arial, sans-serif; font-size: 12px; }
                table { width: 100%; border-collapse: collapse; }
                th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
                .text-center { text-align: center; }
                .text-right { text-align: right; }
                .font-weight-bold { font-weight: bold; }
            ';

            // Apply minimal styles for faster processing
            $mpdf->WriteHTML($essentialCss, \Mpdf\HTMLParserMode::HEADER_CSS);
            $mpdf->WriteHTML($htmlContent, \Mpdf\HTMLParserMode::HTML_BODY);

            // Output PDF
            $pdfFileName = "Invoice_" . time() . ".pdf";
            $mpdf->Output($pdfFileName, "I"); // Open in browser

        } catch (\Exception $e) {
            // Log the error for debugging
            $this->log('PDF Generation Error: ' . $e->getMessage(), 'error');

            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => 'error',
                'message' => 'PDF generation failed: ' . $e->getMessage()
            ]));
        }

        return $this->response->withType('application/pdf');
    }

    public function approveOrder($id = null)
    {

        $this->request->allowMethod(['post']);
        $id = $this->request->getData('id');
        $order = $this->Orders->get($id);

        $response = [];

        if (!$order) {
            $response = [
                'status' => 'error',
                'message' => __('Order not found.')
            ];
        } else {
            $order->status = 'Processing';

            if ($this->Orders->save($order)) {
                $response = [
                    'status' => 'success',
                    'message' => __('Order has been approved.')
                ];
            } else {
                $response = [
                    'status' => 'error',
                    'message' => __('Unable to approve the order. Please try again.')
                ];
            }
        }

        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode($response));

        return $this->response;
    }

    public function updateOrderStatus()
    {
        $this->request->allowMethod(['post']);

        $id = $this->request->getData('id');
        $status = $this->request->getData('status');

        // Validate status values
        $allowedStatuses = ['Pending', 'Confirmed', 'Processing', 'Shipped', 'Out for Delivery', 'Delivered', 'Rejected','Returned','Return Requested','Return Approved','Return Rejected','Cancelled','Cancellation Requested','Cancellation Rejected'];
        if (!in_array($status, $allowedStatuses)) {
            $response = [
                'status' => 'error',
                'message' => __('Invalid status provided.')
            ];
        } else {
            $order = $this->Orders->get($id);


            if (!$order) {
                $response = [
                    'status' => 'error',
                    'message' => __('Order not found.')
                ];
            } else {
                $order->status = $status;

                if ($this->Orders->save($order)) {
                    // Send email notifications based on status change
                    if ($status === 'Rejected') {
                        $this->sendOrderStatusEmail($order, 'rejected');
                    } elseif ($status === 'Confirmed') {
                        $this->sendOrderStatusEmail($order, 'confirmed');
                    }

                    if ($status === 'Delivered') {
                    
                    $transaction = $this->Transactions->find()
                        ->where(['order_id' => $order->id])
                        ->first();

                    if ($transaction && strtolower($transaction->payment_method) === 'cash on delivery') {
                        $transaction->payment_status = 'Completed';
                        $this->Transactions->save($transaction);
                    }
                }

                    $response = [
                        'status' => 'success',
                        'message' => __('Order status updated to {0} successfully.', $status)
                    ];
                } else {
                    $response = [
                        'status' => 'error',
                        'message' => __('Unable to update order status. Please try again.')
                    ];
                }
            }
        }

        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode($response));

        return $this->response;
    }

    public function approveCancellation($id = null)
    {
        $this->request->allowMethod(['post']);

        $id = $this->request->getData('id');
        $condition = $this->request->getData('condition');
        // $order_cancellation_category_id = $this->request->getData('order_cancellation_category_id');
        // $reason = $this->request->getData('reason');

        $order = $this->Orders->get($id, [
            'contain' => [
                'Customers' => ['Users'],
                'OrderItems'
            ]
        ]);

        $response = [];

        if (!$order) {
            $response = [
                'status' => 'error',
                'message' => __('Order not found.')
            ];
        } else {
            if ($condition == 'Approved') {
                $order->status = 'Cancelled';
            } else if ($condition == 'Rejected') {
                $order->status = 'Cancellation Rejected';
            } else {
                $order->status = 'Pending Cancellation';
            }


            if ($this->Orders->save($order)) {
                $lastOrderCancellation = null;

                foreach ($order->order_items as $item) {
                    $orderItem = $this->OrderItems
                        ->find()
                        ->where(['id' => $item->id])
                        ->first();
                    if ($orderItem) {
                        if ($condition == 'Approved') {
                            $orderItem->status = 'Cancelled';
                        } else if ($condition == 'Rejected') {
                            $orderItem->status = 'Cancellation Rejected';
                        } else {
                            $orderItem->status = 'Pending Cancellation';
                        }
                        $this->OrderItems->save($orderItem);
                    }
                    $orderCancellation = $this->OrderCancellations
                        ->find()
                        ->where(['order_item_id' => $item->id])
                        ->first();

                    if ($orderCancellation) {
                        if ($condition == 'Approved') {
                            $orderCancellation->status = 'Approved';
                        } else if ($condition == 'Rejected') {
                            $orderCancellation->status = 'Rejected';
                        } else {
                            $orderCancellation->status = 'Pending';
                        }

                        $orderCancellation->canceled_at = new DateTime();
                        // $orderCancellation = $this->OrderCancellations->newEmptyEntity();
                        // $orderCancellation->order_item_id = $item->id;
                        // $orderCancellation->order_id = $order->id;
                        // $orderCancellation->order_cancellation_category_id = $order_cancellation_category_id;
                        // $orderCancellation->reason = $reason;
                        // $orderCancellation->customer_id = $order->customer_id;
                        if ($this->OrderCancellations->save($orderCancellation)) {
                            $lastOrderCancellation = $orderCancellation; // Store last saved record
                        }
                    }
                }
                if ($lastOrderCancellation) {
                    $this->sendCancelledEmails($order, $lastOrderCancellation, $condition);
                    if ($condition == 'Approved') {
                        $response = [
                            'status' => 'success',
                            'message' => __('Order cancellation has been approved.')
                        ];
                    } else if ($condition == 'Rejected') {
                        $response = [
                            'status' => 'success',
                            'message' => __('Order cancellation has been rejected.')
                        ];
                    }
                } else {
                    $response = [
                        'status' => 'error',
                        'message' => __('Order was not cancelled before and email not sent to client.')
                    ];
                }
            } else {
                $response = [
                    'status' => 'error',
                    'message' => __('Unable to cancel the order. Please try again.')
                ];
            }
        }

        return $this->response
            ->withType('application/json')
            ->withStringBody(json_encode($response));
    }


    private function sendCancelledEmails($order, $orderCancellation, $condition)
    {
        if ($order && $orderCancellation) {
            $toEmails = [$order->customer->user->email];

            if (empty($toEmails)) {
                \Cake\Log\Log::warning("No valid recipients found for order ID: {$order->id}");
                return;
            }

            $emailData = [
                'order_number' => $order->order_number,
               'customer_name' => $order->customer->user->first_name.' '.$order->customer->user->last_name,
                'reason' => $orderCancellation->reason,  // Fixed double '$' typo
                'comment' => $orderCancellation->reason, // Fixed double '$' typo
                'canceled_at' => $orderCancellation->canceled_at, // Fixed double '$' typo & incorrect field name
                'status' =>$orderCancellation->status
            ];

            $subject = "Order #{$order->order_number} Cancellation has been ".$condition;

            $this->Global->send_email(
                $toEmails,
                null,
                $subject,
                'order_cancellation',
                $emailData
            );
        }
    }

    public function approveReturn($id = null)
    {
        $this->request->allowMethod(['post']);

        $id = $this->request->getData('id');
        $condition = $this->request->getData('condition');
        // $order_cancellation_category_id = $this->request->getData('order_cancellation_category_id');
        // $reason = $this->request->getData('reason');

        $order = $this->Orders->get($id, [
            'contain' => [
                'Customers' => ['Users'],
                'OrderItems'
            ]
        ]);

        $response = [];

        if (!$order) {
            $response = [
                'status' => 'error',
                'message' => __('Order not found.')
            ];
        } else {
            if ($condition == 'Approved') {
                $order->status = 'Return Approved';
            } else if ($condition == 'Rejected') {
                $order->status = 'Return Rejected';
            } else {
                $order->status = 'Pending Return';
            }


            if ($this->Orders->save($order)) {
                $lastOrderRejected = null;

                foreach ($order->order_items as $item) {
                    $orderItem = $this->OrderItems
                        ->find()
                        ->where(['id' => $item->id])
                        ->first();
                    if ($orderItem) {
                        if ($condition == 'Approved') {
                            $orderItem->status = 'Return Approved';
                        } else if ($condition == 'Rejected') {
                            $orderItem->status = 'Return Rejected';
                        } else {
                            $orderItem->status = 'Pending Return';
                        }
                        $this->OrderItems->save($orderItem);
                    }
                    $orderReturn = $this->OrderReturns
                        ->find()
                        ->where(['order_item_id' => $item->id])
                        ->first();

                    if ($orderReturn) {
                        if ($condition == 'Approved') {
                            $orderReturn->status = 'Approved';
                        } else if ($condition == 'Rejected') {
                            $orderReturn->status = 'Rejected';
                        } else {
                            $orderReturn->status = 'Pending';
                        }

                        $orderReturn->processed_at = new DateTime();
                        // $orderCancellation = $this->OrderCancellations->newEmptyEntity();
                        // $orderCancellation->order_item_id = $item->id;
                        // $orderCancellation->order_id = $order->id;
                        // $orderCancellation->order_cancellation_category_id = $order_cancellation_category_id;
                        // $orderCancellation->reason = $reason;
                        // $orderCancellation->customer_id = $order->customer_id;
                        if ($this->OrderReturns->save($orderReturn)) {
                            $lastOrderRejected = $orderReturn; // Store last saved record
                        }
                    }
                }
                if ($lastOrderRejected) {
                    $this->sendReturnedEmails($order, $lastOrderRejected, $condition);
                    if ($condition == 'Approved') {
                        $response = [
                            'status' => 'success',
                            'message' => __('Order return has been approved.')
                        ];
                    } else if ($condition == 'Rejected') {
                        $response = [
                            'status' => 'success',
                            'message' => __('Order return has been rejected.')
                        ];
                    }
                } else {
                    $response = [
                        'status' => 'error',
                        'message' => __('Order was not returned before and email not sent to client.')
                    ];
                }
            } else {
                $response = [
                    'status' => 'error',
                    'message' => __('Unable to retunr the order. Please try again.')
                ];
            }
        }

        return $this->response
            ->withType('application/json')
            ->withStringBody(json_encode($response));
    }
    private function sendReturnedEmails($order, $orderReturn, $condition)
    {
        if ($order && $orderReturn) {
            $toEmails = [$order->customer->user->email];

            if (empty($toEmails)) {
                \Cake\Log\Log::warning("No valid recipients found for order ID: {$order->id}");
                return;
            }

            $emailData = [
                'order_number' => $order->order_number,
                'customer_name' => $order->customer->user->first_name.' '.$order->customer->user->last_name,
                'reason' => $orderReturn->reason,
                'requested_at' => $orderReturn->requested_at,
                'processed_at' => $orderReturn->processed_at,
                'status' =>$orderReturn->status
            ];

            $subject = "Order #{$order->order_number} Return has been ".$condition;

            $this->Global->send_email(
                $toEmails,
                null,
                $subject,
                'order_cancellation',
                $emailData
            );
        }
    }

    /**
     * Send order status email to customer (reusable for confirmed/rejected status)
     */
    private function sendOrderStatusEmail($order, $statusType)
    {
        try {
            // Get order with customer and order items
            $orderWithDetails = $this->Orders->find()
                ->contain([
                    'Customers' => ['Users'],
                    'OrderItems' => ['Products'],
                    'Transactions'
                ])
                ->where(['Orders.id' => $order->id])
                ->first();

            if (!$orderWithDetails || !$orderWithDetails->customer || !$orderWithDetails->customer->user) {
                $this->log("Order {$statusType} email skipped: No customer information found for order " . $order->order_number, 'info');
                return false;
            }

            $customer = $orderWithDetails->customer;
            $user = $customer->user;
            $customerEmail = $user->email;
            $customerName = trim($user->first_name . ' ' . $user->last_name);

            // Get delivery address (only for confirmed orders)
            $deliveryAddress = 'Address not available';
            if ($statusType === 'confirmed' && $orderWithDetails->customer_address_id) {
                $deliveryAddress = $this->getFormattedDeliveryAddress($orderWithDetails->customer_address_id);
            }

            // Prepare order items for email
            $emailOrderItems = [];
            foreach ($orderWithDetails->order_items as $item) {
                $emailOrderItems[] = [
                    'product_name' => $item->product ? $item->product->name : 'Product',
                    'quantity' => $item->quantity,
                    // 'price_formatted' => $this->formatCurrency($item->price),
                    // 'total_formatted' => $this->formatCurrency($item->price * $item->quantity)
                     'price_formatted' => $this->Orders->formatOrderAmount($item->price,$orderWithDetails->country_id ),
                    'total_formatted' => $this->Orders->formatOrderAmount(($item->price * $item->quantity),$orderWithDetails->country_id )
                    
                ];
            }

            // Prepare common email data
            $emailData = [
                'customer_name' => $customerName,
                'customer_email' => $customerEmail,
                'order_number' => $orderWithDetails->order_number,
                'order_date' => $orderWithDetails->order_date->format('M d, Y'),
                'payment_method' => $orderWithDetails->payment_method,
               // 'total_amount_formatted' => $this->formatCurrency($orderWithDetails->total_amount),
               'subtotal'=>$this->Orders->formatOrderAmount($orderWithDetails->subtotal_amount,$orderWithDetails->country_id ),
               'discount_amount'=>$this->Orders->formatOrderAmount($orderWithDetails->discount_amount,$orderWithDetails->country_id ),
                'total_amount_formatted' => $this->Orders->formatOrderAmount($orderWithDetails->total_amount,$orderWithDetails->country_id ),
                'tax_amount'=>$this->Orders->formatOrderAmount($orderWithDetails->tax_amount,$orderWithDetails->country_id ),
                'installation_amount'=>$this->Orders->formatOrderAmount($orderWithDetails->installation_amount,$orderWithDetails->country_id ),
                'order_items' => $emailOrderItems,
                'delivery_charge'=>$this->Orders->formatOrderAmount($orderWithDetails->delivery_charge,$orderWithDetails->country_id ),
                'order_notes' => $orderWithDetails->order_notes,
                'contact_support_url' => $this->request->scheme() . '://' . $this->request->host() . '/support'
            ];

            // Add status-specific data and configure email
            if ($statusType === 'confirmed') {
                $emailData['confirmation_date'] = date('M d, Y');
                $emailData['delivery_address'] = $deliveryAddress;
                $emailData['track_order_url'] = $this->request->scheme() . '://' . $this->request->host() . '/account/orders';
                $subject = 'Order Confirmed - ' . $orderWithDetails->order_number;
                $template = 'cod_order_confirmation';
                $logAction = 'confirmation';
            } else { // rejected
                $emailData['rejection_reason'] = __('Order could not be processed due to administrative reasons.');
                $emailData['rejection_date'] = date('M d, Y');
                $emailData['browse_products_url'] = $this->request->scheme() . '://' . $this->request->host() . '/products';
                $subject = 'Order Rejected - ' . $orderWithDetails->order_number;
                $template = 'order_rejected';
                $logAction = 'rejection';
            }

            // Send email
            $emailResult = $this->Global->send_email(
                $customerEmail,
                null,
                $subject,
                $template,
                $emailData
            );

            if ($emailResult) {
                $this->log("Order {$logAction} email sent successfully for order: " . $orderWithDetails->order_number, 'info');
                return true;
            } else {
                $this->log("Failed to send order {$logAction} email for order: " . $orderWithDetails->order_number, 'error');
                return false;
            }

        } catch (\Exception $e) {
            $this->log("Error sending order {$statusType} email: " . $e->getMessage(), 'error');
            return false;
        }
    }

    /**
     * Get formatted delivery address for email
     */
    private function getFormattedDeliveryAddress($customerAddressId)
    {
        $address = $this->CustomerAddresses->find()
            ->contain([
                'Cities' => function ($q) {
                    return $q->select(['id', 'city_name', 'state_id']);
                },
                'States' => function ($q) {
                    return $q->select(['id', 'state_name', 'country_id'])
                        ->contain(['Countries' => function ($q2) {
                            return $q2->select(['id', 'name']);
                        }]);
                }
            ])
            ->where(['CustomerAddresses.id' => $customerAddressId])
            ->first();

        if (!$address) {
            return 'Address not available';
        }

        $addressParts = [];

        // Add house number if available
        if (!empty($address->house_no)) {
            $addressParts[] = $address->house_no;
        }

        // Add address line 1
        if (!empty($address->address_line1)) {
            $addressParts[] = $address->address_line1;
        }

        // Add address line 2
        if (!empty($address->address_line2)) {
            $addressParts[] = $address->address_line2;
        }

        // Add landmark
        if (!empty($address->landmark)) {
            $addressParts[] = $address->landmark;
        }

        // Add city
        if ($address->city && !empty($address->city->city_name)) {
            $addressParts[] = $address->city->city_name;
        }

        // Add state
        if ($address->state && !empty($address->state->state_name)) {
            $addressParts[] = $address->state->state_name;
        }

        // Add country
        if ($address->state && $address->state->country && !empty($address->state->country->name)) {
            $addressParts[] = $address->state->country->name;
        }

        // Add zipcode
        if (!empty($address->zipcode)) {
            $addressParts[] = $address->zipcode;
        }

        return implode(', ', array_filter($addressParts));
    }

    /**
     * Format currency amount
     */
    private function formatCurrency($amount)
    {
         $country = $this->getRequest()->getSession()->read('siteSettings.country') ?? 'Qatar';
        $currency = ($country == 'Qatar') ? 'QAR' : 'SAR';
        return number_format((float)$amount, 2) . " " . $currency;
        // $currencyConfig = \Cake\Core\Configure::read('Settings.Currency.format');
        // $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : 'QAR';
        // $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '.';
        // $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : ',';

        // return number_format((float)$amount, 2, $decimalSeparator, $thousandSeparator) . ' ' . $currencySymbol;
    }
}
