<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * OrderCancellations Model
 *
 * @property \App\Model\Table\OrdersTable&\Cake\ORM\Association\BelongsTo $Orders
 * @property \App\Model\Table\OrderItemsTable&\Cake\ORM\Association\BelongsTo $OrderItems
 * @property \App\Model\Table\OrderCancellationCategoriesTable&\Cake\ORM\Association\BelongsTo $OrderCancellationCategories
 *
 * @method \App\Model\Entity\OrderCancellation newEmptyEntity()
 * @method \App\Model\Entity\OrderCancellation newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\OrderCancellation> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\OrderCancellation get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\OrderCancellation findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\OrderCancellation patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\OrderCancellation> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\OrderCancellation|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\OrderCancellation saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\OrderCancellation>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\OrderCancellation>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\OrderCancellation>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\OrderCancellation> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\OrderCancellation>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\OrderCancellation>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\OrderCancellation>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\OrderCancellation> deleteManyOrFail(iterable $entities, array $options = [])
 */
class OrderCancellationsTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('order_cancellations');
        $this->setDisplayField('reason');
        $this->setPrimaryKey('id');

        $this->belongsTo('Orders', [
            'foreignKey' => 'order_id',
        ]);
        $this->belongsTo('OrderItems', [
            'foreignKey' => 'order_item_id',
        ]);
        $this->belongsTo('OrderCancellationCategories', [
            'foreignKey' => 'order_cancellation_category_id',
            'joinType' => 'INNER',
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->nonNegativeInteger('order_id')
            ->allowEmptyString('order_id');

        $validator
            ->nonNegativeInteger('order_item_id')
            ->allowEmptyString('order_item_id');

        // $validator
        //     ->nonNegativeInteger('order_cancellation_category_id')
        //     ->notEmptyString('order_cancellation_category_id');

        $validator
            ->scalar('reason')
            ->maxLength('reason', 255)
            ->requirePresence('reason', 'create')
            ->notEmptyString('reason');

        $validator
            ->scalar('status')
            ->notEmptyString('status');

        $validator
            ->dateTime('canceled_at')
            ->requirePresence('canceled_at', 'create')
            ->notEmptyDateTime('canceled_at');

        $validator
            ->dateTime('updated')
            ->notEmptyDateTime('updated');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->existsIn(['order_id'], 'Orders'), ['errorField' => 'order_id']);
        $rules->add($rules->existsIn(['order_item_id'], 'OrderItems'), ['errorField' => 'order_item_id']);
        $rules->add($rules->existsIn(['order_cancellation_category_id'], 'OrderCancellationCategories'), ['errorField' => 'order_cancellation_category_id']);

        return $rules;
    }

    //M
    public function add_record($attributes) {

        // echo "<pre>"; print_r($attributes); die;
        $new = $this->newEmptyEntity();
         foreach($attributes as $key => $value) {
             $new->$key = $value;
         }
 
         if($this->save($new)) {
             return $new->id;
         } else {
             return false;
         }
    }
    //Ax
    public function add_record_data($attributes) {
        $fields = [
            'order_id',
            'customer_id',
            'order_item_id',
            'order_cancellation_category_id',
            'reason',
            'status',
            'canceled_at'
        ];

        $new = $this->newEmptyEntity();
        foreach ($fields as $field) {
            if (isset($attributes[$field])) {
                $new->$field = $attributes[$field];
            }
        }

        if ($this->save($new)) {
            return $new->id;
        } else {
            return false;
        }
    }

    //M
    public function update_record($id, $attributes) {
        $old_attr = $this->get($id);
        $old_attr = $this->patchEntity($old_attr, $attributes);
        if ($this->save($old_attr)) {
            return $old_attr;
        } else {
            return false;
        }
    }
}
