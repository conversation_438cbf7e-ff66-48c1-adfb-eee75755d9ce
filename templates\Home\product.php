   <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css" />
   <style>
div#ajaxReviewsContainer {
    max-height: 500px;
    overflow-y: auto;
}
</style>

    <section class="my-1 my-md-5 d-none d-lg-block">
        <div class="container">
            <nav aria-label="breadcrumb" class="">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/"><?= __('Home') ?></a></li>
                    <?php if (!empty($productData['product_categories'][0]['category'])): ?>
                        <li class="breadcrumb-item">
                            <a href="/product-list/<?= h($productData['product_categories'][0]['category']['url_key']) ?>">
                                <?= h($productData['product_categories'][0]['category']['name']) ?>
                            </a>
                        </li>
                    <?php endif; ?>
                    <li class="breadcrumb-item active" aria-current="page"><?= $productData['name'] ?></li>
                </ol>
            </nav>
        </div>
    </section>
    <!-- Brand Banner -->
    <section class="product-details-banner py-lg-2">
        <div class="container ">
            <div class="row mobile-reverse-product">
                <!-- Product Info -->
                <div class="col-md-5">
                    <h1 class="fw-bold product-title"><?= $productData['name'] ?></h1>
                    <div class="rating mb-2 d-flex">
                        <?php
                            $avg = floatval($productData['avg_rating']);
                            $fullStars = floor($avg);
                            $halfStar = ($avg - $fullStars) >= 0.5 ? 1 : 0;
                            $emptyStars = 5 - $fullStars - $halfStar;
                            for ($i = 0; $i < $fullStars; $i++) {
                                echo '<i class="fas fa-star"></i>';
                            }
                            if ($halfStar) {
                                echo '<i class="fas fa-star-half-alt"></i>';
                            }
                            for ($i = 0; $i < $emptyStars; $i++) {
                                echo '<i class="far fa-star"></i>';
                            }
                        ?>
                        <p class="rating-value"><?= $productData['avg_rating'] ?> / 5</p>
                        <?php if (!empty($productData['avg_rating'])): ?>
                            <span class="text-muted ms-2">(<?= $productData['avg_rating'] ?>)</span>
                        <?php endif; ?>
                    </div>
                    <p><?= $productData['description'] ?></p>

                    <!-- <div class="d-flex model-text">
                        <p>Model: <strong>ASG18CAXTA-U</strong> </p>
                    </div> -->
                  
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            
                            <div class="">
                                <h1 class="price-range"><?= $this->Price->setPriceFormat($productData['promotion_price']) ?></h1>
                                <label class="form-label"><?= __('Unit(s) without installation') ?></label>
                            </div>
                            <?php if ($productData->is_in_cart && $productData->cart_quantity >= 1): ?>
                            <div class="input-group increment-decrement">
                                <button class="btn" type="button" onclick="updateQuantityDynamic('unitInput2', -1)"><img
                                        src="../../img/ozone/minus.png" class="img-fluid" /></button>
                                <input type="text" id="unitInput2" class="form-control text-center"
                                       value="<?= $productData->is_in_cart ? $productData->cart_quantity : 1 ?>"
                                       data-product-id="<?= $productData->id ?>"
                                       data-original-value="<?= $productData->is_in_cart ? $productData->cart_quantity : 1 ?>">
                                <button class="btn" type="button" onclick="updateQuantityDynamic('unitInput2', 1)"><img
                                        src="../../img/ozone/plus.png" class="img-fluid" /></button>
                            </div>
                         <?php endif; ?>
                        </div>

                        <!-- Installation Charge Checkbox -->
                     
                        <div class="form-check mt-2">
                            <input class="form-check-input" type="checkbox" value="1" id="installationChargeCheckbox"
                                <?= ($productData->is_in_cart && !empty($productData->cart_installation_charge)) ? 'checked' : '' ?>>
                            <label class="form-check-label" for="installationChargeCheckbox">
                                <?= __('Add Installation Charge') ?> (<?= $this->Price->setPriceFormat($productData['installation_charge']) ?>)
                            </label>
                        </div>
                    </div>
                    <div class="purchase-cta d-none d-lg-block">
                        <?php if (!$productData->is_in_cart): ?>
                            <button class="btn btn-add-cart w-100" id="addToCartBtn" data-product-id="<?= $productData->id ?>">
                                <i class="fas fa-cart-plus me-2"></i>   <?= __('Add to Cart') ?>
                            </button>
                        <?php else: ?>
                            <button class="btn btn-add-cart w-100" onclick="window.location.href='<?= $this->Url->build(['controller' => 'Cart', 'action' => 'cart']) ?>'">
                                <i class="fas fa-shopping-cart me-2"></i> <?= __('Go to Cart') ?>
                            </button>
                        <?php endif; ?>
                        <div class="wishlist-container" data-product-id="<?= $productData->id ?>">
                            <?php if ($productData->whishlist): ?>
                                <button class="btn btn-wishlist w-100 <?= !$productData->is_in_cart ? 'mt-2' : '' ?> d-flex justify-content-center align-items-center remove-wishlist-btn">
                                    <i class="fas fa-heart text-danger me-2"></i> <?= __('Remove from Wishlist') ?>
                                </button>
                            <?php else: ?>
                                <button class="btn btn-wishlist w-100 <?= !$productData->is_in_cart ? 'mt-2' : '' ?> d-flex justify-content-center align-items-center add-wishlist-btn">
                                    <i class="far fa-heart text-success me-2"></i> <?= __('Add to Wishlist') ?>
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                   
                </div>
                <div class="col-md-7 product-gallery">
                    <!-- Main Swiper -->
                    <div class="swiper-counter">
                        <span class="current-slide">01</span> / <span class="total-slides">04</span>
                    </div>

                    <!-- Navigation buttons -->
                    <div class="swiper-navigation">
                        <div class="swiper-button-prev custom-nav"></div>
                        <div class="swiper-button-next custom-nav"></div>
                    </div>
                    <!-- Share Dropdown (Bootstrap - click only) -->
                    <div class="dropdown d-inline-block position-relative share-icons">
                        <button class="btn" type="button" id="shareDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <img src="../../img/ozone/Share-prdt.png" class="img-fluid" alt="Share" />
                        </button>

                        <ul class="dropdown-menu dropdown-menu-end shadow social-list" aria-labelledby="shareDropdown" style="">
                            <li>
                                <a class="dropdown-item d-flex align-items-center" href="#" onclick="shareViaEmail(); return false;">
                                    <img src="../../img/ozone/share-email.png" class="img-fluid me-2" width="25"/> Email
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item d-flex align-items-center" href="#" onclick="shareViaWhatsApp(); return false;" target="_blank">
                                    <img src="../../img/ozone/share-whatsapp.png" class="img-fluid me-2" width="25"/> Whatsapp
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item d-flex align-items-center" href="#" onclick="shareViaFacebook(); return false;" target="_blank">
                                    <img src="../../img/ozone/share-facebook.png" class="img-fluid me-2" width="25"/> Facebook
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item d-flex align-items-center" href="#" onclick="shareViaTwitter(); return false;" target="_blank">
                                    <img src="../../img/ozone/share-twitter.png" class="img-fluid me-2" width="25"/> Twitter
                                </a>
                            </li>
                            <li>
                                <button class="dropdown-item d-flex align-items-center" onclick="copyShareLink()">
                                    <img src="../../img/ozone/share-link.png" class="img-fluid me-2" width="25"/> Copy Link
                                </button>
                            </li>
                        </ul>
                    </div>
                    <div class="swiper main-swiper w-full">
                        <div class="swiper-wrapper">
                            <?php foreach ($productData->product_images as $image): ?>
                                <div class="swiper-slide w-full flex justify-center items-center">
                                    <div class="tile" data-scale="1.6">
                                        <div class="photo">
                                            <img src="<?= $image->image ?>" class="img-fluid w-full h-auto object-contain" alt="Product" />
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>


                    <div class="thumbnail-images">
                        <!-- Thumbnails -->
                        <div class="swiper thumb-swiper mt-3">
                            <div class="swiper-wrapper">
                                <?php foreach ($productData->product_images as $image): ?>
                                    <div class="swiper-slide">
                                        <img src="<?= $image->image ?>" class="img-fluid" alt="Thumb 1">
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                    <!-- Thumbnails -->
                </div>
            </div>
        </div>
         <div class="purchase-cta d-lg-none">
                        <?php if (!$productData->is_in_cart): ?>
                            <button class="btn btn-add-cart w-100" id="addToCartBtn" data-product-id="<?= $productData->id ?>">
                                <i class="fas fa-cart-plus me-2"></i>   <?= __('Add to Cart') ?>
                            </button>
                        <?php else: ?>
                            <button class="btn btn-add-cart w-100" onclick="window.location.href='<?= $this->Url->build(['controller' => 'Cart', 'action' => 'cart']) ?>'">
                                <i class="fas fa-shopping-cart me-2"></i> <?= __('Go to Cart') ?>
                            </button>
                        <?php endif; ?>
                        <div class="wishlist-container" data-product-id="<?= $productData->id ?>">
                            <?php if ($productData->whishlist): ?>
                                <button class="btn btn-wishlist w-100 <?= !$productData->is_in_cart ? 'mt-2' : '' ?> d-flex justify-content-center align-items-center remove-wishlist-btn">
                                    <i class="fas fa-heart text-danger me-2"></i> <?= __('Remove from Wishlist') ?>
                                </button>
                            <?php else: ?>
                                <button class="btn btn-wishlist w-100 <?= !$productData->is_in_cart ? 'mt-2' : '' ?> d-flex justify-content-center align-items-center add-wishlist-btn">
                                    <i class="far fa-heart text-success me-2"></i> <?= __('Add to Wishlist') ?>
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
    </section>

    <section class="scroll-spy my-5 desktop">
        <!-- Nav tabs -->
        <div class="container nav-tabs-scroll">
            <ul class="nav justify-content-around" id="scrollNav">
                <li class="nav-item">
                    <a class="nav-link active" href="#features"><?= __('Features') ?></a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#gallery"><?= __('Gallery') ?></a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#specs"><?= __('Specifications') ?></a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#review"><?= __('Review') ?></a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#help"><?= __('Need Help?') ?></a>
                </li>
            </ul>
        </div>
    </section>
    <!-- Leading Company Section -->

    <section class="product-features" id="features">
        <div class="container">
        <!-- Full-width feature image -->
            <?= $productData['features'] ?>
        </div>
    </section>

    
    <section class="gallery mb-5" id="gallery">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center mb-3 gap-3">
                <h1 class="sec-title fw-bold m-0"><?= __('Gallery') ?></h1>
                <button class="zoom-btn" onclick="toggleZoomFromTitle()">
                    <img id="zoom-icon"  class="img-fluid" src="../../img/ozone/search-zoom-in.png" alt="Zoom" width="60">
                </button>
            </div>

            <div id="gallerycarousel" class="carousel slide">
                <div class="carousel-inner">
                    <?php foreach ($productData->product_images as $index => $image): ?>
                        <div class="carousel-item <?= $index === 0 ? 'active' : '' ?>">
                            <img src="<?= h($image->image) ?>" class="d-block w-100 mb-5 pb-5 zoomable" alt="Gallery Image <?= $index + 1 ?>">
                        </div>
                    <?php endforeach; ?>
                </div>

                <button class="carousel-control-prev" type="button" data-bs-target="#gallerycarousel"
                    data-bs-slide="prev">
                    <span class="carousel-control-prev" aria-hidden="true"><img src="../../img/ozone/prv.png"
                            class="img-fluid" /></span>
                    <span class="visually-hidden"><?= __('Previous') ?></span>
                </button>
                <button class="carousel-control-next" type="button" data-bs-target="#gallerycarousel"
                    data-bs-slide="next">
                    <span class="carousel-control-next" aria-hidden="true"><img src="../../img/ozone/nxt-pro.png"
                            class="img-fluid" /></span>
                    <span class="visually-hidden"><?= __('Next') ?></span>
                </button>
            </div>
        </div>
    </section>


    <section class="Specifications my-5" id="specs">
        <div class="container">
            <h1 class="sec-title fw-bold my-4">
                <?= __("Specifications") ?>
            </h1>

            <?= $productData['details'] ?>

        </div>
    </section>

    <section class="reviews" id="review">
        <div class="container my-5">
            <h2 class="sec-title fw-bold"><?= __('Reviews') ?> <span class="float-end text-success"><?= $productData['avg_rating'] ?> ★</span></h2>

            <div class="row my-4">
                <div class="col-md-5">
                    <div class="rounded p-3 bg-white">
                        <div class="d-flex align-items-center">
                            <h4><?= $productData['avg_rating'] ?> <span class="star-rating">★★★★★</span></h4>
                            <p class="mb-1 total-rating"><?= __('Based on') ?> <?= count($productData['reviews']) ?> <?= __('ratings') ?></p>
                        </div>
                        <div>
                            <h3 class="mt-3"><?= __('Rating') ?></h3>
                            <?php
                                $totalRatings = (
                                    (int)($productData['rating_1_count'] ?? 0) +
                                    (int)($productData['rating_2_count'] ?? 0) +
                                    (int)($productData['rating_3_count'] ?? 0) +
                                    (int)($productData['rating_4_count'] ?? 0) +
                                    (int)($productData['rating_5_count'] ?? 0)
                                );
                                $ratings = [
                                    5 => (int)($productData['rating_5_count'] ?? 0),
                                    4 => (int)($productData['rating_4_count'] ?? 0),
                                    3 => (int)($productData['rating_3_count'] ?? 0),
                                    2 => (int)($productData['rating_2_count'] ?? 0),
                                    1 => (int)($productData['rating_1_count'] ?? 0),
                                ];
                                foreach ($ratings as $star => $count):
                                    $percent = $totalRatings > 0 ? round(($count / $totalRatings) * 100) : 0;
                            ?>
                                <div class="mb-1 progress-d-flex">
                                    <pre class="ratings"><?= $star ?> <?= __('Star') ?></pre>
                                    <div class="progress" role="progressbar" aria-label="Basic example" aria-valuenow="<?= $percent ?>"
                                        aria-valuemin="0" aria-valuemax="100">
                                        <div class="progress-bar" style="width: <?= $percent ?>%"></div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                <div class="col-md-7 review-product d-flex align-items-center justify-content-center">
                    <div class="text-center" style="cursor: pointer;" onclick="openProductReviewModal()" id="reviewProductSection">
                        <h3 class=""><?= __('Review this Product') ?></h3>
                        <div class="fs-1 star-rating">★★★★★</div>
                        <p><?= __('Help other customers make their decision') ?></p>
                        <div id="reviewStatus" class="mt-2" style="display: none;">
                            <span class="badge bg-success"><?= __('You have reviewed this product') ?></span>
                        </div>
                    </div>
                </div>
            </div>

            <hr class="my-5">


            <div class="d-flex justify-content-between mb-3">
                <h4 class="customer-reviews-title"><?= __('Customer Reviews') ?></h4>
                <div>
                    <select id="reviewSortBy" class="form-select form-select-sm" style="width:auto;display:inline-block;">
                        <option value="newest"><?= __('Newest First') ?></option>
                        <option value="oldest"><?= __('Oldest First') ?></option>
                        <option value="highest"><?= __('Highest Rated') ?></option>
                        <option value="lowest"><?= __('Lowest Rated') ?></option>
                    </select>
                </div>
            </div>

            <!-- AJAX Reviews Container -->
            <div id="ajaxReviewsContainer"></div>
            <!-- No more Load More Button -->
            <div class="text-center mt-5 loadmore">
                <div id="reviewsLoadingSpinner" style="display:none;">
                    <span class="spinner-border spinner-border-sm"></span> <?= __('Loading...') ?>
                </div>
                <div id="noMoreReviewsMsg" class="text-muted mt-3" style="display:none;"><?= __('No more reviews found.') ?></div>
                <div id="noReviewsMsg" class="alert alert-warning mt-3" style="display:none;"><?= __('No reviews found.') ?></div>
            </div>
        </div>
    </section>

    <section class="need-help" id="help">
        <div class="container py-5">
            <h2 class="sec-title fw-bold"><?= __('Need help?') ?></h2>
            <p class="text-success"><?= __('We’re here to provide all the help you need.') ?></p>

            <div class="row g-4 mt-4">
                <!-- Card 1 -->
                <div class="col-md-6 col-lg-3">
                    <a href="<?= h($productData->catalogue) ?>" target="_blank" class="text-decoration-none text-dark">
                    <div class="p-4 help-card h-100">
                        <h5 class="fw-bold mb-4"><?= __('Download') ?><br><?= __('Product Manual') ?></h5>
                        <p class="mb-5"><?= __('Download product manuals and latest software for your product.') ?></p>
                        <div class="arrow-circle mt-3"><img src="../../img/ozone/arrow-left.svg" class="img-fluid" /></div>
                    </div>
                    </a>
                </div>

                <!-- Card 2 -->
                <div class="col-md-6 col-lg-3">
                    <a href="/cms/troubleshoot" class="text-decoration-none text-dark">
                        <div class="p-4 help-card h-100">
                            <h5 class="fw-bold mb-4"><?= __('Troubleshoot') ?></h5>
                            <p class="mb-5"><?= __('Find helpful how-to videos for your product.') ?></p>
                            <div class="arrow-circle mt-3"><img src="../../img/ozone/arrow-left.svg" class="img-fluid" /></div>
                        </div>
                    </a>
                </div>

                <!-- Card 3 -->
                <div class="col-md-6 col-lg-3">
                    <a href="/cms/warranty" class="text-decoration-none text-dark">
                        <div class="p-4 help-card h-100">
                            <h5 class="fw-bold mb-4"><?= __('Warranty') ?></h5>
                            <p class="mb-5"><?= __('Check your product warranty information here.') ?></p>
                            <div class="arrow-circle mt-3"><img src="../../img/ozone/arrow-left.svg" class="img-fluid" /></div>
                        </div>
                    </a>
                </div>

                <!-- Card 4 -->
                <div class="col-md-6 col-lg-3">
                    <a href="/contact-us" class="text-decoration-none text-dark">
                        <div class="p-4 help-card h-100">
                            <h5 class="fw-bold mb-4"><?= __('Repair request') ?></h5>
                            <p class="mb-5"><?= __('Repair request service conveniently online.') ?></p>
                            <div class="arrow-circle mt-3"><img src="../../img/ozone/arrow-left.svg" class="img-fluid" /></div>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Latest Offers Section -->
    <?php if (isset($productData['related_products']) && is_array($productData['related_products']) && !empty($productData['related_products'])): ?>
    <section class="latest-offers py-5 latest-product">
    <div class="container">
    <div class="d-flex justify-content-between align-items-center mb-5">
        <h2 class="section-title mb-0"><?= __('Related Products') ?></h2>
    </div>
    <div class="row">
        <div class="large-12 columns relative p-0">
            <div class="owl-carousel owl-theme " id="general-carousel">
                <?php foreach ($productData['related_products'] as $product): ?>
                    <div class="item">
                        <div class="product-card clickable-product-card" data-product-id="<?= $product->url_key ?>">
                            <div class="position-relative substract">
                                <img src="<?= $product->product_image ?>" alt="Air Conditioner"
                                    class="img-fluid w-100 ">
                                <div class=" position-absolute">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div> <?php if (isset($product->discount) && $product->discount > 0): ?>
                                                            <div class="badge"><?= h($product->discount) ?>% <?= __('Off') ?></div>
                                        <?php endif; ?></div>
                                        <div class="add-cart add-to-cart-btn" data-product-id="<?= $product->url_key ?>">
                                            <span class="bor-top-rig"></span>
                                            <span class="bor-bot-left"></span>
                                            <div class="energy-rating">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="38" height="38"
                                                    viewBox="0 0 38 38" fill="none">
                                                    <path
                                                        d="M11.8 26.2L27.4962 24.8919C32.4075 24.4827 33.51 23.41 34.0543 18.5119L35.2 8.19995"
                                                        stroke="white" stroke-width="2" stroke-linecap="round" />
                                                    <path d="M8.19995 8.19995H9.09995M37 8.19995H32.5" stroke="white"
                                                        stroke-width="2" stroke-linecap="round" />
                                                    <path d="M14.5 8.1999H27.1M20.8 14.4999V1.8999" stroke="white"
                                                        stroke-width="2" stroke-linecap="round" />
                                                    <circle cx="8.19985" cy="33.4" r="3.6" stroke="white"
                                                        stroke-width="2" />
                                                    <circle cx="28.0001" cy="33.4" r="3.6" stroke="white"
                                                        stroke-width="2" />
                                                    <path d="M11.8001 33.3999L24.4001 33.3999" stroke="white"
                                                        stroke-width="2" stroke-linecap="round" />
                                                    <path
                                                        d="M1 1H2.7388C4.43923 1 5.92145 2.12427 6.33387 3.72687L11.6893 24.5377C11.96 25.5894 11.7284 26.7035 11.0588 27.5708L9.33784 29.8"
                                                        stroke="white" stroke-width="2" stroke-linecap="round" />
                                                </svg>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                            <div class="pt-3 px-3">
                                <h5 class="text-truncate-2-line"><?= $product->name ?></h5>
                                    <?php
                                        $rating = floatval($product->avg_rating);
                                        $fullStars = floor($rating);
                                        $halfStar = ($rating - $fullStars) >= 0.5 ? 1 : 0;
                                        $emptyStars = 5 - $fullStars - $halfStar;
                                    ?>
                                    <div class="rating mb-2">
                                        <?php for ($i = 0; $i < $fullStars; $i++): ?>
                                            <i class="fas fa-star"></i>
                                        <?php endfor; ?>
                                        <?php if ($halfStar): ?>
                                            <i class="fas fa-star-half-alt"></i>
                                        <?php endif; ?>
                                        <?php for ($i = 0; $i < $emptyStars; $i++): ?>
                                            <i class="far fa-star"></i>
                                        <?php endfor; ?>
                                        <span class="text-muted ms-2">(<?= number_format($rating, 0) ?>)</span>
                                    </div>
                                        <div class="price">
                                            <div>
                                                <?= $this->Price->setPriceFormat($product->promotion_price) ?> 
                                            </div>
                                            <?php if ($product->sales_price > $product->promotion_price): ?>
                                                <div class="text-muted text-decoration-line-through small mb-1">
                                                    <?= $this->Price->setPriceFormat($product->sales_price) ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                            </div>
                        </div>
                    </div>
                
                <?php endforeach; ?>
        
            </div>
        </div>
    </div>
    </div>
    </section>
    <?php endif; ?>
    
    <div class="modal fade " id="loginmodal" aria-hidden="true" aria-labelledby="loginmodalLabel" tabindex="-1">
        <div class="modal-dialog modal-fullscreen modal-dialog-right">
            <div class="modal-content login">
                <div class="login-container">
                    <div class="position-relative">
                        <!-- <span class="substract"></span> -->
                        <div class="container substract">
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><img
                                    src="../../img/ozone/close-circle.png" width="40" height="40" /></button>
                            <div class="form-login my-5">
                                <div class="logo">
                                    <img src="../../img/ozone/logo.png" class="img-fluid">
                                    <span style="font-weight: 600;"><?= __('Log in') ?></span>
                                </div>
                                <!-- Social Icons -->
                                <div class="social-icons my-5">
                                    <button><img src="https://img.icons8.com/color/24/google-logo.png" /><span><?= __('Sign in with Google') ?></span></button>
                                    <button><img src="https://img.icons8.com/ios-filled/24/facebook-new.png" /><span><?= __('Sign in with Facebook') ?></span></button>
                                    <!-- <button><img src="https://img.icons8.com/ios-filled/24/mac-os.png" /><span><?= __('Sign in with Apple') ?></span></button> -->
                                </div>

                                <!-- Divider -->
                                <div class="divider">OR</div>

                                <!-- Email Field -->
                                <div class="input-group email mb-4">
                                    <label><?= __('Enter your email address') ?></label>
                                    <input type="text" placeholder="Username or email address" />
                                </div>

                                <!-- Password Field -->
                                <div class="input-group">
                                    <label><?= __('Enter your Password') ?></label>
                                    <input type="password" placeholder="Password" />
                                </div>

                                <div class="forgot-password"><?= __('Forgot Password?') ?></div>

                                <!-- Login Button -->
                                <button class="btn-glow btn-login my-5"><?= __('Log in') ?></button>

                                <!-- Register Button -->
                                <button class="btn-glow btn-register" data-bs-target="#loginmodal2"
                                    data-bs-toggle="modal" data-bs-dismiss="modal" aria-label="Close"><?= __('Register here!') ?></button>

                                <!-- Footer -->
                                <div class="footer-text">
                                    <?= __('if you don\'t have an account register, You can') ?>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="loginmodal2" aria-hidden="true" aria-labelledby="loginmodalLabel2" tabindex="-1">
        <div class="modal-dialog modal-fullscreen modal-dialog-right">
            <div class="modal-content login">

                <div class="login-container">
                    <div class="position-relative">
                        <!-- <span class="substract"></span> -->
                        <div class="container substract">
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><img
                                    src="../../img/ozone/close-circle.png" width="40" height="40" /></button>
                            <div class="form-login my-5 ">
                                <div class="logo">
                                    <img src="../../img/ozone/logo.png" class="img-fluid">
                                    <span style="font-weight: 600;"><?= __('Register') ?></span>
                                </div>
                                <!-- Social Icons -->
                                <div class="social-icons my-5">
                                    <button><img src="https://img.icons8.com/color/24/google-logo.png" /><span> <?= __('Sign in with Google') ?></span></button>
                                    <button><img src="https://img.icons8.com/ios-filled/24/facebook-new.png" /><span> <?= __('Sign in with Facebook') ?></span></button>
                                    <!-- <button><img src="https://img.icons8.com/ios-filled/24/mac-os.png" /><span> <?= __('Sign in with MacOS') ?></span></button> -->
                                </div>

                                <!-- Divider -->
                                <div class="divider"><?= __('OR') ?></div>
                                <label><?= __('Enter your email address') ?></label>
                                <div class="d-flex my-4 ">
                                    <!-- Email Field -->
                                    <div class="input-group email ">

                                        <input type="text" placeholder="Username or email address" />
                                    </div>
                                    <button class="verify-btn">
                                        <?= __('Verify Email') ?>
                                    </button>
                                </div>
                                <div class="d-flex align-items-center mb-3">
                                    <!-- Email Field -->
                                    <div class="input-group email ">
                                        <input type="text" placeholder="Verification Code" />
                                        <span class="timer-inside" id="countdown">07:00</span>
                                    </div>
                                    <button class="verify-btn ms-2" onclick="restartTimer()">Resend Code</button>
                                </div>
                                <div class="error-text mb-5">Verification Code is required</div>
                                <!-- Email -->


                                <!-- Full Name -->
                                <div class="mb-4 input-group ">
                                    <label class="form-label"><?= __('Full Name') ?></label>
                                    <input type="text" class="form-control rounded-pill"
                                        placeholder="<?= __('Enter your full Name') ?>" />
                                </div>

                                <!-- Password -->
                                <div class="mb-4 input-group ">
                                    <label class="form-label"><?= __('Password') ?></label>
                                    <input type="password" class="form-control rounded-pill"
                                        placeholder="Enter Password" />
                                </div>

                                <!-- Confirm Password -->
                                <div class="mb-4 input-group ">
                                    <label class="form-label"><?= __('Confirm Password') ?></label>
                                    <input type="password" class="form-control rounded-pill"
                                        placeholder="<?= __('Confirm Password') ?>" />
                                </div>

                                <!-- Terms -->
                                <div class="form-check mb-4 ">
                                    <input class="form-check-input" type="checkbox" id="terms" />
                                    <label class="form-check-label terms-condition" for="terms">
                                        <?= ("By signing up you agree to our") ?> <a href="" class="terms"><?= __('Terms & Conditions') ?></a>
                                    </label>
                                </div>



                                <!-- Register Button -->
                                <button class="btn-glow btn-login my-5" data-bs-target="#loginmodal"
                                    data-bs-toggle="modal" data-bs-dismiss="modal" aria-label="Close"><?= __('Register') ?>
                                    <?= __('Account') ?></button>


                                <!-- Login Button -->
                                <button class="btn-glow btn-register"><?= __('Log In') ?></button>

                                <div class="footer-text mb-4"><?= __('Already have an account?') ?></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<?php $this->append('add_js'); ?>

<script src="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    const thumbSwiper = new Swiper(".thumb-swiper", {
        spaceBetween: 10,
        slidesPerView: 5,
        freeMode: true,
        watchSlidesProgress: true,
    });

    const mainSwiper = new Swiper(".main-swiper", {
        spaceBetween: 10,
        slidesPerView: 1, // ✅ Only one image visible
        navigation: {
            nextEl: ".swiper-button-next",
            prevEl: ".swiper-button-prev",
        },
        thumbs: {
            swiper: thumbSwiper,
        },
        loop: false,
        on: {
            init: function () {
                const total = document.querySelector('.total-slides');
                const current = document.querySelector('.current-slide');
                if (total && current) {
                    total.textContent = String(this.slides.length).padStart(2, '0');
                    current.textContent = String(this.realIndex + 1).padStart(2, '0');
                }
            },
            slideChange: function () {
                const current = document.querySelector('.current-slide');
                if (current) {
                    current.textContent = String(this.realIndex + 1).padStart(2, '0');
                }
            }
        }
    });

    setTimeout(() => {
        mainSwiper.update();
        thumbSwiper.update();
    }, 300);
</script>


    
<script>
// --- AJAX Reviews with Infinite Scroll ---
(function() {
    const productId = <?= json_encode($productData['id']) ?>;
    let currentPage = 1;
    let currentSort = 'newest';
    let lastPage = 1;
    let loading = false;
    let reviewsLoaded = 0;
    const reviewsPerPage = 1; // Change as needed

    // Get CSRF token from meta tag (CakePHP default)
    const csrfToken = $('meta[name="csrfToken"]').attr('content');

    function renderReviewCard(review) {
        const stars = '★'.repeat(review.rating) + '☆'.repeat(5 - review.rating);
        const profilePhoto = review.Customers && review.Customers.profile_photo
            ? review.Customers.profile_photo
            : 'https://i.pravatar.cc/40';
        const reviewer = review.Users && review.Users.first_name && review.Users.last_name
            ? `${review.Users.first_name} ${review.Users.last_name}`
            : 'Anonymous';
        const reviewerRole = review.Users && review.Users.role
            ? review.Users.role
            : '';
        const date = review.created ? (new Date(review.created)).toLocaleDateString() : '';
        return `
            <div class="mb-4 p-4 bg-white review-card">
                <div class="review-header d-flex justify-content-between">
                    <div class="star-rating">${stars}</div>
                    <div class="review-footer">${date}</div>
                </div>
                <p class="mt-3">${review.review || ''}</p>
                <div class="d-flex align-items-center mt-3">
                    <img src="${profilePhoto}" class="rounded-circle me-2" alt="User">
                    <div>
                        <strong>${reviewer}</strong><br>
                        <small>${reviewerRole}</small>
                    </div>
                </div>
            </div>
        `;
    }

    function showLoading(show) {
        $('#reviewsLoadingSpinner').toggle(show);
    }

    function showNoMoreReviews(show) {
        $('#noMoreReviewsMsg').toggle(show);
    }

    function showNoReviews(show) {
        $('#noReviewsMsg').toggle(show);
    }

    function loadReviews(page = 1, sortBy = 'newest', append = false) {
        if (loading) return;
        loading = true;
        showLoading(true);
        showNoMoreReviews(false);
        showNoReviews(false);

        $.ajax({
            url: '/home/<USER>',
            method: 'POST',
            data: {
                product_id: productId,
                sortBy: sortBy,
                page: page,
                limit: reviewsPerPage
            },
            beforeSend: function(xhr) {
                if (csrfToken) {
                    xhr.setRequestHeader('X-CSRF-Token', csrfToken);
                }
            },
            success: function(res) {
                showLoading(false);
                loading = false;
                const reviews = Array.isArray(res.data) ? res.data : [];
                // Read new pagination keys
                let totalPages = 1;
                let hasMore = false;
                let currentPageResp = 1;
                if (res.pagination) {
                    totalPages = parseInt(res.pagination.total_pages, 10) || 1;
                    hasMore = !!res.pagination.has_more;
                    currentPageResp = parseInt(res.pagination.current_page, 10) || 1;
                }
                lastPage = totalPages;
                currentPage = currentPageResp;

                if (!append) {
                    $('#ajaxReviewsContainer').empty();
                    reviewsLoaded = 0;
                }

                reviews.forEach(function(review) {
                    $('#ajaxReviewsContainer').append(renderReviewCard(review));
                    reviewsLoaded++;
                });

                if (reviewsLoaded === 0) {
                    showNoReviews(true);
                    showNoMoreReviews(false);
                    return;
                }

                // Show "No more reviews" if no more pages
                if (!hasMore || currentPage >= totalPages) {
                    showNoMoreReviews(true);
                } else {
                    showNoMoreReviews(false);
                }
            },
            error: function() {
                showLoading(false);
                loading = false;
                if (!append) {
                    $('#ajaxReviewsContainer').empty();
                }
                showNoReviews(true);
                showNoMoreReviews(false);
            }
        });
    }

    // Initial load
    $(document).ready(function() {
        loadReviews(1, currentSort, false);
    });

    // Sort change
    $('#reviewSortBy').on('change', function() {
        currentSort = $(this).val();
        currentPage = 1;
        reviewsLoaded = 0;
        loadReviews(currentPage, currentSort, false);
    });

    // Infinite scroll for reviews
    $(window).on('scroll', function() {
        if (loading || currentPage >= lastPage) return;
        const reviewsBottom = $('#ajaxReviewsContainer').offset().top + $('#ajaxReviewsContainer').outerHeight();
        const windowBottom = $(window).scrollTop() + $(window).height();
        if (windowBottom + 100 >= reviewsBottom) {
            currentPage++;
            loadReviews(currentPage, currentSort, true);
        }
    });
})();

// Add to Cart functionality for product page
$(document).ready(function() {
    $('#addToCartBtn').on('click', function(e) {
        e.preventDefault();

        const productId = $(this).data('product-id');
        const quantity = parseInt($('#unitInput2').val()) || 1;
        const installationCharge = $('#installationChargeCheckbox').is(':checked') ? 1 : 0;
        const button = $(this);
        const originalContent = button.html();

        // Show loading state
        button.prop('disabled', true).html(`
            <i class="fas fa-spinner fa-spin me-2"></i> Adding...
        `);

        // Call the same addToCart function as list.php
        addToCartProduct(productId, quantity, installationCharge)
            .then(response => {
                if (response.status === 'success') {
                    // Update cart count if provided
                    if (response.cartCount !== undefined && window.CartUpdater) {
                        window.CartUpdater.updateCartCount(response.cartCount);
                    }

                    // Show success state
                    button.html(`
                        <i class="fas fa-check me-2"></i> Added!
                    `);

                    // Show success message
                    showToastMessage(response.message || 'Product added to cart successfully!', 'success');

                    // Update the page to show "Go to Cart" button after 2 seconds
                    setTimeout(() => {
                        // Reload page to update cart status
                        location.reload();
                    }, 2000);
                } else {
                    button.prop('disabled', false).html(originalContent);
                    showToastMessage(response.message || 'Failed to add product to cart', 'error');
                }
            })
            .catch(error => {
                console.error('Error adding to cart:', error);
                button.prop('disabled', false).html(originalContent);
                showToastMessage('Failed to add product to cart', 'error');
            });
    });
});

// Helper function for add to cart (same as list.php)
function addToCartProduct(productId, quantity = 1, installationCharge = 0) {
    return new Promise((resolve, reject) => {
        // Get CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                         document.querySelector('meta[name="csrfToken"]')?.getAttribute('content') ||
                         document.querySelector('input[name="_csrfToken"]')?.value ||
                         '<?= $this->request->getAttribute('csrfToken') ?>';

        $.ajax({
            headers: {
                'X-CSRF-Token': csrfToken
            },
            url: "<?= $this->Url->build(['controller' => 'Cart', 'action' => 'addToCart']) ?>/" + productId,
            type: 'POST',
            data: {
                quantity: quantity,
                installation_charge: installationCharge
            },
            success: function (response) {
                resolve(response);
            },
            error: function (xhr, status, error) {
                reject('An error occurred: ' + error);
            }
        });
    });
}

// Wishlist functionality is now handled by website.php layout
</script>

<?php
// Customer ID is already passed from the controller
// $customerId is available from the controller
$productId = $productData['id'] ?? null;
?>
<script>
function getShareUrl() {
    const baseUrl = "<?= $this->Url->build(null, ['fullBase' => true]) ?>";
    const country = localStorage.getItem('user_country') || 'qatar';
    const language = localStorage.getItem('user_language') || 'eng';
    return `${baseUrl}?country=${encodeURIComponent(country)}&language=${encodeURIComponent(language)}`;
}

function shareViaEmail() {
    const url = getShareUrl();
    const subject = encodeURIComponent("<?= $productData['name'] ?>");
    const body = encodeURIComponent(url);
    window.location.href = `mailto:?subject=${subject}&body=${body}`;
}

function shareViaWhatsApp() {
    const url = getShareUrl();
    const text = encodeURIComponent("<?= $productData['name'] ?> " + url);
    window.open(`https://wa.me/?text=${text}`, '_blank');
}

function shareViaFacebook() {
    const url = getShareUrl();
    const shareUrl = encodeURIComponent(url);
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${shareUrl}`, '_blank');
}

function shareViaTwitter() {
    const url = getShareUrl();
    const shareUrl = encodeURIComponent(url);
    window.open(`https://twitter.com/intent/tweet?url=${shareUrl}`, '_blank');
}

function copyShareLink() {
    const url = getShareUrl();
    
    // Modern browsers with clipboard API
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(url).then(() => {
            alert('Link copied to clipboard!');
        }).catch(() => {
            // Fallback if clipboard API fails
            fallbackCopyTextToClipboard(url);
        });
    } else {
        // Fallback for older browsers
        fallbackCopyTextToClipboard(url);
    }
}

function fallbackCopyTextToClipboard(text) {
    // Create a temporary textarea element
    const textArea = document.createElement('textarea');
    textArea.value = text;
    
    // Avoid scrolling to bottom
    textArea.style.top = '0';
    textArea.style.left = '0';
    textArea.style.position = 'fixed';
    textArea.style.opacity = '0';
    
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        const successful = document.execCommand('copy');
        if (successful) {
            alert('Link copied to clipboard!');
        } else {
            alert('Failed to copy link. Please copy manually: ' + text);
        }
    } catch (err) {
        console.error('Fallback: Could not copy text: ', err);
        alert('Failed to copy link. Please copy manually: ' + text);
    }
    
    document.body.removeChild(textArea);
}


    // Product card click functionality - redirect to product details
    $(document).on('click', '.clickable-product-card', function(e) {
        // Don't redirect if user clicked on cart button, wishlist button, or their children
        if ($(e.target).closest('.add-to-cart-btn, .wishlist, .add-cart').length > 0) {
            return;
        }

        const productId = $(this).data('product-id');
        if (productId) {
            // Redirect to product details page (Home controller, product method)
            window.location.href = `<?= $this->Url->build(['controller' => 'Home', 'action' => 'product']) ?>/${productId}`;
        }
    });
    // Product card click functionality - redirect to product details
    $(document).on('click', '.clickable-product-card', function(e) {
        // Don't redirect if user clicked on cart button, wishlist button, or their children
        if ($(e.target).closest('.add-to-cart-btn, .wishlist, .add-cart').length > 0) {
            return;
        }

        const productId = $(this).data('product-id');
        if (productId) {
            // Redirect to product details page (Home controller, product method)
            window.location.href = `<?= $this->Url->build(['controller' => 'Home', 'action' => 'product']) ?>/${productId}`;
        }
    });

    // Check review status on page load
    $(document).ready(function() {
        checkUserReviewStatus();
    });

    function checkUserReviewStatus() {
        const isLoggedIn = <?= json_encode($this->request->getSession()->check('Auth.User')) ?>;

        if (!isLoggedIn) {
            return; // Don't check if user is not logged in
        }

        const productId = <?= json_encode($productId) ?>;
        const customerId = <?= json_encode($customerId) ?>;

        if (!customerId) {
            return;
        }

        $.ajax({
            url: '<?= $this->Url->build(['controller' => 'Home', 'action' => 'checkProductReview']) ?>',
            method: 'POST',
            data: {
                product_id: productId,
                customer_id: customerId,
                _csrfToken: $('meta[name="csrfToken"]').attr('content')
            },
            success: function(response) {
                if (response.hasReview) {
                    // Show reviewed status
                    $('#reviewStatus').show();
                    $('#reviewProductSection h3').text('<?= __('You have reviewed this product') ?>');
                    $('#reviewProductSection p').text('<?= __('Thank you for your feedback!') ?>');
                }
            },
            error: function() {
                // Silently fail - don't show error for status check
                console.log('Could not check review status');
            }
        });
    }

    // Product Review Modal Functionality
    function openProductReviewModal() {
        // Check if user is logged in
        const isLoggedIn = <?= json_encode($this->request->getSession()->check('Auth.User')) ?>;
        console.log(isLoggedIn);

        if (!isLoggedIn) {
            // Show login modal from website.php
            if (typeof showLoginModal === 'function') {
                showLoginModal();
            } else {
                // Fallback if login modal function is not available
                alert('<?= __('Please login to review this product') ?>');
                window.location.href = '<?= $this->Url->build(['controller' => 'Users', 'action' => 'login']) ?>';
            }
            return;
        }

        // Check if user has already reviewed this product
        const productId = <?= json_encode($productData['id']) ?>;
        console.log(productId);
        const customerId = <?= json_encode($customerId) ?>;
                               
        if (!customerId) {
            alert('<?= __('Customer information not found. Please login again.') ?>');
            return;
        }

        // Check for existing review via AJAX
        $.ajax({
            url: '<?= $this->Url->build(['controller' => 'Home', 'action' => 'checkProductReview']) ?>',
            method: 'POST',
            data: {
                product_id: productId,
                customer_id: customerId,
                _csrfToken: $('meta[name="csrfToken"]').attr('content')
            },
            success: function(response) {
                if (response.hasReview) {
                    Swal.fire({
                        icon: 'info',
                        title: '<?= __('Already Reviewed') ?>',
                        text: '<?= __('You have already reviewed this product.') ?>',
                        confirmButtonText: '<?= __('OK') ?>'
                    });
                } else {
                    // Open the review modal
                    openReviewModal(null, null, productId, '<?= h($productData['name']) ?>');
                }
            },
            error: function() {
                Swal.fire({
                    icon: 'error',
                    title: '<?= __('Error') ?>',
                    text: '<?= __('Unable to check review status. Please try again.') ?>',
                    confirmButtonText: '<?= __('OK') ?>'
                });
            }
        });
    }

    // Override the openReviewModal function for product reviews
    function openReviewModal(orderId, orderItemId, productId, productName) {
        console.log('Opening product review modal for:', {orderId, orderItemId, productId, productName});

        document.getElementById('singleOrderId').value = orderId || '';
        document.getElementById('singleOrderItemId').value = orderItemId || '';
        document.getElementById('singleProductId').value = productId;
        document.getElementById('productName').textContent = productName;
        document.getElementById('singleSelectedRating').value = '0';
        document.getElementById('singleReviewText').value = '';

        // Reset stars
        const stars = document.querySelectorAll('#singleStarRating .star');
        stars.forEach(star => {
            star.querySelector('i').className = 'far fa-star';
        });

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('reviewModal'));
        modal.show();
    }
</script>

<!-- Include Single Review Modal Component -->
<?= $this->element('single_review_modal') ?>

<?php $this->end(); ?>
