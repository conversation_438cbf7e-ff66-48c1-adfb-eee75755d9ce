<?php

/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Order $order
 */
?>
<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/select2/dist/css/select2.min.css') ?>">
<style>
    .details {
        display: none;
        /* Hide details initially */
    }

    .qr-code-section {
        display: none;
        margin-top: 10px;
    }

    .label {
        width: max-content !important;
    }

    .bg-green-light {
        background-color: rgb(173 253 173 / 61%);
        /* A light green color */
    }

    .fist-table-header {
        border-top-left-radius: 15px;
        border-bottom-left-radius: 15px;
    }

    .end-table-header {
        border-top-right-radius: 15px;
        border-bottom-right-radius: 15px;
    }

    .table:not(.table-sm) thead th {
        background-color: rgba(246, 184, 49, 0.614) !important;
        color: black !important;
    }

    .card,
    .card-body {
        border-radius: 10px !important;
    }

    input {
        padding: 0px !important;
    }

    .small {
        font-size: 10px !important;
        font-weight: 200 !important;
    }

    .payment-info-card {
        margin-top: -180px !important;
    }

    .bg-light-purple {
        background-color: #8ce28c;
        /* A lighter purple */
    }

    .card {
        background-color: #fff !important;
        box-shadow: 0 .46875rem 2.1875rem rgba(90, 97, 105, .1), 0 .9375rem 1.40625rem rgba(90, 97, 105, .1), 0 .25rem .53125rem rgba(90, 97, 105, .12), 0 .125rem .1875rem rgba(90, 97, 105, .1) !important;
    }

    .main-wrapper-1 .section .section-header {
        height: max-content;
    }

    @media (max-width: 500px) {
        .payment-info-card {
            margin-top: 0px;
        }
    }

    .widthStyle {
        width: 100% !important
    }

    .is-invalid-select {
        border-color: #dc3545 !important;
        padding-right: calc(1.5em + .75rem);
        background-image: url('data:image/svg+xml,%3csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" width="12" height="12" fill="none" stroke="%23dc3545"%3e%3ccircle cx="6" cy="6" r="4.5" /%3e%3cpath stroke-linejoin="round" d="M5.8 3.6h.4L6 6.5z" /%3e%3ccircle cx="6" cy="8.2" r=".6" fill="%23dc3545" stroke="none" /%3e%3c/svg%3e');
        background-repeat: no-repeat;
        background-position: right calc(.375em + .1875rem) center;
        background-size: calc(.75em + .375rem) calc(.75em + .375rem);
    }

    .is-invalid-ckeditor {
        border-color: #dc3545 !important;
        padding-right: calc(1.5em + .75rem);
        background-image: url('data:image/svg+xml,%3csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" width="12" height="12" fill="none" stroke="%23dc3545"%3e%3ccircle cx="6" cy="6" r="4.5" /%3e%3cpath stroke-linejoin="round" d="M5.8 3.6h.4L6 6.5z" /%3e%3ccircle cx="6" cy="8.2" r=".6" fill="%23dc3545" stroke="none" /%3e%3c/svg%3e');
        background-repeat: no-repeat;
        background-position: right calc(.375em + .1875rem) center;
        background-size: calc(.75em + .375rem) calc(.75em + .375rem);
    }

    /* Order Status Button Styles */
    .order-status-btn {
        font-weight: 600;
        border-radius: 8px;
        padding: 10px 18px;
        font-size: 14px;
        transition: all 0.3s ease;
        border-width: 2px;
        min-width: 100px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .order-status-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    .order-status-btn:disabled {
        opacity: 0.7;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    /* Active Status Button Styles with Strong Background Colors */
    .order-status-btn.btn-warning {
        background-color: #ffc107 !important;
        border-color: #ffc107 !important;
        color: #000 !important;
        font-weight: 700;
        box-shadow: 0 3px 10px rgba(255, 193, 7, 0.4);
    }

    .order-status-btn.btn-primary {
        background-color: #007bff !important;
        border-color: #007bff !important;
        color: #fff !important;
        font-weight: 700;
        box-shadow: 0 3px 10px rgba(0, 123, 255, 0.4);
    }

    .order-status-btn.btn-info {
        background-color: #17a2b8 !important;
        border-color: #17a2b8 !important;
        color: #fff !important;
        font-weight: 700;
        box-shadow: 0 3px 10px rgba(23, 162, 184, 0.4);
    }

    .order-status-btn.btn-secondary {
        background-color: #6c757d !important;
        border-color: #6c757d !important;
        color: #fff !important;
        font-weight: 700;
        box-shadow: 0 3px 10px rgba(108, 117, 125, 0.4);
    }

    .order-status-btn.btn-dark {
        background-color: #343a40 !important;
        border-color: #343a40 !important;
        color: #fff !important;
        font-weight: 700;
        box-shadow: 0 3px 10px rgba(52, 58, 64, 0.4);
    }

    .order-status-btn.btn-success {
        background-color: #28a745 !important;
        border-color: #28a745 !important;
        color: #fff !important;
        font-weight: 700;
        box-shadow: 0 3px 10px rgba(40, 167, 69, 0.4);
    }

    .order-status-btn.btn-danger {
        background-color: #dc3545 !important;
        border-color: #dc3545 !important;
        color: #fff !important;
        font-weight: 700;
        box-shadow: 0 3px 10px rgba(220, 53, 69, 0.4);
    }

    /* Inactive Status Button Styles with Subtle Background */
    .order-status-btn.btn-outline-warning {
        background-color: #fff8e1 !important;
        border-color: #ffc107 !important;
        color: #856404 !important;
        font-weight: 500;
    }

    .order-status-btn.btn-outline-primary {
        background-color: #e3f2fd !important;
        border-color: #007bff !important;
        color: #004085 !important;
        font-weight: 500;
    }

    .order-status-btn.btn-outline-info {
        background-color: #e1f5fe !important;
        border-color: #17a2b8 !important;
        color: #0c5460 !important;
        font-weight: 500;
    }

    .order-status-btn.btn-outline-secondary {
        background-color: #f8f9fa !important;
        border-color: #6c757d !important;
        color: #495057 !important;
        font-weight: 500;
    }

    .order-status-btn.btn-outline-dark {
        background-color: #f8f9fa !important;
        border-color: #343a40 !important;
        color: #212529 !important;
        font-weight: 500;
    }

    .order-status-btn.btn-outline-success {
        background-color: #e8f5e8 !important;
        border-color: #28a745 !important;
        color: #155724 !important;
        font-weight: 500;
    }

    .order-status-btn.btn-outline-danger {
        background-color: #fdeaea !important;
        border-color: #dc3545 !important;
        color: #721c24 !important;
        font-weight: 500;
    }

    /* Hover effects for inactive buttons */
    .order-status-btn.btn-outline-warning:hover {
        background-color: #fff3cd !important;
        border-color: #ffc107 !important;
        color: #856404 !important;
    }

    .order-status-btn.btn-outline-primary:hover {
        background-color: #cce7ff !important;
        border-color: #007bff !important;
        color: #004085 !important;
    }

    .order-status-btn.btn-outline-info:hover {
        background-color: #bee5eb !important;
        border-color: #17a2b8 !important;
        color: #0c5460 !important;
    }

    .order-status-btn.btn-outline-secondary:hover {
        background-color: #e2e6ea !important;
        border-color: #6c757d !important;
        color: #495057 !important;
    }

    .order-status-btn.btn-outline-dark:hover {
        background-color: #e2e6ea !important;
        border-color: #343a40 !important;
        color: #212529 !important;
    }

    .order-status-btn.btn-outline-success:hover {
        background-color: #d4edda !important;
        border-color: #28a745 !important;
        color: #155724 !important;
    }

    .order-status-btn.btn-outline-danger:hover {
        background-color: #f5c6cb !important;
        border-color: #dc3545 !important;
        color: #721c24 !important;
    }

    /* Default grey style for inactive buttons */
    .order-status-btn.btn-outline-secondary {
        background-color: #f8f9fa !important;
        border-color: #6c757d !important;
        color: #495057 !important;
        font-weight: 500;
    }

    .order-status-btn.btn-outline-secondary:hover:not(:disabled) {
        background-color: #e2e6ea !important;
        border-color: #6c757d !important;
        color: #495057 !important;
    }

    /* Disabled button styles */
    .order-status-btn:disabled {
        opacity: 0.5 !important;
        cursor: not-allowed !important;
        transform: none !important;
        box-shadow: none !important;
    }
</style>
<?php $this->end(); ?>


<section class="section">
    <div class="section-header d-flex justify-content-between align-items-center mb-3">
        <ul class="breadcrumb breadcrumb-style mb-0">
            <li class="breadcrumb-item">
                <a href="<?= $this->Url->build(['controller' => 'Dashboard', 'action' => 'index']) ?>">
                    <h4 class="page-title m-b-0">Dashboard</h4>
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="<?= $this->Url->build(['controller' => 'Orders', 'action' => 'index']) ?>">
                    Orders
                </a>
            </li>
            <li class="breadcrumb-item active">
                View
            </li>
        </ul>
        <a href="javascript:void(0);" class="d-flex align-items-center" id="back-button-mo" onclick="history.back();">
            <span class="rotate me-2">➥</span><small style="font-weight: bold"><?= __('BACK') ?></small>
        </a>
        <!-- <button class="d-flex align-items-center" id="back-button-mo">
                        <span class="rotate me-2">➥</span><small style="font-weight: bold">BACK</small>
                    </button> -->
    </div>

    <div
        class="section-header d-flex justify-content-between align-items-center mb-3 mt-3">
        <ul class="breadcrumb breadcrumb-style mb-0">
            <li class="breadcrumb-item">
                <h4 class="page-title m-b-0">Order Details</h4>
            </li>
    </div>

    <div
        class="section-header d-flex justify-content-between align-items-center mt-3">
        <ul class="breadcrumb breadcrumb-style mb-0">

            <li class="form-group mb-0 p-10 m-r-10" style="padding-left: 0px;">
                <img src="../../img/cart.png" />
            </li>

            <li class="form-group mb-0 m-r-15">
                <label style="color: #F77F00; width: 100px;" for="order-date">Order ID</label><br />
                <strong><?= h($order->order_number) ?></strong>
            </li>

            <li class="form-group mb-0">
                <label style="color: #F77F00; width: 110px;" for="order-amount">Order Placed</label><br />
                <strong><?= h($order->order_date->i18nFormat('dd MMM yyyy')) ?></strong>
            </li>
    
            <li class="form-group mb-0">
                <label style="color: #F77F00; width: 110px;" for="total-amount">Order Value</label><br />
                <!-- <strong><?= h($order->total_amount) ?> <?= $currencyCode ?></strong> -->
                 <strong><?= $this->Price->formatOrderAmount($order->total_amount,$order->country_id ) ?></strong> 
            </li>

            <li class="form-group mb-0">
                <label style="color: #F77F00; width: 100px;" for="order-status">No of items</label><br />
                <strong><?= h($orderItemCount) ?></strong>
            </li>

            <li class="form-group mb-0">
                <label style="color: #F77F00; width: 110px;" for="payment-status">Delivery Address</label><br />
                <strong>
                    <?php if (!empty($order->customer_address_id)): ?>
                        <p style="max-width:180px">
                            <?= h($order->customer_address->house_no) ?>,
                            <?= h($order->customer_address->address_line1) ?>,
                            <?= h($order->customer_address->address_line2) ?>,
                            <?= !empty($order->customer_address->city_id) ? h($order->customer_address->city->city_name) : '' ?>
                            <?= !empty($order->customer_address->municipality_id) ? h($order->customer_address->municipality->name) : '' ?> -
                            <?= h($order->customer_address->zipcode) ?>
                        </p>
                    <?php else: ?>
                        <p>No address available</p>
                    <?php endif; ?>

                </strong>
            </li>

            <li class="form-group mb-0" style="margin: 10px; margin-left: 15px;">
                <?php
                $status = $order->status;
                $statusLabel = isset($orderStatusMap[$status]) ? $orderStatusMap[$status]['label'] : 'Unknown';
                $statusClass = isset($orderStatusMap[$status]) ? $orderStatusMap[$status]['class'] : 'col-purple';
                ?>

                <?php if ($status === 'Pending Cancellation') { ?>
                    <a href="#" onclick="approveCancellation(this, <?= h($order->id) ?>);" class="badge <?= h($statusClass) ?> badge-outline fw-bold" style="padding: 12px;">
                        <?= h($statusLabel) ?>
                    </a>
                <?php } else if ($status === 'Pending Return') { ?>
                    <a href="#" onclick="approveReturn(this, <?= h($order->id) ?>);" class="badge <?= h($statusClass) ?> badge-outline fw-bold" style="padding: 12px;">
                        <?= h($statusLabel) ?>
                    </a>
                <?php } else { ?>
                    <strong class="badge <?= h($statusClass) ?> badge-outline fw-bold" style="padding: 12px;">
                        <?= h($statusLabel) ?>
                    </strong>
                <?php } ?>
            </li>

            <li class="form-group mb-0" style="margin: 10px;">
                <a href="<?= $this->Url->build(['controller' => 'Orders', 'action' => 'donwloadInvoice', $order->id]) ?>" target="_blank" title="Download Invoice">
                    <strong class="badge bg-light-purple text-dark badge-outline bg-success-subtle  text-light fw-bold">
                        <img src="../../img/document-download.png" />
                            Download Invoice
                    </strong>
                </a>
            </li>
            
    </div>
    <div class="section-body" id="view-order-section-body">
        <div class="row">
            <div class="col-12 col-md-6 col-lg-12">
                <div class="card p-0 m-t-20">
                    <?php if (!($order->status === 'Pending Return' || $order->status === 'Return Approved' || $order->status === 'Return Rejected' || $order->status === 'Returned')) { ?>
                    <div class="card-header">
                        <h4 style="color: black !important;">Progress</h4>
                    </div>
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="d-flex align-items-center gap-2">
                                <!-- Order Status Update Buttons -->
                                <button type="button"
                                        class="btn btn-sm order-status-btn <?= $order->status === 'Pending' ? 'btn-warning' : 'btn-outline-secondary' ?>"
                                        data-status="Pending"
                                        <?= ($order->status === 'Delivered' || $order->status === 'Rejected') ? 'disabled' : '' ?>
                                        onclick="updateOrderStatus('<?= $order->id ?>', 'Pending', this)">
                                    Pending
                                </button>
                                <button type="button"
                                        class="btn btn-sm order-status-btn <?= $order->status === 'Confirmed' ? 'btn-primary' : 'btn-outline-secondary' ?>"
                                        data-status="Confirmed"
                                        <?= ($order->status === 'Delivered' || $order->status === 'Rejected') ? 'disabled' : '' ?>
                                        onclick="updateOrderStatus('<?= $order->id ?>', 'Confirmed', this)">
                                    Confirmed
                                </button>
                                <button type="button"
                                        class="btn btn-sm order-status-btn <?= $order->status === 'Processing' ? 'btn-info' : 'btn-outline-secondary' ?>"
                                        data-status="Processing"
                                        <?= ($order->status === 'Delivered' || $order->status === 'Rejected') ? 'disabled' : '' ?>
                                        onclick="updateOrderStatus('<?= $order->id ?>', 'Processing', this)">
                                    Processing
                                </button>
                                <button type="button"
                                        class="btn btn-sm order-status-btn <?= $order->status === 'Shipped' ? 'btn-secondary' : 'btn-outline-secondary' ?>"
                                        data-status="Shipped"
                                        <?= ($order->status === 'Delivered' || $order->status === 'Rejected') ? 'disabled' : '' ?>
                                        onclick="updateOrderStatus('<?= $order->id ?>', 'Shipped', this)">
                                    Shipped
                                </button>
                                <button type="button"
                                        class="btn btn-sm order-status-btn <?= $order->status === 'Out for Delivery' ? 'btn-dark' : 'btn-outline-secondary' ?>"
                                        data-status="Out for Delivery"
                                        <?= ($order->status === 'Delivered' || $order->status === 'Rejected') ? 'disabled' : '' ?>
                                        onclick="updateOrderStatus('<?= $order->id ?>', 'Out for Delivery', this)">
                                    Out for Delivery

                                </button>
                                <button type="button"
                                        class="btn btn-sm order-status-btn <?= $order->status === 'Delivered' ? 'btn-success' : 'btn-outline-secondary' ?>"
                                        data-status="Delivered"
                                        <?= ($order->status === 'Delivered' || $order->status === 'Rejected') ? 'disabled' : '' ?>
                                        onclick="updateOrderStatus('<?= $order->id ?>', 'Delivered', this)">
                                    Delivered
                                </button>
                                <button type="button"
                                        class="btn btn-sm order-status-btn <?= $order->status === 'Rejected' ? 'btn-danger' : 'btn-outline-secondary' ?>"
                                        data-status="Rejected"
                                        <?= $order->status === 'Delivered' ? 'disabled' : '' ?>
                                        onclick="updateOrderStatus('<?= $order->id ?>', 'Rejected', this)">
                                    Rejected
                                </button>
                            </div>
                        </div>
                    </div>
                    <?php } ?>

                    <?php if ($order->status === 'Pending Return' || $order->status === 'Return Approved' || $order->status === 'Return Rejected' || $order->status === 'Returned') { ?>
                        <div class="card-header">
                            <h4 style="color: black !important;">Return Progress</h4>
                        </div>
                        <div class="card-body">
                            <button type="button" class="btn btn-sm order-status-btn <?= $order->status === 'Pending Return' ? 'btn-warning' : 'btn-outline-warning' ?>"
                                data-status="Pending Return">
                                Pending Return
                            </button>

                            <button type="button" class="btn btn-sm order-status-btn <?= $order->status === 'Return Approved' ? 'btn-primary' : 'btn-outline-primary' ?>"
                                data-status="Return Approved">
                                Return Approved
                            </button>

                            <button type="button" class="btn btn-sm order-status-btn <?= $order->status === 'Return Rejected' ? 'btn-danger' : 'btn-outline-danger' ?>"
                                data-status="Return Rejected">
                                Return Rejected
                            </button>

                            <button type="button" class="btn btn-sm order-status-btn <?= $order->status === 'Returned' ? 'btn-success' : 'btn-outline-success' ?>"
                                data-status="Returned">
                                Returned
                            </button>
                        </div>
                    <?php } ?>

                    <?php if ($order->status === 'Pending Cancellation' || $order->status === 'Cancellation Rejected' || $order->status === 'Cancelled') { ?>
                        <div class="card-body">
                            <button type="button" class="btn btn-sm order-status-btn <?= $order->status === 'Pending Cancellation' ? 'btn-warning' : 'btn-outline-warning' ?>"
                                data-status="Pending Cancellation">
                                Pending Cancellation
                            </button>

                            <button type="button" class="btn btn-sm order-status-btn <?= $order->status === 'Cancellation Rejected' ? 'btn-danger' : 'btn-outline-danger' ?>"
                                data-status="Cancellation Rejected">
                                Cancellation Rejected
                            </button>

                            <button type="button" class="btn btn-sm order-status-btn <?= $order->status === 'Cancelled' ? 'btn-secondary' : 'btn-outline-secondary' ?>"
                                data-status="Cancelled">
                                Cancelled
                            </button>
                        </div>
                    <?php } ?>

                    <?php if ($order->delivery_mode === 'delivery') { ?>
                        <div class="card-header d-flex justify-content-between">
                            <div class="text-success">
                                <img src="../../img/truck.png" class="p-r-10" />
                                Estimated <?= $order->delivery_mode_type == 'express' ? 'Express' : 'Standard' ?> Delivery:
                                <?= $order->delivery_date ? h($order->delivery_date->i18nFormat('dd MMM yyyy')) : '' ?>
                            </div>

                            <div class="card-header-form align-items-center">
                                <form class="d-flex align-items-center">
                                    <div class="">

                                    </div>
                                </form>
                            </div>
                        </div>
                    <?php } ?>
                </div>
            </div>
            
            <div class="col-12 col-md-6 col-lg-8">
                <div class="card p-0 pb-20">
                    <div class="card-header">
                        <h4 style="color: black !important;"><img src="../../img/product-list.png" style="width: 25px;" class="m-r-20" />Product Information</h4>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive" style="height: 300px;
                                    overflow: scroll;">
                            <div id="table-1_wrapper" class="dataTables_wrapper container-fluid dt-bootstrap4 no-footer">
                                <table class="table table-striped" id="table-1">
                                    <thead>
                                        <tr>
                                            <th><?= __("Product Image") ?></th>
                                            <th><?= __("Product Name & Size") ?></th>
                                            <th><?= __("Price") ?></th>
                                            <th><?= __("Quantity") ?></th>
                                            <!-- <th><?= __("Status") ?></th> -->
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($order->order_items as $item): ?>
                                            <tr>
                                                <td class="mb-0 font-13 table-img">
                                                    <?php
                                                    if ($item->product_variant_id && !empty($item->product_variant->product_variant_images)) {
                                                        $image = $item->product_variant->product_variant_images[0]->image;
                                                    } elseif (!empty($item->product->product_images)) {
                                                        $image = $item->product->product_images[0]->image;
                                                    } else {
                                                        $image = $this->Url->webroot('img/user.png');
                                                    }
                                                    ?>
                                                    <img src="<?= h($image) ?>" alt="Product Image" style="width: 25px; height: auto;" />
                                                </td>
                                                <td class="mb-0 font-13">
                                                    <div>
                                                        <?= h($item->product_variant_id ? $item->product_variant->variant_name : $item->product->name) ?><br>
                                                        <small><?= h($item->product_variant_id ? $item->product_variant->variant_size : $item->product->product_size) ?></small>
                                                    </div>
                                                </td>
                                                <!-- <td class="mb-0 font-13"><?= h($item->price) ?>&nbsp;<?= $currencyCode ?></td> -->
                                                   <td class="mb-0 font-13">
                                                    <!-- <?= h($item->price) ?>&nbsp;<?= $currencyCode ?> -->
                                                     <?= $this->Price->formatOrderAmount($item->price,$order->country_id ) ?>
                                                </td>

                                                <td class="mb-0 font-13"><?= h($item->quantity) ?> Nos</td>
                                                <!-- <td>
                                                    <?php
                                                    $statusval = $orderStatusMap[$item->status] ?? ['label' => 'Unknown', 'class' => 'col-red'];
                                                    ?>
                                                    <div class="badge-outline <?= h($statusval['class']) ?>">
                                                        <?= h($statusval['label']) ?>
                                                    </div>
                                                </td> -->
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- <div class="card p-0 m-t-5">
                    <div class="card-header">
                        <h4 style="color: black !important;"><img src="../../img/shipping.png" style="width: 25px;" class="m-r-20" />Shipping Details</h4>
                    </div>
                    <div class="card-body">
                        <div>
                            <div style="float: left;" class="m-b-10">
                                <div>
                                    <p class="fw-bold m-b-0" style="color: black;">Pickup Type</p>
                                </div>
                                <?php if ($order->delivery_mode === 'delivery'): ?>
                                    <h6 class="mb-1">Deliver to Address</h6>
                                <?php elseif ($order->delivery_mode === 'pickup'): ?>
                                    <h6 class="mb-1">Pickup from Showroom</h6>
                                <?php endif; ?>

                            </div>


                            <div style="float: right; margin-right: 150px;">
                                <div>
                                    <?php if ($order->delivery_mode === 'delivery'): ?>
                                        <p class="fw-bold m-b-0" style="color: black !important;">Shipping Address</p>
                                    <?php elseif ($order->delivery_mode === 'pickup'): ?>
                                        <p class="fw-bold m-b-0" style="color: black !important;">Showroom Address</p>
                                    <?php endif; ?>

                                </div>

                                <?php if ($order->delivery_mode === 'delivery'): ?>
                                    <?php if (!empty($order->customer_address_id)): ?>
                                        <h6>
                                            <?= h($order->customer_address->house_no) ?>,
                                            <?= h($order->customer_address->address_line1) ?>,
                                            <?= h($order->customer_address->address_line2) ?>,
                                            <?= !empty($order->customer_address->city) ? h($order->customer_address->city->city_name) : '' ?>
                                            <?= !empty($order->customer_address->municipality) ? h($order->customer_address->municipality->name) : '' ?> -
                                            <?= h($order->customer_address->zipcode) ?>
                                        </h6>
                                    <?php else: ?>
                                        <p>No address available</p>
                                    <?php endif; ?>
                                <?php elseif ($order->delivery_mode === 'pickup'): ?>
                                    <?php if (!empty($order->showroom_id)): ?>
                                        <h6>
                                            <?= h($order->showroom->name) ?>,
                                            <?= h($order->showroom->address) ?>,
                                            <?= !empty($order->showroom->city_id) ? h($order->showroom->city->name) : '' ?>
                                        </h6>
                                    <?php else: ?>
                                        <p>No address available</p>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>

                        </div>

                    </div>
                </div> -->
                <div class="card p-0" id="payment-info-card">
                    <div class="card-header">
                        <h4 style="color: black !important;"><img src="../../img/bank.png" style="width: 25px;" class="m-r-20" />Payment Information</h4>
                    </div>
                    <!-- <div class="card-body" style="border: 2px solid #f780006b; margin: 15px;margin-top: 0px;">

                        <div style="float: left;">

                            <div class="form-group mb-0">
                                <label for="payment-type">Payment Method</label>
                                <div>
                                    <div class="d-flex">
                                        <p class="fw-bold m-b-0" style="color: black;"><?= h($order->transactions[0]->payment_method) ?></p>
                                    </div>
                                    <div class="d-flex">
                                        <p class="fw-bold m-b-0" style="color: black;">Transaction Date : </p>
                                       <p class="fw-bold m-b-0" style="color: black;">
                                        <?= !empty($order->transactions[0]->transaction_date) ? h($order->transactions[0]->transaction_date->i18nFormat('dd MMM yyyy')) : 'Date not available' ?>
                                    </p>

                                    </div>
                                    <div class="form-group mb-0">
                                        <label for="order-date" style="width: max-content !important;">Transaction ID : <?= h($order->transactions[0]->transaction_number) ?></label>
                                        <div>
                                            <div class="form-group mb-0">
                                                <label for="order-date" style="width: max-content !important;">Customer Name : <?= h($order->customer->user->first_name . ' ' . $order->customer->user->last_name) ?>
                                                </label>
                                                <div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>

                        <div style="float: right;">
                            <?php
                            $status = $order->transactions[0]->payment_status;
                            $color = $paymentStatusProgress[$status] ?? 'l-bg-orange';
                            $progress = $paymentStatusProgressBar[$status] ?? '0%';
                            ?>
                            <p class="mb-0 text-sm">
                                <span class="text-nowrap <?= h($color) ?> text-white px-2 rounded" style="font-size: small" ;><?= h($status) ?></span>
                            </p>

                        </div>


                    </div> -->
                    <div class="card-body" style="border: 2px solid #f780006b; margin: 15px;margin-top: 0px;">
                    <div style="float: left;">
                        <div class="form-group mb-0">
                            <label for="payment-type">Payment Method</label>
                            <div>
                                <div class="d-flex">
                                    <p class="fw-bold m-b-0" style="color: black;">
                                        <?= !empty($order->transactions[0]->payment_method) ? h($order->transactions[0]->payment_method) : $order->payment_method ?>
                                    </p>
                                </div>

                                <div class="d-flex">
                                    <p class="fw-bold m-b-0" style="color: black;">Transaction Date : </p>
                                    <p class="fw-bold m-b-0" style="color: black;">
                                        <?= !empty($order->transactions[0]->transaction_date) ? h($order->transactions[0]->transaction_date->i18nFormat('dd MMM yyyy')) : 'Date not available' ?>
                                    </p>
                                </div>

                                <div class="form-group mb-0">
                                    <label for="order-date" style="width: max-content !important;">
                                        Transaction ID :
                                        <?= !empty($order->transactions[0]->transaction_number) ? h($order->transactions[0]->transaction_number) : 'Not available' ?>
                                    </label>

                                    <div class="form-group mb-0">
                                        <label for="order-date" style="width: max-content !important;">
                                            Customer Name :
                                            <?= !empty($order->customer->user) ? h($order->customer->user->first_name . ' ' . $order->customer->user->last_name) : 'Not available' ?>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div style="float: right;">
                        <?php
                        $status = !empty($order->transactions[0]->payment_status) ? $order->transactions[0]->payment_status : $order->status;
                        $color = $paymentStatusProgress[$status] ?? 'l-bg-orange';
                        $progress = $paymentStatusProgressBar[$status] ?? '0%';
                        ?>
                        <p class="mb-0 text-sm">
                            <span id="payment-status-badge" class="text-nowrap <?= h($color) ?> text-white px-2 rounded" style="font-size: small;">
                                <?= h(ucfirst($status)) ?>
                            </span>
                        </p>
                    </div>
                </div>

                </div>
            </div>

            <div class="col-12 col-md-6 col-lg-4">
                <div class="card p-0">
                    <div class="card-header">
                        <h4 style="color: black !important;"><img src="../../img/profile-circle.png" style="width: 25px;" class="m-r-20" />Customer Details</h4>
                    </div>
                    <div class="card-body">
                        <div class="d-flex align-items-center gap-2 p-b-20" style="border-bottom: 2px solid #f780006b;">
                            <img src="../../img/profile-circle.png" alt="" class="avatar rounded-3 border border-light border-3">
                            <div>
                                <h6 class="mb-1"><?= h($order->customer->user->first_name . ' ' . $order->customer->user->last_name) ?> </h6>
                                <a href="#!" style="color: #F77F00;" class="link-primary fw-medium"><?= h($order->customer->user->email) ?>
                                </a>
                            </div>
                        </div>

                        <div class="">
                            <div class="m-b-10">
                                <div style="float: left;">
                                    <div class="m-t-10">
                                        <p class="fw-bold m-b-0" style="color: black;">Contact Number</p>
                                    </div>
                                    <p style="font-size: 12px;" class="mb-1"><?= h('(' . $order->customer->user->country_code . ') ' . $order->customer->user->mobile_no) ?>
                                    </p>
                                </div>

                                <div style="float: right;">
                                    <div class="m-t-10">
                                        <p class="fw-bold m-b-0" style="color: black;">Alternate Number</p>
                                    </div>
                                    <p style="font-size: 12px;" class="mb-1"></p>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
                <div class="card m-t-5 p-0">
                    <div class="card-header">
                        <h4 style="color: black !important;"><img src="../../img/order-summary.png" style="width: 25px;" class="m-r-20" />Order Summary</h4>
                    </div>
                    <div class="card-body pt-0 pb-0 ">
                        <div class="d-flex" style="color: black; border-bottom: 1px solid #F77F00;">
                            <p class="fw-bold m-b-0 p-b-10 p-t-10" style="color: black;margin-right: auto;">Price (<?= h($orderItemCount) ?> item(s)) : </p>
                            <p class="fw-bold m-b-0" style="color: black !important;">
                                <!-- <?= h($order->subtotal_amount) ?> <?= $currencyCode ?> -->
                                <?= $this->Price->formatOrderAmount($order->subtotal_amount,$order->country_id ) ?>
                            </p>
                        </div>
                    </div>
                    <div class="card-body p-t-5 pb-0">
                        <div class="d-flex">
                            <p class="fw-bold m-b-0" style="margin-right: auto;">Discount :</p>
                            <p class="fw-bold m-b-0">
                                <!-- <?= h($order->discount_amount) ?> <?= $currencyCode ?> -->
                                <?= $this->Price->formatOrderAmount($order->discount_amount,$order->country_id ) ?>
                        </p>
                        </div>

                        <!-- <div class="d-flex">
                            <div style="margin-right: auto;">
                                <p class="fw-bold m-b-0" ;>
                                    Coupons <small class="small"><?= !empty($order->offer) ? '(' . h($order->offer->offer_code) . ')' : '' ?>
                                    </small>
                                    <a href=""><small class="small" style="color:red; text-decoration: underline;">(Remove)</small></a>
                                </p>
                            </div>
                            <p class="fw-bold m-b-0">
                             
                                 <?= $this->Price->formatOrderAmount($order->offer_amount,$order->country_id ) ?>
                            </p>
                        </div> -->

                        <!-- <div class="d-flex justify-content-between">

                            <p class="fw-bold m-b-0" id="view-order-radeem-loyalty-points">Redeem Loyalty Points</p>
                            <a>
                                <p class="fw-bold m-b-0" id="view-order-radeem-loyalty-point" data-bs-toggle="modal" data-bs-target="#redeemPointsModal">
                                    Redeemed Loyalty Points
                                </p>
                            </a>
                            <p class="fw-bold m-b-0 txt-right">0.00 FCFA</p>
                        </div> -->
                        
                        <?php if (!empty($order->installation_amount && $order->installation_amount > 0)): ?>
                        <div class="d-flex">
                            <p class="fw-bold m-b-0" style="margin-right: auto;">Installation Charge :</p>
                            <p class="fw-bold m-b-0">
                                 <?= $this->Price->formatOrderAmount($order->installation_amount,$order->country_id ) ?>
                            </p>
                        </div>
                        <?php endif; ?>

                        <?php if (!empty($order->tax_amount && $order->tax_amount > 0)): ?>
                        <div class="d-flex">
                            <p class="fw-bold m-b-0" style="margin-right: auto;">Tax Amount :</p>
                            <p class="fw-bold m-b-0">
                                 <?= $this->Price->formatOrderAmount($order->tax_amount,$order->country_id ) ?>
                            </p>
                        </div>
                        <?php endif; ?>
                        <div class="d-flex">
                            <p class="fw-bold m-b-0" style="margin-right: auto;">Delivery Charges :</p>
                            <p class="fw-bold m-b-0">
                                <!-- <?= h($order->delivery_charge) ?> <?= $currencyCode ?> -->
                                 <?= $this->Price->formatOrderAmount($order->delivery_charge,$order->country_id ) ?>
                            </p>
                        </div>

                        <!-- <div class="d-flex">
                            <p class="fw-bold m-b-0" style="margin-right: auto;">Shipping :</p>
                            <p class="fw-bold m-b-0">Free</p>
                        </div> -->


                    </div>
                    <div class="card-body">
                        <div class="d-flex" style="color: black; border-bottom: 1px solid #F77F00; border-top: 1px solid #F77F00;">
                            <p class="fw-bold m-b-0 p-b-10 p-t-10" style="color: black;margin-right: auto;">Total Price (<?= h($orderItemCount) ?> item(s)) : </p>
                            <p class="fw-bold m-b-0  p-t-10" style="color: black !important;">
                                <!-- <?= h($order->total_amount) ?> <?= $currencyCode ?> -->
                                 <?= $this->Price->formatOrderAmount($order->total_amount,$order->country_id ) ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>
    <div class="modal fade" id="approveCancellModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="approveCancellModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="approveCancellModalLabel"><?= __('Approve Cancellation') ?></h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="resetCancellationForm()">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <?= $this->Form->create(null, [
                    'id' => 'approveCancellForm',
                    'novalidate' => true,
                    'type' => 'post',
                    'enctype' => 'multipart/form-data'
                ]); ?>
                <div class="modal-body">
                    <input type="hidden" name="order_id" id="order-id" value="<?= h($order->id) ?>">

                    <div class="form-group">
                        <label for="order_cancellation_category_id"><?= __('Cancellation Category') ?> <sup class="text-danger font-11">*</sup></label>
                        <?= $this->Form->control('order_cancellation_category_id', [
                            'type' => 'select',
                            'options' => $orderCancellationCategories,
                            'id' => 'order_cancellation_category_id',
                            'class' => 'form-control select2 widthStyle',
                            'label' => false,
                            'empty' => __('Select a Cancellation Category'),
                            'required' => true
                        ]); ?>
                    </div>

                    <div class="form-group">
                        <label for="reason"><?= __('Reason') ?> <sup class="text-danger font-11">*</sup></label>
                        <?= $this->Form->control('reason', [
                            'type' => 'textarea',
                            'class' => 'form-control ckeditor-textarea',
                            'id' => 'ckeditor',
                            'label' => false,
                            'required' => true
                        ]); ?>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="resetCancellationForm()"><?= __('Close') ?></button>
                    <button type="submit" class="btn btn-primary" id="btnSaveOrderCancellation"><?= __('Save') ?></button>
                </div>
                <?= $this->Form->end(); ?>
            </div>
        </div>
    </div>

    <?php $this->append('script'); ?>
    <script src="<?= $this->Url->webroot('bundles/datatables/datatables.min.js') ?>"></script>
    <script
        src="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/js/dataTables.bootstrap4.min.js') ?>"></script>
    <script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
    <script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js'); ?>"></script>
    <script src="<?= $this->Url->webroot('bundles/select2/dist/js/select2.full.min.js') ?>"></script>
    <script src="<?= $this->Url->webroot('js/delete.js'); ?>"></script>
    <script src="<?= $this->Url->webroot('js/filter.js') ?>"></script>
    <script src="<?= $this->Url->webroot('bundles/ckeditor/ckeditor.js') ?>"></script>
    <script>
        $(document).ready(function() {
            $('.select2').select2({
                minimumResultsForSearch: 0
            });

            $('#approveCancellModal').on('shown.bs.modal', function() {
                $('#order_cancellation_category_id').select2({
                    dropdownParent: $('#approveCancellModal')
                });
            });
            $(function() {
                CKEDITOR.replace("ckeditor");
                CKEDITOR.config.height = 300;

                for (instance in CKEDITOR.instances) {
                    CKEDITOR.instances[instance].on('blur', function() {
                        CKEDITOR.instances[this.name].updateElement();
                    });
                }
            });

            $('#btnSaveOrderCancellation').on('click', function(e) {
                event.preventDefault();
                let isValid = true;

                $('#approveCancellForm').find('input[required], select[required]:visible, #ckeditor').each(function() {
                    let isSelect2 = $(this).hasClass('select2');
                    let isckEditor = $(this).hasClass('ckeditor-textarea');

                    if (isckEditor) {
                        value = CKEDITOR.instances[$(this).attr('id')].getData().trim();
                    } else if (isSelect2) {
                        if (isSelect2) {
                            value = $(this).val();
                        } else {
                            value = $(this).val() ? $(this).val().trim() : '';
                        }
                    } else {
                        value = $(this).val().trim();
                    }

                    if ((value.length === 0) && value === '') {
                        $(this).addClass('is-invalid');
                        let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                        let fieldName = $(this).closest('.form-group').find('label').text().trim().replace(/\*$/, '');
                        feedback.text('<?= __("Please enter ") ?>' + fieldName.toLowerCase() + '.').show();
                        isValid = false;
                        if (isSelect2) {
                            $(this).closest('.form-group').find('.select2-selection--single').addClass('is-invalid-select');
                            $(this).closest('.form-group').find('.select2-selection--multiple').addClass('is-invalid-select');
                        }
                        if (isckEditor) {
                            $(this).closest('.form-group').find('.cke').addClass('is-invalid-ckeditor');
                        }
                    } else {
                        $(this).removeClass('is-invalid');
                        let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                        feedback.hide();
                        if (isSelect2) {
                            $(this).closest('.form-group').find('.select2-selection--single').removeClass('is-invalid-select');
                            $(this).closest('.form-group').find('.select2-selection--multiple').removeClass('is-invalid-select');
                        }
                        if (isckEditor) {
                            $(this).closest('.form-group').find('.cke').removeClass('is-invalid-ckeditor');
                        }
                    }
                });
                if (isValid) {
                    var orderId = $('#order-id').val();
                    for (instance in CKEDITOR.instances) {
                        CKEDITOR.instances[instance].updateElement();
                    }
                    var order_cancellation_category_id = $('#order_cancellation_category_id').val();
                    var reason = $('#ckeditor').val();
                    const swalApproveConfirmation = "<?= __("Are you sure you want to approve this cancellation request?") ?>";
                    const swalApproveWarning = "<?= __("Once approved, the order will be permanently cancelled.") ?>";

                    swal({
                        title: swalApproveConfirmation,
                        text: swalApproveWarning,
                        icon: "warning",
                        buttons: true,
                        dangerMode: true,
                    }).then((willApprove) => {
                        if (willApprove) {
                            $.ajax({
                                url: '<?= $this->Url->build(['controller' => 'Orders', 'action' => 'approveCancellation']) ?>',
                                type: 'POST',
                                data: {
                                    id: orderId,
                                    reason: reason,
                                    order_cancellation_category_id: order_cancellation_category_id
                                },
                                headers: {
                                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                                },
                                success: function(response) {
                                    if (response.status === 'success') {
                                        swal("Cancelled!", response.message, "success").then(function() {
                                            location.reload();
                                        });
                                    } else {
                                        swal("Error!", response.message || "There was an error cancelling the order.", "error");
                                    }
                                },
                                error: function(xhr, status, error) {
                                    swal("Error!", "There was an error approving the product.", "error");
                                }
                            });
                        } else {
                            swal("Your order is not cancelled.");
                        }
                    });

                }

            });
        });

        function approveCancellation(element, orderId) {
            const swalApproveConfirmation = "<?= __("Are you sure you want to approve this cancellation request?") ?>";
            const swalApproveWarning = "<?= __("Once approved, the order will be permanently cancelled.") ?>";

            swal({
                title: swalApproveConfirmation,
                text: swalApproveWarning,
                icon: "warning",
                buttons: {
                    cancel: {
                        text: "Reject",
                        value: false,
                        visible: true,
                        className: "btn btn-danger",
                    },
                    confirm: {
                        text: "Approve",
                        value: true,
                        visible: true,
                        className: "btn btn-success",
                    }
                },
                dangerMode: true,
            }).then((willApprove) => {
                if (willApprove) {
                    condition = 'Approved';
                } else {
                    condition = 'Rejected';
                }
                $.ajax({
                    url: '<?= $this->Url->build(['controller' => 'Orders', 'action' => 'approveCancellation']) ?>',
                    type: 'POST',
                    data: {
                        id: orderId,
                        condition: condition
                        // reason: reason,
                        // order_cancellation_category_id: order_cancellation_category_id
                    },
                    headers: {
                        'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                    },
                    success: function(response) {
                        if (response.status === 'success') {
                            if (condition == 'Approved') {
                                swal("Cancellation Approved!", response.message, "success").then(function() {
                                    location.reload();
                                });
                            } else {
                                swal("Cancellation Rejected!", response.message, "success").then(function() {
                                    location.reload();
                                });
                            }
                        } else {
                            swal("Error!", response.message || "There was an error cancelling the order.", "error");
                        }
                    },
                    error: function(xhr, status, error) {
                        swal("Error!", "There was an error cancelling the order.", "error");
                    }
                });
            });
        }

        function approveReturn(element, orderId) {
            const swalApproveConfirmation = "<?= __("Are you sure you want to approve this return request?") ?>";
            const swalApproveWarning = "<?= __("Once approved, the order will be permanently in return status.") ?>";

            swal({
                title: swalApproveConfirmation,
                text: swalApproveWarning,
                icon: "warning",
                buttons: {
                    cancel: {
                        text: "Reject",
                        value: false,
                        visible: true,
                        className: "btn btn-danger",
                    },
                    confirm: {
                        text: "Approve",
                        value: true,
                        visible: true,
                        className: "btn btn-success",
                    }
                },
                dangerMode: true,
            }).then((willApprove) => {
                if (willApprove) {
                    condition = 'Approved';
                } else {
                    condition = 'Rejected';
                }
                $.ajax({
                    url: '<?= $this->Url->build(['controller' => 'Orders', 'action' => 'approveReturn']) ?>',
                    type: 'POST',
                    data: {
                        id: orderId,
                        condition: condition
                        // reason: reason,
                        // order_cancellation_category_id: order_cancellation_category_id
                    },
                    headers: {
                        'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                    },
                    success: function(response) {
                        if (response.status === 'success') {
                            if (condition == 'Approved') {
                                swal("Return Approved!", response.message, "success").then(function() {
                                    location.reload();
                                });
                            } else {
                                swal("Return Rejected!", response.message, "success").then(function() {
                                    location.reload();
                                });
                            }
                        } else {
                            swal("Error!", response.message || "There was an error returning the order.", "error");
                        }
                    },
                    error: function(xhr, status, error) {
                        swal("Error!", "There was an error returning the order.", "error");
                    }
                });
            });
        }

        // Function to update order status
        function updateOrderStatus(orderId, status, buttonElement) {
            // Check if order is already delivered or rejected and prevent status changes
            const currentStatus = '<?= $order->status ?>';
            if (currentStatus === 'Delivered') {
                swal("Not Allowed!", "<?= __('Cannot change status of a delivered order.') ?>", "warning");
                return;
            }
            if (currentStatus === 'Rejected') {
                swal("Not Allowed!", "<?= __('Cannot change status of a rejected order.') ?>", "warning");
                return;
            }

            // Show loading state
            const originalText = buttonElement.innerHTML;
            buttonElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Updating...';
            buttonElement.disabled = true;

            $.ajax({
                url: '<?= $this->Url->build(['controller' => 'Orders', 'action' => 'updateOrderStatus']) ?>',
                type: 'POST',
                data: {
                    id: orderId,
                    status: status
                },
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(response) {
                    if (response.status === 'success') {
                        // Update button states
                        $('.order-status-btn').each(function() {
                            const btnStatus = $(this).data('status');
                            if (btnStatus === status) {
                                // Make this button active
                                $(this).removeClass('btn-outline-warning btn-outline-primary btn-outline-info btn-outline-secondary btn-outline-dark btn-outline-success btn-outline-danger')
                                       .addClass(getActiveButtonClass(status));
                            } else {
                                // Make other buttons inactive (grey)
                                $(this).removeClass('btn-warning btn-primary btn-info btn-secondary btn-dark btn-success btn-danger')
                                       .addClass('btn-outline-secondary');
                            }

                            // If status is now "Delivered" or "Rejected", disable all buttons except the current one
                            if (status === 'Delivered' || status === 'Rejected') {
                                if (btnStatus !== status) {
                                    $(this).prop('disabled', true);
                                }
                            }
                        });

                        // Update payment status badge in Payment Information section
                        updatePaymentStatusBadge(status);

                        // Show success message
                        swal("Success!", response.message || "Order status updated successfully.", "success");
                    } else {
                        // Restore button state
                        buttonElement.innerHTML = originalText;
                        swal("Error!", response.message || "Failed to update order status.", "error");
                    }
                },
                error: function(xhr, status, error) {
                    // Restore button state
                    buttonElement.innerHTML = originalText;
                    swal("Error!", "There was an error updating the order status.", "error");
                },
                complete: function() {
                    buttonElement.disabled = false;
                    if (buttonElement.innerHTML.includes('Updating...')) {
                        buttonElement.innerHTML = originalText;
                    }
                }
            });
        }

        // Helper function to get active button class
        function getActiveButtonClass(status) {
            switch(status) {
                case 'Pending': return 'btn-warning';
                case 'Confirmed': return 'btn-primary';
                case 'Processing': return 'btn-info';
                case 'Shipped': return 'btn-secondary';
                case 'Out for Delivery': return 'btn-dark';
                case 'Delivered': return 'btn-success';
                case 'Rejected': return 'btn-danger';
                default: return 'btn-secondary';
            }
        }



        // Function to update payment status badge
        function updatePaymentStatusBadge(status) {
            const badge = document.getElementById('payment-status-badge');
            if (badge) {
                // Remove all existing status classes
                badge.className = badge.className.replace(/\b(l-bg-orange|l-bg-green|l-bg-blue|l-bg-red|bg-warning|bg-info|bg-success|bg-danger)\b/g, '');

                // Update text content
                badge.textContent = status.charAt(0).toUpperCase() + status.slice(1);

                // Add appropriate background class based on status
                let statusClass = '';
                switch(status) {
                    case 'Pending':
                        statusClass = 'bg-warning text-dark';
                        break;
                    case 'Confirmed':
                        statusClass = 'bg-primary text-white';
                        break;
                    case 'Processing':
                        statusClass = 'bg-info text-white';
                        break;
                    case 'Shipped':
                        statusClass = 'bg-secondary text-white';
                        break;
                    case 'Out for Delivery':
                        statusClass = 'bg-dark text-white';
                        break;
                    case 'Delivered':
                        statusClass = 'bg-success text-white';
                        break;
                    case 'Rejected':
                        statusClass = 'bg-danger text-white';
                        break;
                    default:
                        statusClass = 'bg-secondary text-white';
                }

                // Add the new classes
                badge.className = `text-nowrap ${statusClass} px-2 rounded`;
                badge.style.fontSize = 'small';
            }
        }
    </script>
    <?php $this->end(); ?>