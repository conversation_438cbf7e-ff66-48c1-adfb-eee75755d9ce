<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * SiteSetting Entity
 *
 * @property int $id
 * @property string $site_title
 * @property string $address_line1
 * @property string|null $address_line2
 * @property string $country
 * @property string $state
 * @property string $city
 * @property string $zipcode
 * @property string $customer_support_no
 * @property string $contact_no
 * @property string $support_email
 * @property string $admin_email
 * @property \Cake\I18n\Time $business_open_time
 * @property \Cake\I18n\Time $business_close_time
 * @property string|null $company_logo
 * @property string|null $fav_icon
 * @property string|null $facebook_url
 * @property string|null $twitter_url
 * @property string|null $pinterest_url
 * @property string|null $youtube_url
 * @property string|null $instagram_url
 * @property string|null $linkedin_url
 * @property \Cake\I18n\DateTime $created
 * @property \Cake\I18n\DateTime $modified
 */
class SiteSetting extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array<string, bool>
     */
    protected array $_accessible = [
        '*' => true,
        'id' => false,
        'product_cancel_in_days'=> true,
        'product_return_in_days' => true,
    ];
}
