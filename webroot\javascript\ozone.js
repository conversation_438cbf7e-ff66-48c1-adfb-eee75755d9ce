// function selectDropdownItem(element) {
//     const button = document.getElementById("dropdownButton");
//     const selectedContent = element.innerHTML;
//     button.innerHTML = selectedContent;
// }
// function selectDropdownregion(element) {
//     const button = document.getElementById("dropdownregion");
//     const selectedContent = element.innerHTML;
//     button.innerHTML = selectedContent;
// }

function updateQuantity(change) {
    const input = document.getElementById("unitInput");
    let value = parseInt(input.value) || 1;
    value = Math.max(1, value + change); // minimum quantity of 1
    input.value = value;
}
function updateQuantity2(change) {
    const input = document.getElementById("unitInput2");
    let value = parseInt(input.value) || 1;
    value = Math.max(1, value + change); // minimum quantity of 1
    input.value = value;
}


// General Carousel
// $('.owl-carousel').owlCarousel({
//     loop: true,
//     margin: 10,
//     responsiveClass: true,
//     touchDrag: true,   // Enable touch swipe
//     mouseDrag: true,   // Enable mouse drag (optional)
//     responsive: {
//         0: {
//             items: 1,
//             nav: true
//         },
//         600: {
//             items: 2,
//             nav: false
//         },
//         1000: {
//             items: 3,
//             nav: true,
//             loop: false
//         }
//     }
// });
$('#general-carousel').owlCarousel({
    loop: true,
    margin: 20,               // spacing between items
    stagePadding: 80,         // show part of the next/prev item
    responsiveClass: true,
    touchDrag: true,
    mouseDrag: true,
    responsive: {
        0: {
            items: 1.1,       // show 1 item + partial next
            nav: false,
            stagePadding: 40
        },
        600: {
            items: 1.5,       // 1.5 items visible
            nav: false,
            stagePadding: 60
        },
        1000: {
            items: 3,       // 2.5 items visible
            nav: true,
            stagePadding: 80,
            loop: true
        }
    }
});
$('#general-carousel-products').owlCarousel({
    loop: true,
    margin: 20,               // spacing between items
    stagePadding: 80,         // show part of the next/prev item
    responsiveClass: true,
    touchDrag: true,
    mouseDrag: true,
    responsive: {
        0: {
            items: 1.1,       // show 1 item + partial next
            nav: false,
            stagePadding: 40
        },
        600: {
            items: 1.5,       // 1.5 items visible
            nav: false,
            stagePadding: 60
        },
        1000: {
            items: 3,       // 2.5 items visible
            nav: true,
            stagePadding: 80,
            loop: true
        }
    }
});
$('.owl-carousel-product').owlCarousel({
    loop: true,
    margin: 20,               // spacing between items
    stagePadding: 80,         // show part of the next/prev item
    responsiveClass: true,
    touchDrag: true,
    mouseDrag: true,
    responsive: {
        0: {
            items: 1.1,       // show 1 item + partial next
            nav: false,
            stagePadding: 40
        },
        600: {
            items: 1.5,       // 1.5 items visible
            nav: false,
            stagePadding: 60
        },
        1000: {
            items: 3,       // 2.5 items visible
            nav: true,
            stagePadding: 80,
            loop: true
        }
    }
});
$('#owl-carousel-testimonial').owlCarousel({
    loop: true,
    margin: 20,               // spacing between items
    stagePadding: 80,         // show part of the next/prev item
    responsiveClass: true,
    touchDrag: true,
    mouseDrag: true,
    responsive: {
        0: {
            items: 1,       // show 1 item + partial next
            nav: false,
            stagePadding: 40
        },
        600: {
            items: 1,       // 1.5 items visible
            nav: false,
            stagePadding: 60
        },
        1000: {
            items: 3,       // 2.5 items visible
            nav: true,
            stagePadding: 0,
            loop: true
        }
    }
});

// General Carousel
$('#our-strength').owlCarousel({
    loop: true,
    margin: 10,
    responsiveClass: true,
    dots: true, // 👈 Enable dots
    responsive: {
        0: {
            items: 1,
            nav: true,
            dots: true
        },
        600: {
            items: 2,
            nav: false,
            dots: true
        },
        1000: {
            items: 3,
            nav: true,
            loop: false,
            dots: true
        }
    }
});

// Partner Carousel
$('#partner-carousel').owlCarousel({
    loop: true,
    margin: 10,
    nav: false,
    dots: false,
    autoplay: true,
    autoplayTimeout: 2000,
    responsive: {
        0: { items: 6 },       // Show 6 items on all screen sizes
        600: { items: 6 },
        1000: { items: 6 }
    }
});



document.querySelectorAll('.tile').forEach(tile => {
    const img = tile.querySelector('img');
    const scale = tile.getAttribute('data-scale');

    tile.addEventListener('mousemove', (e) => {
        const rect = tile.getBoundingClientRect();
        const offsetX = e.clientX - rect.left;
        const offsetY = e.clientY - rect.top;
        const percentX = (offsetX / rect.width) * 100;
        const percentY = (offsetY / rect.height) * 100;

        img.style.transformOrigin = `${percentX}% ${percentY}%`;
        img.style.transform = `scale(${scale})`;
    });

    tile.addEventListener('mouseleave', () => {
        img.style.transform = 'scale(1)';
        img.style.transformOrigin = 'center center';
    });
});



const heartIcon = document.getElementById('heartIcon');

if (heartIcon) {
    heartIcon.addEventListener('click', function () {
        console.log('heart clicked')
        this.classList.toggle('heart-colored');
    });
}


function toggleZoomFromTitle() {
    const activeItem = document.querySelector('#gallerycarousel .carousel-item.active');
    const img = activeItem.querySelector('.zoomable');
    const icon = document.getElementById('zoom-icon');

    img.classList.toggle('zoomed');

    if (img.classList.contains('zoomed')) {
        icon.src = "../../img/ozone/search-zoom-out.png";
    } else {
        icon.src = "../../img/ozone/search-zoom-in.png";
    }
}

// Reset zoom when slide changes
const carousel = document.getElementById('gallerycarousel');
if (carousel) {
    carousel.addEventListener('slide.bs.carousel', () => {
        document.querySelectorAll('.zoomable').forEach(img => img.classList.remove('zoomed'));
        const zoomIcon = document.getElementById('zoom-icon');
        if (zoomIcon) {
            zoomIcon.src = "../../img/ozone/search-zoom-in.png";
        }
    });
}