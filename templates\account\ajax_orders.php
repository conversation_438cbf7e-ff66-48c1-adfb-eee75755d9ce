<?php foreach ($orders as $k => $order): ?>
<div class="accordion-item mb-3">
    <h2 class="accordion-header" id="headingOrder<?= $order->id ?>">
        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
            data-bs-target="#collapseOrder<?= $order->id ?>" aria-expanded="false"
            aria-controls="collapseOrder<?= $order->id ?>">
            <?= __('Order') ?> #<?= h($order->order_number ?? $order->id) ?> &nbsp;|&nbsp;
            <span class="status-badge status-<?= strtolower($order->status) ?> ms-2"><?= h($order->status) ?></span>
        </button>
    </h2>
    <div id="collapseOrder<?= $order->id ?>" class="accordion-collapse collapse"
        aria-labelledby="headingOrder<?= $order->id ?>">
        <div class="accordion-body">
            <div class="row mb-3">
                                                            <div class="col-md-6">
                                                                <p><strong><?= __('Order Date:') ?></strong> <?= h($order->order_date ? date('Y-m-d', strtotime($order->order_date)) : '') ?></p>
                                                                <p><strong><?= __('Estimated Delivery:') ?></strong> <?= h($order->delivery_date ?? '-') ?></p>
                                                            </div>
                                                        </div>
                                                        <?php if (!empty($order->order_items)): ?>
                                                            <ul class="list-group mb-3">
                                                                <?php foreach ($order->order_items as $item): ?>
                                                                    <li class="list-group-item">
                                                                        <div class="d-flex justify-content-between mb-3">
                                                                            <div>
                                                                                <strong><a class="text-decoration-none text-dark" target="_blank" href="/product/<?= h($item->product->id) ?>"><?= h($item->product_name ?? 'Product') ?></a>
                                                                                <a class="text-dark" target="_blank" href="/product/<?= h($item->product->id) ?>"><i class="mx-2 fas fa-eye"></i></a></strong><br>
                                                                                <?= __('Quantity:') ?> <?= h($item->quantity) ?>
                                                                            </div>
                                                                            <div><strong><?= $this->Price->setPriceFormat($item->price) ?></strong></div>
                                                                        </div>
                                                                        <?php if ($order->status == 'Delivered'): ?>
                                                            <?php
                                                            // Check if this order item has been reviewed
                                                            $hasReview = false;
                                                            $reviewRating = 0;
                                                            if (!empty($item->order_item_reviews)) {
                                                                foreach ($item->order_item_reviews as $review) {
                                                                    $hasReview = true;
                                                                    $reviewRating = $review->rating;
                                                                    break;
                                                                }
                                                            }
                                                            ?>
                                                            <div class="mt-2">
                                                                <?php if ($hasReview): ?>
                                                                    <div class="d-flex align-items-center">
                                                                        <strong class="me-2"><?= __('Your Review:') ?></strong>
                                                                        <div class="star-display">
                                                                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                                                                <i class="<?= $i <= $reviewRating ? 'fas' : 'far' ?> fa-star text-warning"></i>
                                                                            <?php endfor; ?>
                                                                        </div>
                                                                        <!-- <span class="badge bg-success ms-2"><?= __('Reviewed') ?></span> -->
                                                                    </div>
                                                                <?php else: ?>
                                                                    <button type="button" class="btn btn-outline-primary btn-sm"
                                                                            onclick="openReviewModal(<?= h($order->id) ?>, <?= h($item->id) ?>, <?= h($item->product_id) ?>, '<?= h($item->product_name) ?>')">
                                                                        <i class="fas fa-star me-1"></i><?= __('Rate & Review') ?>
                                                                    </button>
                                                                <?php endif; ?>
                                                            </div>
                                                        <?php endif; ?>
                                                                    </li>
                                                                <?php endforeach; ?>
                                                            </ul>
                                                        <?php endif; ?>
                                                        <!-- Action Buttons -->
                                                        <div class="d-flex flex-wrap gap-2">
                                                            <?php if ($order->status == 'Pending' || $order->status == 'Processing' || $order->status == 'Approved'): ?>
                                                                <button class="btn btn-outline-danger btn-sm" onclick="cancelOrder(<?= $order->id ?>)"><?= __('Cancel Order') ?></button>
                                                            <?php endif; ?>
                                                            <?php if ($order->status == 'Delivered' && strtotime($order->delivery_date) > strtotime('-7 days')): ?>
                                                                <button class="btn btn-primary btn-sm" onclick="returnOrder(<?= $order->id ?>)"><?= __('Request Return') ?></button>
                                                            <?php endif; ?>
                                                            <a target="_blank" href="<?= $this->Url->build(['controller' => 'Account','action' => 'downloadInvoice', $order->id]); ?>" class="btn btn-outline-primary btn-sm"><?= __('Download Invoice (PDF)') ?></a>

                                                            <?php if ($order->status == 'Delivered'): ?>
                                                                <?php
                                                                // Check if there are any unreviewed items in this order
                                                                $hasUnreviewedItems = false;
                                                                foreach ($order->order_items as $item) {
                                                                    $itemReviewed = false;
                                                                    if (!empty($item->order_item_reviews)) {
                                                                        $itemReviewed = true;
                                                                    }
                                                                    if (!$itemReviewed) {
                                                                        $hasUnreviewedItems = true;
                                                                        break;
                                                                    }
                                                                }
                                                                ?>
                                                                <!-- <?php if ($hasUnreviewedItems): ?>
                                                                    <button type="button" class="btn btn-warning btn-sm" onclick="openBulkReviewModal(<?= h($order->id) ?>)">
                                                                        <i class="fas fa-star me-1"></i><?= __('Review All Items') ?>
                                                                    </button>
                                                                <?php endif; ?> -->
                                                            <?php endif; ?>
                                                        </div>
        </div>
    </div>
</div>
<?php endforeach; ?>