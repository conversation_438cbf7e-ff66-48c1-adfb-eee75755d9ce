<?php foreach ($orders as $k => $order): ?>
<div class="accordion-item mb-3">
    <h2 class="accordion-header" id="headingOrder<?= $order->id ?>">
        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
            data-bs-target="#collapseOrder<?= $order->id ?>" aria-expanded="false"
            aria-controls="collapseOrder<?= $order->id ?>">
            <?= __('Order') ?> #<?= h($order->order_number ?? $order->id) ?> &nbsp;|&nbsp;
            <span class="status-badge status-<?= strtolower($order->status) ?> ms-2"><?= h($order->status) ?></span>
        </button>
    </h2>
    <div id="collapseOrder<?= $order->id ?>" class="accordion-collapse collapse"
        aria-labelledby="headingOrder<?= $order->id ?>">
        <div class="accordion-body">
            <div class="row mb-3">
                                                            <div class="col-md-6">
                                                                <p><strong><?= __('Order Date:') ?></strong> <?= h($order->order_date ? date('Y-m-d', strtotime($order->order_date)) : '') ?></p>
                                                                <p><strong><?= __('Estimated Delivery:') ?></strong> <?= h($order->delivery_date ?? '-') ?></p>
                                                            </div>
                                                        </div>
                                                        <?php if (!empty($order->order_items)): ?>
                                                            <ul class="list-group mb-3">
                                                                <?php foreach ($order->order_items as $item): ?>
                                                                    <li class="list-group-item">
                                                                        <div class="d-flex justify-content-between mb-3">
                                                                            <div>
                                                                                <strong><a class="text-decoration-none text-dark" target="_blank" href="/product/<?= h($item->product->id) ?>"><?= h($item->product_name ?? 'Product') ?></a>
                                                                                <a class="text-dark" target="_blank" href="/product/<?= h($item->product->id) ?>"><i class="mx-2 fas fa-eye"></i></a></strong><br>
                                                                                <?= __('Quantity:') ?> <?= h($item->quantity) ?>
                                                                            </div>
                                                                            <div><strong><?= $this->Price->setPriceFormat($item->price) ?></strong></div>
                                                                        </div>
                                                                        <?php if ($order->status == 'Delivered'): ?>
                                                                                <div>
                                                                                    <strong><?= __('Rate this Product') ?></strong>
                                                                                    <!-- Star Buttons -->
                                                                                    <button <?= !empty($item->product->reviews) ? 'disabled' : '' ?> type="button" class="btn btn" onclick="rateProduct(<?= h($item->product->id) ?>)" style="color:#FF6200">
                                                                                        <i class="far fa-star"></i><i class="far fa-star"></i><i class="far fa-star"></i><i class="far fa-star"></i><i class="far fa-star"></i>
                                                                                    </button>
                                                                                </div>
                                                                        <?php endif; ?>
                                                                    </li>
                                                                <?php endforeach; ?>
                                                            </ul>
                                                        <?php endif; ?>
                                                        <!-- Action Buttons -->
                                                        <div class="d-flex flex-wrap gap-2">
                                                            <?php if ($order->status == 'Pending' || $order->status == 'Processing' || $order->status == 'Approved'): ?>
                                                                <button class="btn btn-outline-danger btn-sm" onclick="cancelOrder(<?= $order->id ?>)"><?= __('Cancel Order') ?></button>
                                                            <?php endif; ?>
                                                            <?php if ($order->status == 'Delivered' && strtotime($order->delivery_date) > strtotime('-7 days')): ?>
                                                                <button class="btn btn-primary btn-sm" onclick="returnOrder(<?= $order->id ?>)"><?= __('Request Return') ?></button>
                                                            <?php endif; ?>
                                                            <a target="_blank" href="<?= $this->Url->build(['controller' => 'Account','action' => 'downloadInvoice', $order->id]); ?>" class="btn btn-outline-primary btn-sm"><?= __('Download Invoice (PDF)') ?></a>
                                                        </div>
        </div>
    </div>
</div>
<?php endforeach; ?>