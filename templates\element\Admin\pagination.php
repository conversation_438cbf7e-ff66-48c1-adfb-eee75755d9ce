<?php
/**
 * Admin Pagination Element
 */
?>

<div class="row">
    <div class="col-sm-12 col-md-5">
        <div class="dataTables_info">
            <?= $this->Paginator->counter(__('Showing {{start}} to {{end}} of {{count}} entries')) ?>
        </div>
    </div>
    <div class="col-sm-12 col-md-7">
        <div class="dataTables_paginate paging_simple_numbers">
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-end">
                    <?= $this->Paginator->first('<<', [
                        'class' => 'page-link',
                        'templates' => [
                            'first' => '<li class="page-item"><a class="page-link" href="{{url}}">{{text}}</a></li>'
                        ]
                    ]) ?>
                    
                    <?= $this->Paginator->prev('<', [
                        'class' => 'page-link',
                        'templates' => [
                            'prevActive' => '<li class="page-item"><a class="page-link" href="{{url}}">{{text}}</a></li>',
                            'prevDisabled' => '<li class="page-item disabled"><span class="page-link">{{text}}</span></li>'
                        ]
                    ]) ?>
                    
                    <?= $this->Paginator->numbers([
                        'modulus' => 4,
                        'separator' => '',
                        'class' => 'page-link',
                        'currentClass' => 'active',
                        'templates' => [
                            'number' => '<li class="page-item"><a class="page-link" href="{{url}}">{{text}}</a></li>',
                            'current' => '<li class="page-item active"><span class="page-link">{{text}}</span></li>',
                        ]
                    ]) ?>
                    
                    <?= $this->Paginator->next('>', [
                        'class' => 'page-link',
                        'templates' => [
                            'nextActive' => '<li class="page-item"><a class="page-link" href="{{url}}">{{text}}</a></li>',
                            'nextDisabled' => '<li class="page-item disabled"><span class="page-link">{{text}}</span></li>'
                        ]
                    ]) ?>
                    
                    <?= $this->Paginator->last('>>', [
                        'class' => 'page-link',
                        'templates' => [
                            'last' => '<li class="page-item"><a class="page-link" href="{{url}}">{{text}}</a></li>'
                        ]
                    ]) ?>
                </ul>
            </nav>
        </div>
    </div>
</div>
