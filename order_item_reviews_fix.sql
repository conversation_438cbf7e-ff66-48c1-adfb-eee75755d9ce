-- SQL script to fix order_item_reviews table for product page reviews
-- This allows reviews to be created without order_id and order_item_id

-- First, check if there are any foreign key constraints that need to be dropped
-- You may need to adjust the constraint names based on your actual database

-- Drop foreign key constraints if they exist (adjust constraint names as needed)
-- ALTER TABLE `order_item_reviews` DROP FOREIGN KEY `fk_order_item_reviews_orders`;
-- ALTER TABLE `order_item_reviews` DROP FOREIGN KEY `fk_order_item_reviews_order_items`;

-- Ensure the columns allow NULL values (they already do based on your screenshot)
ALTER TABLE `order_item_reviews` 
MODIFY COLUMN `order_id` int(11) NULL,
MODIFY COLUMN `order_item_id` int(11) NULL;

-- Update the status enum to include the new status values
ALTER TABLE `order_item_reviews` 
MODIFY COLUMN `status` enum('pending','approved','rejected') 
COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'pending';

-- Remove the publish_status column if it exists and is no longer needed
-- ALTER TABLE `order_item_reviews` DROP COLUMN `publish_status`;

-- Add foreign key constraints back with ON DELETE SET NULL to handle deletions gracefully
-- Adjust the constraint names and referenced table/column names as needed
ALTER TABLE `order_item_reviews` 
ADD CONSTRAINT `fk_order_item_reviews_orders` 
FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) 
ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE `order_item_reviews` 
ADD CONSTRAINT `fk_order_item_reviews_order_items` 
FOREIGN KEY (`order_item_id`) REFERENCES `order_items` (`id`) 
ON DELETE SET NULL ON UPDATE CASCADE;

-- Ensure product_id foreign key exists and is properly configured
ALTER TABLE `order_item_reviews` 
ADD CONSTRAINT `fk_order_item_reviews_products` 
FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- Ensure customer_id foreign key exists and is properly configured
ALTER TABLE `order_item_reviews` 
ADD CONSTRAINT `fk_order_item_reviews_customers` 
FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- Add an index for better performance on product page reviews
CREATE INDEX `idx_product_customer_review` ON `order_item_reviews` (`product_id`, `customer_id`);

-- Add an index for order-based reviews
CREATE INDEX `idx_order_item_review` ON `order_item_reviews` (`order_id`, `order_item_id`);
