<?php
/**
 * Test script to verify OrderItemReview saving with NULL order_id and order_item_id
 * 
 * Place this file in the webroot directory and run it to test the review saving
 */

// Initialize CakePHP
require dirname(__DIR__) . '/vendor/autoload.php';
require dirname(__DIR__) . '/config/bootstrap.php';

use Cake\Datasource\ConnectionManager;
use Cake\ORM\TableRegistry;

// Get the OrderItemReviews table
$orderItemReviewsTable = TableRegistry::getTableLocator()->get('OrderItemReviews');

// Create test data
$testReview = [
    'customer_id' => 1, // Replace with a valid customer ID
    'product_id' => 1, // Replace with a valid product ID
    'rating' => 5,
    'review' => 'This is a test review from the product page',
    'status' => 'pending',
    'order_id' => null,
    'order_item_id' => null
];

// Create entity
$review = $orderItemReviewsTable->newEntity($testReview);

// Try to save
echo "<h1>Testing OrderItemReview Save</h1>";
echo "<pre>";
echo "Attempting to save review with NULL order_id and order_item_id...\n\n";

try {
    if ($orderItemReviewsTable->save($review)) {
        echo "SUCCESS: Review saved successfully!\n";
        echo "Review ID: " . $review->id . "\n";
        echo "Customer ID: " . $review->customer_id . "\n";
        echo "Product ID: " . $review->product_id . "\n";
        echo "Rating: " . $review->rating . "\n";
        echo "Review Text: " . $review->review . "\n";
        echo "Status: " . $review->status . "\n";
        echo "Order ID: " . ($review->order_id ?? 'NULL') . "\n";
        echo "Order Item ID: " . ($review->order_item_id ?? 'NULL') . "\n";
    } else {
        echo "ERROR: Failed to save review\n";
        echo "Validation errors:\n";
        print_r($review->getErrors());
    }
} catch (\Exception $e) {
    echo "EXCEPTION: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    echo "Trace:\n" . $e->getTraceAsString() . "\n";
}

echo "</pre>";
