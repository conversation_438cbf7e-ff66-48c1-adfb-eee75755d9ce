<?php
namespace App\Controller\Component;

use Cake\Controller\Component;
use Cake\Core\Configure;

class QibPaymentComponent extends Component
{
    protected $_gatewayId;
    protected $_secretKey;

    public function initialize(array $config): void
    {
        parent::initialize($config);
        $this->_gatewayId = Configure::read('Settings.GATEWAYID');
        $this->_secretKey = Configure::read('Settings.SECRETKEY');
    }

    public function generateSignature($amount, $referenceId)
    {
        $amount = number_format($amount, 2, '.', '');
        $hashable_string = "gatewayId={$this->_gatewayId},amount={$amount},referenceId={$referenceId}";
        return base64_encode(hash_hmac('sha256', $hashable_string, $this->_secretKey, true));
    }

    public function getFormFields($data)
    {
        $amount = number_format($data['amount'], 2, '.', '');
        $referenceId = $data['referenceId'];
        $signature = $this->generateSignature($amount, $referenceId);

        return [
            'action' => 'capture',
            'gatewayId' => $this->_gatewayId,
            'signatureFields' => 'gatewayId,amount,referenceId',
            'signature' => $signature,
            'referenceId' => $referenceId,
            'amount' => $amount,
            'currency' => 'QAR',
            'mode' => 'LIVE',
            'description' => 'Order Payment',
            'returnUrl' => $data['returnUrl'] ?? '',
            'name' => $data['name'] ?? '',
            'address' => $data['address'] ?? '',
            'city' => $data['city'] ?? '',
            'state' => $data['state'] ?? '',
            'country' => 'QA',
            'phone' => $data['phone'] ?? '',
            'email' => $data['email'] ?? '',
        ];
    }
}