<?php
/**
 * @var \App\View\AppView $this
 * @var iterable<\App\Model\Entity\OrderItemReview> $reviews
 */
?>
<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/datatables.min.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/css/dataTables.bootstrap4.min.css') ?>">
<style>
    .filter-panel {
        margin-bottom: 20px;
        transition: all 0.3s ease;
    }

    .filter-panel .card {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
    }

    .btn.menu-toggle {
        transition: all 0.3s ease;
        background-color: #fd7e14;
        color: white;
        border: none;
    }

    .btn.menu-toggle:hover {
        background-color: #e8690b;
        color: white;
    }

    .btn.menu-toggle.filter-active {
        background-color: #dc6545;
        color: white;
        box-shadow: 0 0 10px rgba(220, 101, 69, 0.5);
    }

    .btn.menu-toggle:focus {
        outline: none;
        box-shadow: 0 0 0 0.2rem rgba(253, 126, 20, 0.25);
    }

    .form-group label {
        font-weight: 600;
        margin-bottom: 5px;
        color: #495057;
    }

    /* Prevent any interference */
    .filter-panel * {
        box-sizing: border-box;
    }
</style>
<?php $this->end(); ?>

<div class="section-header">
    <ul class="breadcrumb breadcrumb-style">
        <li class="breadcrumb-item">
            <a href="<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'index']) ?>">
                <h4 class="page-title m-b-0"><?= __("Dashboard") ?></h4>
            </a>
        </li>
        <li class="breadcrumb-item"><?= __("Reviews") ?></li>
        <li class="breadcrumb-item active"><?= __("Reviews") ?></li>
    </ul>
</div>
<div class="section-body1">
    <div class="container-fluid">
        <?= $this->Flash->render() ?>
    </div>
</div>
<div class="section-body" id="list">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <h4><?= __("Manage Reviews") ?></h4>
                <div class="card-header-form">
                    <div class="input-group">
                        <input type="text" class="form-control search-control" placeholder="<?= __("Search") ?>" id="searchInput" value="<?= h($search ?? '') ?>" />
                        <div class="input-group-btn">
                            <button class="btn" type="button" id="searchBtn">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                        <button class="btn menu-toggle" type="button" id="filterToggleBtn">
                            <i class="fas fa-filter"></i>
                            <?= __("Filter") ?>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter Container -->
        <div id="filterContainer" class="filter-panel" style="display: none;">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label><?= __('Status') ?></label>
                                <select class="form-control" id="statusFilter">
                                    <option value=""><?= __('All Status') ?></option>
                                    <option value="Active" <?= ($status ?? '') === 'Active' ? 'selected' : '' ?>><?= __('Active') ?></option>
                                    <option value="Inactive" <?= ($status ?? '') === 'Inactive' ? 'selected' : '' ?>><?= __('Inactive') ?></option>
                                    <option value="Deleted" <?= ($status ?? '') === 'Deleted' ? 'selected' : '' ?>><?= __('Deleted') ?></option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label><?= __('Publish Status') ?></label>
                                <select class="form-control" id="publishStatusFilter">
                                    <option value=""><?= __('All Publish Status') ?></option>
                                    <option value="pending" <?= ($publishStatus ?? '') === 'pending' ? 'selected' : '' ?>><?= __('Pending') ?></option>
                                    <option value="published" <?= ($publishStatus ?? '') === 'published' ? 'selected' : '' ?>><?= __('Published') ?></option>
                                    <option value="rejected" <?= ($publishStatus ?? '') === 'rejected' ? 'selected' : '' ?>><?= __('Rejected') ?></option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label><?= __('Rating') ?></label>
                                <select class="form-control" id="ratingFilter">
                                    <option value=""><?= __('All Ratings') ?></option>
                                    <option value="5" <?= ($rating ?? '') === '5' ? 'selected' : '' ?>><?= __('5 Stars') ?></option>
                                    <option value="4" <?= ($rating ?? '') === '4' ? 'selected' : '' ?>><?= __('4 Stars') ?></option>
                                    <option value="3" <?= ($rating ?? '') === '3' ? 'selected' : '' ?>><?= __('3 Stars') ?></option>
                                    <option value="2" <?= ($rating ?? '') === '2' ? 'selected' : '' ?>><?= __('2 Stars') ?></option>
                                    <option value="1" <?= ($rating ?? '') === '1' ? 'selected' : '' ?>><?= __('1 Star') ?></option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div class="d-flex">
                                    <button type="button" class="btn btn-primary mr-2" id="applyFiltersBtn">
                                        <i class="fa fa-filter" aria-hidden="true"></i> <?= __('Apply') ?>
                                    </button>
                                    <button type="button" class="btn btn-secondary" id="resetFiltersBtn">
                                        <i class="fas fa-redo-alt"></i> <?= __('Reset') ?>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Reviews Table Card -->
        <div class="card">

            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped" id="table-1">
                        <thead>
                            <tr>
                                <th class="text-center"><?= __('ID') ?></th>
                                <th><?= __('Customer') ?></th>
                                <th><?= __('Product') ?></th>
                                <th><?= __('Order') ?></th>
                                <th><?= __('Rating') ?></th>
                                <th><?= __('Review') ?></th>
                                <th><?= __('Status') ?></th>
                                <th><?= __('Publish Status') ?></th>
                                <th><?= __('Date') ?></th>
                                <th><?= __('Action') ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $i = 1; ?>
                            <?php foreach ($reviews as $review): ?>
                                <tr>
                                    <td class="text-center"><?= $i++ ?></td>
                                    <td>
                                        <?= h($review->customer->user->first_name . ' ' . $review->customer->user->last_name) ?>
                                        <br><small class="text-muted"><?= h($review->customer->user->email) ?></small>
                                    </td>
                                    <td><?= h($review->product->name) ?></td>
                                    <td>#<?= h($review->order->order_number ?? $review->order_id) ?></td>
                                    <td>
                                        <div class="rating">
                                            <?php for ($j = 1; $j <= 5; $j++): ?>
                                                <i class="<?= $j <= $review->rating ? 'fas' : 'far' ?> fa-star text-warning"></i>
                                            <?php endfor; ?>
                                            <span class="ml-1">(<?= $review->rating ?>/5)</span>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if (!empty($review->review)): ?>
                                            <span title="<?= h($review->review) ?>">
                                                <?= h(substr($review->review, 0, 50)) ?><?= strlen($review->review) > 50 ? '...' : '' ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted"><?= __('No review text') ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge badge-<?= $review->status === 'Active' ? 'success' : ($review->status === 'Inactive' ? 'warning' : 'danger') ?>">
                                            <?= h($review->status) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <select class="form-control form-control-sm publish-status-select" data-id="<?= $review->id ?>">
                                            <option value="pending" <?= $review->publish_status === 'pending' ? 'selected' : '' ?>><?= __('Pending') ?></option>
                                            <option value="published" <?= $review->publish_status === 'published' ? 'selected' : '' ?>><?= __('Published') ?></option>
                                            <option value="rejected" <?= $review->publish_status === 'rejected' ? 'selected' : '' ?>><?= __('Rejected') ?></option>
                                        </select>
                                    </td>
                                    <td><?= $review->created->format('Y-m-d') ?></td>
                                    <td>
                                        <?= $this->Html->link('<i class="fas fa-eye"></i>', ['action' => 'view', $review->id], [
                                            'class' => 'btn btn-outline-primary btn-sm',
                                            'escape' => false,
                                            'title' => __('View')
                                        ]) ?>
                                        <?= $this->Html->link('<i class="fas fa-edit"></i>', ['action' => 'edit', $review->id], [
                                            'class' => 'btn btn-outline-warning btn-sm',
                                            'escape' => false,
                                            'title' => __('Edit')
                                        ]) ?>
                                        <?= $this->Form->postLink('<i class="fas fa-trash"></i>', ['action' => 'delete', $review->id], [
                                            'class' => 'btn btn-outline-danger btn-sm',
                                            'escape' => false,
                                            'title' => __('Delete'),
                                            'confirm' => __('Are you sure you want to delete this review?')
                                        ]) ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/datatables/datatables.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/js/dataTables.bootstrap4.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('js/filter.js') ?>"></script>
<script>
    var table = $("#table-1").DataTable({
        columnDefs: [
            { orderable: false, targets: -1 } // Make the last column non-sortable
        ],
        dom: 'rt',
        pageLength: 1000, // Show all records since we're using backend pagination
        searching: false,
        paging: false,
        info: false,
        ordering: true
    });

    // Publish status change functionality
    $('.publish-status-select').on('change', function() {
        var id = $(this).data('id');
        var status = $(this).val();
        var selectElement = $(this);
        var originalValue = selectElement.data('original-value') || selectElement.val();

        $.ajax({
            url: '<?= $this->Url->build(['action' => 'updatePublishStatus']) ?>',
            method: 'POST',
            data: {
                id: id,
                status: status,
                _csrfToken: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    // Update the original value
                    selectElement.data('original-value', status);

                    // Show success message
                    alert('<?= __('Review status updated successfully.') ?>');
                } else {
                    alert(response.message || '<?= __('Failed to update review status.') ?>');
                    // Reset to original value
                    selectElement.val(originalValue);
                }
            },
            error: function() {
                alert('<?= __('An error occurred while updating the status.') ?>');
                // Reset to original value
                selectElement.val(originalValue);
            }
        });
    });

    // Store original values for reset on error
    $('.publish-status-select').each(function() {
        $(this).data('original-value', $(this).val());
    });

    // Custom Filter System - Brand New Implementation
    var FilterSystem = {
        isVisible: false,
        container: null,
        button: null,

        init: function() {
            this.container = document.getElementById('filterContainer');
            this.button = document.getElementById('filterToggleBtn');

            if (!this.container || !this.button) {
                console.error('Filter elements not found');
                return;
            }

            // Set initial state
            this.container.style.display = 'none';
            this.isVisible = false;
            this.button.classList.remove('filter-active');

            // Add click event
            this.button.addEventListener('click', this.toggle.bind(this));

            console.log('Filter system initialized');
        },

        toggle: function(event) {
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }

            console.log('Toggle called, current state:', this.isVisible);

            if (this.isVisible) {
                this.hide();
            } else {
                this.show();
            }
        },

        show: function() {
            console.log('Showing filter');
            this.container.style.display = 'block';
            this.button.classList.add('filter-active');
            this.isVisible = true;
        },

        hide: function() {
            console.log('Hiding filter');
            this.container.style.display = 'none';
            this.button.classList.remove('filter-active');
            this.isVisible = false;
        }
    };

    // Search and Filter Functions
    var SearchAndFilter = {
        init: function() {
            // Search functionality
            document.getElementById('searchBtn').addEventListener('click', this.performSearch);
            document.getElementById('searchInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    SearchAndFilter.performSearch();
                }
            });

            // Filter functionality
            document.getElementById('applyFiltersBtn').addEventListener('click', this.applyFilters);
            document.getElementById('resetFiltersBtn').addEventListener('click', this.resetFilters);
        },

        performSearch: function() {
            var searchTerm = document.getElementById('searchInput').value;
            var url = new URL(window.location.href);

            if (searchTerm.trim()) {
                url.searchParams.set('search', searchTerm);
            } else {
                url.searchParams.delete('search');
            }

            window.location.href = url.toString();
        },

        applyFilters: function() {
            var url = new URL(window.location.href);

            // Get filter values
            var status = document.getElementById('statusFilter').value;
            var publishStatus = document.getElementById('publishStatusFilter').value;
            var rating = document.getElementById('ratingFilter').value;

            // Set or remove parameters
            if (status) {
                url.searchParams.set('status', status);
            } else {
                url.searchParams.delete('status');
            }

            if (publishStatus) {
                url.searchParams.set('publish_status', publishStatus);
            } else {
                url.searchParams.delete('publish_status');
            }

            if (rating) {
                url.searchParams.set('rating', rating);
            } else {
                url.searchParams.delete('rating');
            }

            // Hide filter panel and navigate
            FilterSystem.hide();
            window.location.href = url.toString();
        },

        resetFilters: function() {
            var url = new URL(window.location.origin + window.location.pathname);
            FilterSystem.hide();
            window.location.href = url.toString();
        }
    };

    // Initialize everything when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        FilterSystem.init();
        SearchAndFilter.init();
        console.log('All systems initialized');
    });
</script>
<?php $this->end(); ?>
