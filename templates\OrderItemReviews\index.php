<?php
/**
 * @var \App\View\AppView $this
 * @var iterable<\App\Model\Entity\OrderItemReview> $reviews
 */
?>

<div class="content-wrapper">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0"><?= __('Order Item Reviews') ?></h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= $this->Url->build(['controller' => 'Dashboard', 'action' => 'index']) ?>"><?= __('Home') ?></a></li>
                        <li class="breadcrumb-item active"><?= __('Reviews') ?></li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <section class="content">
        <div class="container-fluid">
            <!-- Statistics Cards -->
            <div class="row mb-3">
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-info">
                        <div class="inner">
                            <h3><?= number_format($stats['total']) ?></h3>
                            <p><?= __('Total Reviews') ?></p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-star"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-warning">
                        <div class="inner">
                            <h3><?= number_format($stats['pending']) ?></h3>
                            <p><?= __('Pending Reviews') ?></p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-clock"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-success">
                        <div class="inner">
                            <h3><?= number_format($stats['published']) ?></h3>
                            <p><?= __('Published Reviews') ?></p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-check"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-secondary">
                        <div class="inner">
                            <h3><?= number_format($stats['average_rating'], 1) ?></h3>
                            <p><?= __('Average Rating') ?></p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-star-half-alt"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title"><?= __('Filters') ?></h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-tool" data-card-widget="collapse">
                            <i class="fas fa-minus"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <?= $this->Form->create(null, ['type' => 'get', 'class' => 'row']) ?>
                    <div class="col-md-2">
                        <?= $this->Form->control('status', [
                            'type' => 'select',
                            'options' => [
                                '' => __('All Status'),
                                'Active' => __('Active'),
                                'Inactive' => __('Inactive'),
                                'Deleted' => __('Deleted')
                            ],
                            'value' => $status,
                            'class' => 'form-control',
                            'label' => __('Status')
                        ]) ?>
                    </div>
                    <div class="col-md-2">
                        <?= $this->Form->control('publish_status', [
                            'type' => 'select',
                            'options' => [
                                '' => __('All Publish Status'),
                                'pending' => __('Pending'),
                                'published' => __('Published'),
                                'rejected' => __('Rejected')
                            ],
                            'value' => $publishStatus,
                            'class' => 'form-control',
                            'label' => __('Publish Status')
                        ]) ?>
                    </div>
                    <div class="col-md-2">
                        <?= $this->Form->control('rating', [
                            'type' => 'select',
                            'options' => [
                                '' => __('All Ratings'),
                                '5' => __('5 Stars'),
                                '4' => __('4 Stars'),
                                '3' => __('3 Stars'),
                                '2' => __('2 Stars'),
                                '1' => __('1 Star')
                            ],
                            'value' => $rating,
                            'class' => 'form-control',
                            'label' => __('Rating')
                        ]) ?>
                    </div>
                    <div class="col-md-2">
                        <?= $this->Form->control('date_from', [
                            'type' => 'date',
                            'value' => $dateFrom,
                            'class' => 'form-control',
                            'label' => __('Date From')
                        ]) ?>
                    </div>
                    <div class="col-md-2">
                        <?= $this->Form->control('date_to', [
                            'type' => 'date',
                            'value' => $dateTo,
                            'class' => 'form-control',
                            'label' => __('Date To')
                        ]) ?>
                    </div>
                    <div class="col-md-2">
                        <?= $this->Form->control('search', [
                            'type' => 'text',
                            'value' => $search,
                            'class' => 'form-control',
                            'label' => __('Search'),
                            'placeholder' => __('Product, Customer, Order...')
                        ]) ?>
                    </div>
                    <div class="col-md-12 mt-3">
                        <?= $this->Form->button(__('Filter'), ['class' => 'btn btn-primary']) ?>
                        <?= $this->Html->link(__('Clear'), ['action' => 'index'], ['class' => 'btn btn-secondary']) ?>
                    </div>
                    <?= $this->Form->end() ?>
                </div>
            </div>

            <!-- Reviews Table -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title"><?= __('Reviews List') ?></h3>
                    <div class="card-tools">
                        <div class="btn-group">
                            <button type="button" class="btn btn-success btn-sm" onclick="bulkAction('publish')" disabled id="bulk-publish-btn">
                                <i class="fas fa-check"></i> <?= __('Publish Selected') ?>
                            </button>
                            <button type="button" class="btn btn-danger btn-sm" onclick="bulkAction('reject')" disabled id="bulk-reject-btn">
                                <i class="fas fa-times"></i> <?= __('Reject Selected') ?>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body table-responsive p-0">
                    <table class="table table-hover text-nowrap">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" id="select-all">
                                </th>
                                <th><?= __('ID') ?></th>
                                <th><?= __('Customer') ?></th>
                                <th><?= __('Product') ?></th>
                                <th><?= __('Order') ?></th>
                                <th><?= __('Rating') ?></th>
                                <th><?= __('Review') ?></th>
                                <th><?= __('Status') ?></th>
                                <th><?= __('Publish Status') ?></th>
                                <th><?= __('Date') ?></th>
                                <th><?= __('Actions') ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($reviews)): ?>
                                <?php foreach ($reviews as $review): ?>
                                    <tr>
                                        <td>
                                            <input type="checkbox" class="review-checkbox" value="<?= $review->id ?>">
                                        </td>
                                        <td><?= $review->id ?></td>
                                        <td>
                                            <?= h($review->customer->user->first_name . ' ' . $review->customer->user->last_name) ?>
                                            <br><small class="text-muted"><?= h($review->customer->user->email) ?></small>
                                        </td>
                                        <td>
                                            <?= h($review->product->name) ?>
                                        </td>
                                        <td>
                                            #<?= h($review->order->order_number ?? $review->order_id) ?>
                                        </td>
                                        <td>
                                            <div class="rating">
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <i class="<?= $i <= $review->rating ? 'fas' : 'far' ?> fa-star text-warning"></i>
                                                <?php endfor; ?>
                                                <span class="ml-1">(<?= $review->rating ?>/5)</span>
                                            </div>
                                        </td>
                                        <td>
                                            <?php if (!empty($review->review)): ?>
                                                <span class="review-text" data-toggle="tooltip" title="<?= h($review->review) ?>">
                                                    <?= h(substr($review->review, 0, 50)) ?><?= strlen($review->review) > 50 ? '...' : '' ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="text-muted"><?= __('No review text') ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge badge-<?= $review->status === 'Active' ? 'success' : ($review->status === 'Inactive' ? 'warning' : 'danger') ?>">
                                                <?= h($review->status) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <select class="form-control form-control-sm publish-status-select" data-id="<?= $review->id ?>">
                                                <option value="pending" <?= $review->publish_status === 'pending' ? 'selected' : '' ?>><?= __('Pending') ?></option>
                                                <option value="published" <?= $review->publish_status === 'published' ? 'selected' : '' ?>><?= __('Published') ?></option>
                                                <option value="rejected" <?= $review->publish_status === 'rejected' ? 'selected' : '' ?>><?= __('Rejected') ?></option>
                                            </select>
                                        </td>
                                        <td>
                                            <?= $review->created->format('Y-m-d H:i') ?>
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <?= $this->Html->link('<i class="fas fa-eye"></i>', ['action' => 'view', $review->id], [
                                                    'class' => 'btn btn-info btn-sm',
                                                    'escape' => false,
                                                    'title' => __('View')
                                                ]) ?>
                                                <?= $this->Html->link('<i class="fas fa-edit"></i>', ['action' => 'edit', $review->id], [
                                                    'class' => 'btn btn-warning btn-sm',
                                                    'escape' => false,
                                                    'title' => __('Edit')
                                                ]) ?>
                                                <?= $this->Form->postLink('<i class="fas fa-trash"></i>', ['action' => 'delete', $review->id], [
                                                    'class' => 'btn btn-danger btn-sm',
                                                    'escape' => false,
                                                    'title' => __('Delete'),
                                                    'confirm' => __('Are you sure you want to delete this review?')
                                                ]) ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="11" class="text-center"><?= __('No reviews found.') ?></td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
                <div class="card-footer clearfix">
                    <?= $this->element('Admin/pagination') ?>
                </div>
            </div>
        </div>
    </section>
</div>

<script>
$(document).ready(function() {
    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();

    // Select all checkbox functionality
    $('#select-all').change(function() {
        $('.review-checkbox').prop('checked', this.checked);
        toggleBulkButtons();
    });

    // Individual checkbox change
    $('.review-checkbox').change(function() {
        toggleBulkButtons();

        // Update select all checkbox
        var totalCheckboxes = $('.review-checkbox').length;
        var checkedCheckboxes = $('.review-checkbox:checked').length;
        $('#select-all').prop('checked', totalCheckboxes === checkedCheckboxes);
    });

    // Publish status change
    $('.publish-status-select').change(function() {
        var id = $(this).data('id');
        var status = $(this).val();
        var selectElement = $(this);

        $.ajax({
            url: '<?= $this->Url->build(['action' => 'updatePublishStatus']) ?>',
            method: 'POST',
            data: {
                id: id,
                status: status,
                _csrfToken: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    // Show success message
                    showAlert('success', response.message);

                    // Update row styling based on status
                    var row = selectElement.closest('tr');
                    row.removeClass('table-warning table-success table-danger');

                    if (status === 'published') {
                        row.addClass('table-success');
                    } else if (status === 'rejected') {
                        row.addClass('table-danger');
                    } else {
                        row.addClass('table-warning');
                    }
                } else {
                    showAlert('error', response.message);
                    // Reset select to previous value
                    selectElement.val(selectElement.data('original-value'));
                }
            },
            error: function() {
                showAlert('error', '<?= __('An error occurred while updating the status.') ?>');
                selectElement.val(selectElement.data('original-value'));
            }
        });
    });

    // Store original values for reset on error
    $('.publish-status-select').each(function() {
        $(this).data('original-value', $(this).val());
    });
});

function toggleBulkButtons() {
    var checkedCount = $('.review-checkbox:checked').length;
    $('#bulk-publish-btn, #bulk-reject-btn').prop('disabled', checkedCount === 0);
}

function bulkAction(action) {
    var checkedIds = [];
    $('.review-checkbox:checked').each(function() {
        checkedIds.push($(this).val());
    });

    if (checkedIds.length === 0) {
        showAlert('warning', '<?= __('Please select at least one review.') ?>');
        return;
    }

    var actionText = action === 'publish' ? '<?= __('publish') ?>' : '<?= __('reject') ?>';
    var confirmMessage = '<?= __('Are you sure you want to {0} {1} selected reviews?') ?>';
    confirmMessage = confirmMessage.replace('{0}', actionText).replace('{1}', checkedIds.length);

    if (confirm(confirmMessage)) {
        var form = $('<form>', {
            method: 'POST',
            action: action === 'publish' ? '<?= $this->Url->build(['action' => 'bulkPublish']) ?>' : '<?= $this->Url->build(['action' => 'bulkReject']) ?>'
        });

        // Add CSRF token
        form.append($('<input>', {
            type: 'hidden',
            name: '_csrfToken',
            value: $('meta[name="csrf-token"]').attr('content')
        }));

        // Add selected IDs
        checkedIds.forEach(function(id) {
            form.append($('<input>', {
                type: 'hidden',
                name: 'ids[]',
                value: id
            }));
        });

        $('body').append(form);
        form.submit();
    }
}

function showAlert(type, message) {
    var alertClass = 'alert-info';
    if (type === 'success') alertClass = 'alert-success';
    else if (type === 'error') alertClass = 'alert-danger';
    else if (type === 'warning') alertClass = 'alert-warning';

    var alert = $('<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">' +
        message +
        '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
        '<span aria-hidden="true">&times;</span>' +
        '</button>' +
        '</div>');

    $('.content-wrapper .container-fluid').prepend(alert);

    // Auto hide after 5 seconds
    setTimeout(function() {
        alert.fadeOut();
    }, 5000);
}
</script>
