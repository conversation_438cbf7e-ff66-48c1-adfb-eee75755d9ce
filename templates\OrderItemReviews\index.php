<?php
/**
 * @var \App\View\AppView $this
 * @var iterable<\App\Model\Entity\OrderItemReview> $reviews
 */
?>
<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/datatables.min.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/css/dataTables.bootstrap4.min.css') ?>">
<style>
    #filter-body-container {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        margin-bottom: 20px;
        border-radius: 0.375rem;
    }

    .btn.menu-toggle {
        transition: all 0.3s ease;
        background-color: #fd7e14;
        color: white;
        border: none;
    }

    .btn.menu-toggle:hover {
        background-color: #e8690b;
        color: white;
    }

    .btn.menu-toggle.active {
        background-color: #dc6545;
        color: white;
    }

    .btn.menu-toggle:focus {
        outline: none;
        box-shadow: 0 0 0 0.2rem rgba(253, 126, 20, 0.25);
    }

    .form-group label {
        font-weight: 600;
        margin-bottom: 5px;
        color: #495057;
    }
</style>
<?php $this->end(); ?>

<div class="section-header">
    <ul class="breadcrumb breadcrumb-style">
        <li class="breadcrumb-item">
            <a href="<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'index']) ?>">
                <h4 class="page-title m-b-0"><?= __("Dashboard") ?></h4>
            </a>
        </li>
        <li class="breadcrumb-item"><?= __("Reviews") ?></li>
        <li class="breadcrumb-item active"><?= __("Reviews") ?></li>
    </ul>
</div>
<div class="section-body1">
    <div class="container-fluid">
        <?= $this->Flash->render() ?>
    </div>
</div>
<div class="section-body" id="list">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <h4><?= __("Manage Reviews") ?></h4>
                <div class="card-header-form">
                    <?= $this->Form->create(null, ['type' => 'get', 'class' => 'input-group']) ?>
                        <input type="text" class="form-control search-control" placeholder="<?= __("Search") ?>" name="search" value="<?= h($search ?? '') ?>" />
                        <div class="input-group-btn">
                            <button class="btn" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                        <button class="btn menu-toggle" type="button" id="filter-toggle-btn">
                            <i class="fas fa-filter"></i>
                            <?= __("Filter") ?>
                        </button>
                    <?= $this->Form->end() ?>
                </div>
            </div>
        </div>

        <!-- Filter Container -->
        <div class="card" id="filter-body-container" style="display: none;">
            <div class="card-body">
                <?= $this->Form->create(null, ['type' => 'get', 'id' => 'filterForm']) ?>
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label><?= __('Status') ?></label>
                            <?= $this->Form->control('status', [
                                'type' => 'select',
                                'options' => [
                                    '' => __('All Status'),
                                    'Active' => __('Active'),
                                    'Inactive' => __('Inactive'),
                                    'Deleted' => __('Deleted')
                                ],
                                'id' => 'filterStatus',
                                'class' => 'form-control',
                                'label' => false,
                                'value' => $status ?? ''
                            ]) ?>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label><?= __('Publish Status') ?></label>
                            <?= $this->Form->control('publish_status', [
                                'type' => 'select',
                                'options' => [
                                    '' => __('All Publish Status'),
                                    'pending' => __('Pending'),
                                    'published' => __('Published'),
                                    'rejected' => __('Rejected')
                                ],
                                'id' => 'filterPublishStatus',
                                'class' => 'form-control',
                                'label' => false,
                                'value' => $publishStatus ?? ''
                            ]) ?>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label><?= __('Rating') ?></label>
                            <?= $this->Form->control('rating', [
                                'type' => 'select',
                                'options' => [
                                    '' => __('All Ratings'),
                                    '5' => __('5 Stars'),
                                    '4' => __('4 Stars'),
                                    '3' => __('3 Stars'),
                                    '2' => __('2 Stars'),
                                    '1' => __('1 Star')
                                ],
                                'id' => 'filterRating',
                                'class' => 'form-control',
                                'label' => false,
                                'value' => $rating ?? ''
                            ]) ?>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div class="d-flex">
                                <button type="submit" class="btn btn-primary mr-2" id="applyFilters">
                                    <i class="fa fa-filter" aria-hidden="true"></i> <?= __('Apply') ?>
                                </button>
                                <?= $this->Html->link(__('Reset'), ['action' => 'index'], [
                                    'class' => 'btn btn-secondary',
                                    'id' => 'resetFilters'
                                ]) ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?= $this->Form->end() ?>
            </div>
        </div>

        <!-- Reviews Table Card -->
        <div class="card">

            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped" id="table-1">
                        <thead>
                            <tr>
                                <th class="text-center"><?= __('ID') ?></th>
                                <th><?= __('Customer') ?></th>
                                <th><?= __('Product') ?></th>
                                <th><?= __('Order') ?></th>
                                <th><?= __('Rating') ?></th>
                                <th><?= __('Review') ?></th>
                                <th><?= __('Status') ?></th>
                                <th><?= __('Publish Status') ?></th>
                                <th><?= __('Date') ?></th>
                                <th><?= __('Action') ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $i = 1; ?>
                            <?php foreach ($reviews as $review): ?>
                                <tr>
                                    <td class="text-center"><?= $i++ ?></td>
                                    <td>
                                        <?= h($review->customer->user->first_name . ' ' . $review->customer->user->last_name) ?>
                                        <br><small class="text-muted"><?= h($review->customer->user->email) ?></small>
                                    </td>
                                    <td><?= h($review->product->name) ?></td>
                                    <td>#<?= h($review->order->order_number ?? $review->order_id) ?></td>
                                    <td>
                                        <div class="rating">
                                            <?php for ($j = 1; $j <= 5; $j++): ?>
                                                <i class="<?= $j <= $review->rating ? 'fas' : 'far' ?> fa-star text-warning"></i>
                                            <?php endfor; ?>
                                            <span class="ml-1">(<?= $review->rating ?>/5)</span>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if (!empty($review->review)): ?>
                                            <span title="<?= h($review->review) ?>">
                                                <?= h(substr($review->review, 0, 50)) ?><?= strlen($review->review) > 50 ? '...' : '' ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted"><?= __('No review text') ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge badge-<?= $review->status === 'Active' ? 'success' : ($review->status === 'Inactive' ? 'warning' : 'danger') ?>">
                                            <?= h($review->status) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <select class="form-control form-control-sm publish-status-select" data-id="<?= $review->id ?>">
                                            <option value="pending" <?= $review->publish_status === 'pending' ? 'selected' : '' ?>><?= __('Pending') ?></option>
                                            <option value="published" <?= $review->publish_status === 'published' ? 'selected' : '' ?>><?= __('Published') ?></option>
                                            <option value="rejected" <?= $review->publish_status === 'rejected' ? 'selected' : '' ?>><?= __('Rejected') ?></option>
                                        </select>
                                    </td>
                                    <td><?= $review->created->format('Y-m-d') ?></td>
                                    <td>
                                        <?= $this->Html->link('<i class="fas fa-eye"></i>', ['action' => 'view', $review->id], [
                                            'class' => 'btn btn-outline-primary btn-sm',
                                            'escape' => false,
                                            'title' => __('View')
                                        ]) ?>
                                        <?= $this->Html->link('<i class="fas fa-edit"></i>', ['action' => 'edit', $review->id], [
                                            'class' => 'btn btn-outline-warning btn-sm',
                                            'escape' => false,
                                            'title' => __('Edit')
                                        ]) ?>
                                        <?= $this->Form->postLink('<i class="fas fa-trash"></i>', ['action' => 'delete', $review->id], [
                                            'class' => 'btn btn-outline-danger btn-sm',
                                            'escape' => false,
                                            'title' => __('Delete'),
                                            'confirm' => __('Are you sure you want to delete this review?')
                                        ]) ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/datatables/datatables.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/js/dataTables.bootstrap4.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('js/filter.js') ?>"></script>
<script>
    var table = $("#table-1").DataTable({
        columnDefs: [
            { orderable: false, targets: -1 } // Make the last column non-sortable
        ],
        dom: 'rt',
        pageLength: 1000, // Show all records since we're using backend pagination
        searching: false,
        paging: false,
        info: false,
        ordering: true
    });

    // Publish status change functionality
    $('.publish-status-select').on('change', function() {
        var id = $(this).data('id');
        var status = $(this).val();
        var selectElement = $(this);
        var originalValue = selectElement.data('original-value') || selectElement.val();

        $.ajax({
            url: '<?= $this->Url->build(['action' => 'updatePublishStatus']) ?>',
            method: 'POST',
            data: {
                id: id,
                status: status,
                _csrfToken: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    // Update the original value
                    selectElement.data('original-value', status);

                    // Show success message
                    alert('<?= __('Review status updated successfully.') ?>');
                } else {
                    alert(response.message || '<?= __('Failed to update review status.') ?>');
                    // Reset to original value
                    selectElement.val(originalValue);
                }
            },
            error: function() {
                alert('<?= __('An error occurred while updating the status.') ?>');
                // Reset to original value
                selectElement.val(originalValue);
            }
        });
    });

    // Store original values for reset on error
    $('.publish-status-select').each(function() {
        $(this).data('original-value', $(this).val());
    });

    // Filter toggle functionality - Completely rewritten
    $(document).ready(function() {
        var filterContainer = $('#filter-body-container');
        var filterButton = $('#filter-toggle-btn');

        // Ensure filter is initially hidden
        filterContainer.hide();
        filterButton.removeClass('active');

        // Remove any existing handlers and add new one
        filterButton.off('click.filterToggle').on('click.filterToggle', function(e) {
            e.preventDefault();
            e.stopPropagation();

            console.log('Filter toggle clicked'); // Debug

            if (filterContainer.is(':visible')) {
                console.log('Hiding filter'); // Debug
                filterContainer.slideUp(300);
                $(this).removeClass('active');
            } else {
                console.log('Showing filter'); // Debug
                filterContainer.slideDown(300);
                $(this).addClass('active');
            }
        });

        // Prevent form submission when clicking filter toggle
        filterButton.attr('type', 'button');
    });
</script>
<?php $this->end(); ?>
