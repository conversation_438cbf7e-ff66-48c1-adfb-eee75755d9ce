<!DOCTYPE html>
<html lang="en">
<?php
        use Cake\Core\Configure;
        // Get session instance
        $session = $this->request->getSession();
        $currentLang = $session->read('siteSettings.language') ? strtolower($session->read('siteSettings.language')) : 'english';
        $country = $session->read('siteSettings.country') ? strtolower($session->read('siteSettings.country')) : 'Qatar';
?>


<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <?= $this->Html->meta('csrfToken', $this->request->getAttribute('csrfToken')); ?>
    <title><?= $this->fetch('title') ? $this->fetch('title') . ' - ' . $session->read('siteSettings.site_title') : $session->read('siteSettings.site_title'); ?></title>

    <!-- Global JavaScript Variables -->
    <script>
        window.csrfToken = '<?= $this->request->getAttribute('csrfToken') ?>';
    </script>

    <!-- Meta Tags -->
    <?php if ($this->fetch('meta_description')): ?>
        <meta name="description" content="<?= h($this->fetch('meta_description')) ?>">
    <?php endif; ?>

    <?php if ($this->fetch('meta_keywords')): ?>
        <meta name="keywords" content="<?= h($this->fetch('meta_keywords')) ?>">
    <?php endif; ?>

    <?php if (isset($meta_title) && !empty($meta_title)): ?>
        <meta property="og:title" content="<?= h($meta_title) ?>">
    <?php endif; ?>

    <?php if (isset($meta_description) && !empty($meta_description)): ?>
        <meta property="og:description" content="<?= h($meta_description) ?>">
    <?php endif; ?>

    <link rel="stylesheet" href="../../bundles/bootstrap/css/bootstrap.min.css" />
    <link rel="stylesheet" href="../../bundles/bootstrap/css/bootstrap.css" />
    <link rel="stylesheet" href="../../css/ozone.css" />
    <link rel="stylesheet" href="../../css/responsive.css" />
    <!-- For SVG format -->
    <link rel="icon" type="image/svg+xml" href="../../img/ozone/Ozonex-svg.svg">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Owl Stylesheets -->
    <link rel="stylesheet" href="../../css/owl.carousel.min.css">
    <link rel="stylesheet" href="../../css/owl.theme.default.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css" />

    <link href="https://cdn.jsdelivr.net/npm/nouislider@15.7.0/dist/nouislider.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flag-icons@6.7.0/css/flag-icons.min.css">
    <script src="https://ipapi.co/json/"></script>


    <!-- jQuery and jQuery Validation Plugin -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/jquery.validation/1.16.0/jquery.validate.min.js"></script>
    
    <!-- Custom CSS -->
    <style>
        .footer-link {
    color: #243F13; /* Adjust to your brand's dark green */
    font-size: 14px;
    font-weight:600;
    text-decoration: underline;
}

.footer-link:hover {
     color: #003300;
    text-decoration: none;
    font-size: 14px;
}

.footer-links {
    display: flex;
    flex-wrap: wrap;
}

.footer-links a {
    /* margin-right: 15px; */
    margin-bottom: 5px;
}
.footer-links span {
    color: #243F13;
    font-size: 14px;
    font-weight: 600;
    margin: 0px 10px;
}
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        #toast-message-container .alert {
            transition: all 0.3s ease;
        }
    .text-green-dark {
        color: #30583A !important;
    }
    .text-green-medium {
        color: #3C9252 !important;
    }
    .bg-green-light {
        background-color: #c9f1d2 !important;
    }
    .icon-green {
        color: #6FC284 !important;
    }
</style>
    </style>
    <script src="../../js/jquery.min.js"></script>
    <script src="../../js/owl.carousel.js"></script>

<style>
span.show-password-icon {
    position: absolute;
    top: 54%;
    right: 8%;
    color:black;
}
span.show-password-icon-register {
    position: absolute;
    top: 54%;
    right: 8%;
    color:black;
}

</style>
</head>
</head>

<body class="">
    <!-- Navigation -->
    <div class="md-language d-lg-none">
        <div class="container">
            <div class="d-flex justify-content-end align-items-center navbar-icons">

                    <div class="dropdown">
                        <button id="dropdownregion" class="btn dropdown-toggle" type="button" data-bs-toggle="dropdown"
                          aria-expanded="false">
                          <?php
                            // Show country name in Arabic if Saudi Arabia
                            if (strtolower(trim($currentLang)) == 'arabic') {
                                if (strtolower(trim($country)) === 'saudi arabia') {
                                    $countryName = 'المملكة العربية السعودية';
                                } else {
                                    $countryName = 'قطر';
                                }
                            } else {
                                $countryName = ucwords($country);
                            }
                            if (strtolower(trim($countryName)) === 'saudi arabia' || strtolower(trim($countryName)) === 'المملكة العربية السعودية') {
                                $flagClass = 'fi fi-sa';
                            } else {
                                $flagClass = 'fi fi-qa';
                            }
                        ?>
                          <span class="fi <?= $flagClass ?> flag md:my-4"></span> <?= ucwords($countryName) ?>
                        </button>
                        <ul class="dropdown-menu">
                          <li>
                            <a class="dropdown-item" href="#" onclick="selectDropdownItemWithCartCheck(this, '<?= $currentLang ?>', 'Qatar')">
                            <span class="fi fi-qa flag " alt="Qatar" ></span> <span class="dropdown-country-name"><?= strtolower($currentLang) === 'english' ? 'Qatar' : 'قطر'; ?></span>
                              <!-- <span>Qatar</span> -->
                            </a>
                          </li>
                          <li>
                            <a class="dropdown-item" href="#" onclick="selectDropdownItemWithCartCheck(this, '<?= $currentLang ?>', 'Saudi Arabia')">
                            <span class="fi fi-sa flag " alt="Saudi Arabia" ></span> <span class="dropdown-country-name"><?= strtolower($currentLang) === 'english' ? 'Saudi Arabia' : 'المملكة العربية السعودية'; ?></span>
                              <!-- <span>Saudi Arabia</span> -->
                            </a>
                          </li>
                        </ul>
                      </div>
                      <span class="right-bord"></span>


                    <div class="dropdown">


                        <button id="dropdownButton" class="btn " type="button" onclick="selectDropdownItem(this, '<?= $currentLang === 'arabic' ? 'Eng' : 'Arb' ?>', '<?= $country ?>')">
                        <?= strtolower($currentLang) === 'english' ? 'عربي' : 'English'; ?>
                        </button>


                    </div>


                </div>
        </div>
    </div>
    <nav class="navbar-head-main navbar navbar-expand-lg navbar-light bg-white py-3 shadow-sm">
        <div class="container">
            <a class="navbar-brand" href="/">
                <img src="../../img/ozone/logo.png" class="img-fluid logo-head">
            </a>
            
            <?php
                $session = $this->request->getSession();
                $authUser = $session->read('Auth.User');
            ?>
            <?php if($authUser): ?>
                <a href="<?= $this->Url->build(['controller' => 'Account', 'action' => 'myAccount']) ?>" class="btn d-md-none"><img src="../../img/ozone/user-logged-in.png" class="img-fluid" /></a>
            <?php else: ?>
                <button class="btn d-md-none head-icons-md" data-bs-target="#loginmodal" data-bs-toggle="modal">
                            <img src="../../img/ozone/Account.png" class="img-fluid " />
                </button>
            <?php endif; ?>

            <span class="right-bord d-md-none"></span>

            <a href="/cart" class="text-dark mx-3 position-relative d-md-none head-icons-md">
                <img src="../../img/ozone/Shopping.png" class="img-fluid " />
                <?= $this->cell('Cart::display', ['mobile']) ?>
            </a>
            <span class="right-bord d-md-none"></span>
            <a href="/whishlist" class="text-dark  mx-2  d-md-none head-icons-md">
                        <img src="../../img/ozone/heart.png" class="img-fluid" />
                    </a>


            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <?php
                    // Use CakePHP's URL helper for clean URLs
                    $currentUrl = $this->request->getRequestTarget();
                ?>
                <ul class="navbar-nav m-auto">
                    <li class="nav-item">
                        <a class="nav-link <?= $currentUrl === '/' ? 'active' : '' ?>" href="<?= $this->Url->build('/') ?>"><?= __('Home') ?></a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= strpos($currentUrl, '/product-list') === 0 ? 'active' : '' ?>" href="<?= $this->Url->build('/product-list') ?>"><?= __('Shop All') ?></a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= strpos($currentUrl, '/about-us') === 0 ? 'active' : '' ?>" href="<?= $this->Url->build('/about-us') ?>"><?= __('About Us') ?></a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= strpos($currentUrl, '/ozonex') === 0 ? 'active' : '' ?>" href="<?= $this->Url->build('/product-list?brands=5') ?>"><?= __('OzoneX') ?></a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= strpos($currentUrl, '/contact-us') === 0 ? 'active' : '' ?>" href="<?= $this->Url->build('/contact-us') ?>"><?= __('Contact Us') ?></a>
                    </li>
                </ul>
                <div class="d-flex align-items-center navbar-icons">


                    <div class="dropdown">

                        <button id="dropdownregion" class="btn dropdown-toggle d-none d-lg-block" type="button" data-bs-toggle="dropdown"
                          aria-expanded="false">
                        <?php
                            // Show country name in Arabic if Saudi Arabia
                            if (strtolower(trim($currentLang)) == 'arabic') {
                                if (strtolower(trim($country)) === 'saudi arabia') {
                                    $countryName = 'المملكة العربية السعودية';
                                } else {
                                    $countryName = 'قطر';
                                }
                            } else {
                                $countryName = ucwords($country);
                            }
                            if (strtolower(trim($countryName)) === 'saudi arabia' || strtolower(trim($countryName)) === 'المملكة العربية السعودية') {
                                $flagClass = 'fi fi-sa';
                            } else {
                                $flagClass = 'fi fi-qa';
                            }
                        ?>
                        <span class="<?= $flagClass ?> flag me-2 my-4"></span> <?= $countryName ?>
                        </button>
                        <ul class="dropdown-menu">
                          <li>
                            <a class="dropdown-item" href="#" onclick="selectDropdownItemWithCartCheck(this, '<?= $currentLang ?>', 'Qatar')">
                            <span class="fi fi-qa flag me-2" alt="Qatar" ></span> <span class="dropdown-country-name">
                                <?= strtolower($currentLang) === 'english' ? 'Qatar' : 'قطر'; ?>
                            </span>
                            </a>
                          </li>
                          <li>
                            <a class="dropdown-item" href="#" onclick="selectDropdownItemWithCartCheck(this, '<?= $currentLang ?>', 'Saudi Arabia')">
                            <span class="fi fi-sa flag me-2" alt="Saudi Arabia" ></span> <span class="dropdown-country-name">
                                <?= strtolower($currentLang) === 'english' ? 'Saudi Arabia' : 'المملكة العربية السعودية'; ?>
                            </span>
                            </a>
                          </li>
                        </ul>
                      </div>
                      <span class="right-bord  d-none d-lg-block"></span>

                    <div class="dropdown d-none d-lg-block">


                        <button id="dropdownButton" class="btn " type="button" onclick="selectDropdownItem(this, '<?= $currentLang === 'arabic' ? 'Eng' : 'Arb' ?>', '<?= $country ?>')">
                        <?= strtolower($currentLang) === 'english' ? 'عربي' : 'English'; ?>
                        </button>


                    </div>
                    <span class="right-bord d-none d-lg-block"></span>

                    <div class="dropdown d-none d-lg-block">
                        <?php if($authUser): ?>
                        <a class="btn dropdown-toggle" type="button" id="hoverDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <img src="../../img/ozone/user-logged-in.png" class="img-fluid" />
                        </a>
                        <?php if($currentUrl !== '/account/my-account'): ?>
                        <ul class="dropdown-menu" aria-labelledby="hoverDropdown">
                            <li><a class="dropdown-item" href="<?= $this->Url->build(['controller' => 'Account', 'action' => 'myAccount']) ?>"><?= __('My Profile') ?></a></li>
                            <li><a class="dropdown-item" href="<?= $this->Url->build(['controller' => 'Account', 'action' => 'myAccount', '#' => 'billing-tab']) ?>"><?= __('My Address') ?></a></li>
                            <li><a class="dropdown-item" href="<?= $this->Url->build(['controller' => 'Account', 'action' => 'myAccount', '#' => 'orders-tab']) ?>"><?= __('My Orders') ?></a></li>
                            <li><a class="dropdown-item" href="<?= $this->Url->build(['controller' => 'Account', 'action' => 'myAccount', '#' => 'wishlist-tab']) ?>"><?= __('My Wishlist') ?></a></li>
                            <li><a class="dropdown-item" href="<?= $this->Url->build(['controller' => 'Account', 'action' => 'myAccount', '#' => 'changePassword']) ?>"><?= __('Change Password') ?></a></li>
                            <li><a class="dropdown-item" href="<?= $this->Url->build(['controller' => 'Customer', 'action' => 'logout']) ?>"><?= __('Logout') ?></a></li>
                        </ul>
                        <?php endif; ?>
                        <?php else: ?>
                        <a class="btn dropdown-toggle" type="button" id="hoverDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <img src="../../img/ozone/Account.png" class="img-fluid" />
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="hoverDropdown">
                            <li><a class="dropdown-item" data-bs-target="#loginmodal" data-bs-toggle="modal" style="cursor:pointer"><?= __('Login') ?></a></li>
                            <li><a class="dropdown-item" data-bs-target="#loginmodal2" data-bs-toggle="modal" style="cursor:pointer"><?= __('Register') ?></a></li>
                        </ul>
                        <?php endif; ?>
                    </div>

                     <span class="right-bord d-none d-lg-block "></span>
                    <a href="/whishlist" class="text-dark  mx-2 d-none d-lg-block">
                        <img src="../../img/ozone/heart.png" class="img-fluid" />
                    </a>

                    <span class="right-bord  d-none d-lg-block"></span>

                    <a href="/cart" class="text-dark mx-2 position-relative d-none d-lg-block">

                        <img src="../../img/ozone/Shopping.png" class="img-fluid" />
                                 <?= $this->cell('Cart::display', ['desktop']) ?>
                    </a>
                    <span class="right-bord  d-none d-lg-block"></span>

                    <span class="right-bord  d-none d-lg-block"></span>
                    <!-- <a href="#" class="text-dark  mx-2 d-none d-lg-block" id="product-search-trigger">
                        <img src="../../img/ozone/Search.png" class="img-fluid" />
                    </a> -->
                </div>
            </div>
            <!-- Mobile Search Input (shown only on small screens) -->
            <div class="mobile-searchfield">
                <div class="input-group w-full">
                    <a href="#" class="text-dark  mx-2" id="product-search-trigger">
                        <img src="../../img/ozone/Search.png" class="img-fluid" />
                         <input type="text" class="form-control border-0 d-block d-lg-none" placeholder="Search for Ozone Products" />
                    </a>
                </div>
            </div>

        </div>
    </nav>

    <!-- Flash Messages -->
    <div class="container">
        <?= $this->Flash->render() ?>
    </div>

    <!-- Toast Message Container -->
    <div id="toast-message-container" style="position: fixed; top: 80px; right: 20px; z-index: 9999; max-width: 350px;"></div>


    <?= $this->fetch('content') ?>


    <!-- Footer -->
    <footer class="footer-banner ">
        <div class="container">
            <div class="row">
                <div class="col-lg-3 md-mb-4">
                    <a class="py-5" href="#">
                        <img src="../../img/ozone/logo.png" class="img-fluid">
                    </a>
                    <p class="my-5">
                        <?= __('Ozone Cool Qatar Authorized Distributor for Fujitsu General Air Conditioner for Residential, Commercial and Industrial Applications') ?>
                    </p>
                    <div class="social-icons my-3">
                        <!-- <a href="<?= h($this->Url->build($session->read('siteSettings.facebook_url'), ['fullBase' => true])) ?>"><i class="fab fa-facebook-f fs-3"></i></a>
                        <a href="<?= h($this->Url->build($session->read('siteSettings.twitter_url'), ['fullBase' => true])) ?>"><i class="fab fa-twitter fs-3"></i></a>
                        <a href="<?= h($this->Url->build($session->read('siteSettings.instagram_url'), ['fullBase' => true])) ?>"><i class="fab fa-instagram fs-3"></i></a>
                        <a href="<?= h($this->Url->build($session->read('siteSettings.linkedin_url'), ['fullBase' => true])) ?>"><i class="fab fa-linkedin-in fs-3"></i></a> -->

                        <a href="<?= h($this->Url->build($session->read('siteSettings.facebook_url'), ['fullBase' => true])) ?>"><img src="../../img/ozone/Facebook-footer.png" class="img-fluid"/></a>
                        <a href="<?= h($this->Url->build($session->read('siteSettings.twitter_url'), ['fullBase' => true])) ?>"><img src="../../img/ozone/Twitter.png" class="img-fluid"/></a>
                        <a href="<?= h($this->Url->build($session->read('siteSettings.instagram_url'), ['fullBase' => true])) ?>"><img src="../../img/ozone/Instagram.png" class="img-fluid"/></a>
                        <a href="<?= h($this->Url->build($session->read('siteSettings.linkedin_url'), ['fullBase' => true])) ?>"><img src="../../img/ozone/LinkedIn.png" class="img-fluid"/></a>
                        <a href="<?= h($this->Url->build($session->read('siteSettings.linkedin_url'), ['fullBase' => true])) ?>"><img src="../../img/ozone/YouTube.png" class="img-fluid"/></a>
                    </div>
                </div>
                <div class="col-lg-9">
                    <div class="row footer-content">
                        <div class="col-lg-3 mb-md-4  d-none d-lg-block">
                            <h5 class="mt-3"><?= __('Website Link') ?></h5>
                            <ul class="mt-5">
                                <li><a href="<?= $this->Url->build('/') ?>"><?= __('Home') ?></a></li>
                                <li><a href="<?= $this->Url->build('/product-list') ?>"><?= __('Shop All') ?></a></li>
                                <li><a href="<?= $this->Url->build('/about-us') ?>"><?= __('About Us') ?></a></li>
                                <li><a href="<?= $this->Url->build('/') ?>"><?= __('OzoneX') ?></a></li>
                                <li><a href="<?= $this->Url->build('/contact-us') ?>"><?= __('Contact Us') ?></a></li>
                            </ul>
                        </div>
                        <div class="col-lg-3 mb-md-4  d-none d-lg-block">
                            <h5 class="mt-3"><?= __('Working Hours') ?></h5>
                            <ul class="mt-5">
                                <li class="week"><?= __('Sat - Thu:') ?></li>
                                <li><a href="#"><?= $session->read('siteSettings.business_open_time') ?> - <?= $session->read('siteSettings.business_close_time') ?></a></li>

                            </ul>
                        </div>
                        <div class="col-lg-3 mb-md-4">
                            <h5 class="mt-3"><?= __('Contact Us') ?></h5>
                            <ul class="mt-md-5 icons">
                                <li><i class="fas fa-envelope me-2"></i><span><?= $session->read('siteSettings.support_email') ?></span></li>
                                <li><i class="fas fa-phone-alt me-2"></i><span><?= $session->read('siteSettings.contact_no') ?></span>
                                </li>
                                <li><i class="fas fa-map-marker-alt me-2"></i><span>
                                    <?= $session->read('siteSettings.address_line1') ?>, <?= $session->read('siteSettings.address_line2') ?>, <?= $session->read('siteSettings.city') ?>, <?= $session->read('siteSettings.state') ?> <?= $session->read('siteSettings.zipcode') ?>
                                </span></li>
                            </ul>
                        </div>
                        <div class="col-lg-3 mb-md-4">
                        <div class="col-lg-3 mb-md-4 d-none d-lg-block">
                            <h5 class="mt-3"><?= __('Language') ?></h5>
                            <ul class="mt-md-5 icons language-footer">
                                <li class="<?= strtolower($currentLang) === 'english' ? 'active' : '' ?>">
                                    <span style="cursor: pointer;" onclick="selectDropdownItem(this, '<?= $currentLang === 'arabic' ? 'Eng' : 'Arb' ?>', '<?= $country ?>')">
                                        <span><?= __('English') ?></span>
                                    </span>
                                </li>
                                <li class="<?= strtolower($currentLang) === 'arabic' ? 'active' : '' ?>">
                                    <span style="cursor: pointer;" href="" onclick="selectDropdownItem(this, '<?= $currentLang === 'arabic' ? 'Eng' : 'Arb' ?>', '<?= $country ?>')">
                                        <span><?= __('عربي') ?></span>
                                    </span>
                                </li>
                            </ul>
                        </div>
                        <div class="col-lg-3 mb-md-4   d-lg-none">
                            <h5 class="mt-3"><?= __('Working Hours') ?></h5>
                            <ul class="mt-md-5">
                                <li class="week"><?= __('Sat - Thu:') ?></li>
                                <li><a href="#"><?= $session->read('siteSettings.business_open_time') ?> - <?= $session->read('siteSettings.business_close_time') ?></a></li>

                            </ul>
                        </div>
                    </div>
                    <!-- <hr class="my-md-4 bg-secondary">
                    <div class="row">
                        <div class="col-md-12 text-end w-100 copy-rights">
                            <p class="mb-0 text-right">
                                <?= __('Copyright') ?> © <?= date('Y') ?> <?= $session->read('siteSettings.site_title') ?> Cool
                                &nbsp;|&nbsp;
                                <a href="/privacy-policy" target="_blank"><?= __('Privacy Policy') ?></a>
                                &nbsp;|&nbsp;
                                <a href="/terms-and-conditions" target="_blank"><?= __('Terms & Conditions') ?></a>
                            </p>
                        </div>

                    </div> -->
                    <hr class="my-md-4 bg-secondary">
                    

                </div>
                
            </div>
                    </div>
               <div class="row">
                    <div class="col-sm-12 col-md-8 mb-2">
                        <div class="footer-links mb-2 mb-md-0">
                            <a href="/privacy-policies" class="footer-link"><?= __('Privacy Policy') ?></a><span>|</span>
                            <a href="/terms-conditions" class="footer-link"><?= __('Terms & Conditions') ?></a><span>|</span>
                            <a href="/refund-policies" class="footer-link"><?= __('Refund Policy') ?></a>
                        </div>
                    </div>
                    <div class="col-sm-12 col-md-4 text-muted text-lg-end small">
                        <?= __('Copyright') ?> © <?= date('Y') ?> <?= $session->read('siteSettings.site_title') ?>
                    </div>
                </div> 
        </div>
    </footer>

    <!--Login Modal -->
    <div class="modal fade " id="loginmodal" aria-hidden="true" aria-labelledby="loginmodalLabel"
        tabindex="-1">
        <div class="modal-dialog modal-fullscreen modal-dialog-right">
            <div class="modal-content login">
                <div class="login-container">
                    <div class="position-relative">
                        <div class="container">
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><img
                                    src="../../img/ozone/close-circle.png" width="40" height="40" /></button>
                            <div class="form-login my-5">
                                <div class="logo">
                                    <img src="../../img/ozone/logo.png" class="img-fluid">
                                    <span style="font-weight: 600;"><?= __('Log in') ?></span>
                                </div>
                                <!-- Social Icons -->
                                <div class="social-icons my-5">
                                    <a href="<?= $this->Url->build(['controller' => 'Customer', 'action' => 'googleLogin']) ?>"><button><img src="https://img.icons8.com/color/24/google-logo.png" /><span> <?= __('Sign in with Google') ?></span></button></a>
                                    <button><img src="https://img.icons8.com/ios-filled/24/facebook-new.png" /><span>
                                            <?= __('Sign in with Facebook') ?></span></button>
                                    <!-- <button><img src="https://img.icons8.com/ios-filled/24/mac-os.png" /><span><?= __('Sign in with Apple') ?></span></button> -->
                                </div>

                                <!-- Divider -->
                                <div class="divider"><?= __('OR') ?></div>

                                <!-- Email Field -->
                                <form id="login-form">
                                <div class="input-group email mb-4">
                                    <label><?= __('Enter your email address') ?></label>
                                    <input type="hidden" id="csrf-token-login" value="<?= h($this->request->getAttribute('csrfToken')) ?>">
                                    <input type="email" placeholder="<?= __('Enter your email address') ?>" name="email" class="text" required="" />
                                </div>
                                <div class="text-center mb-3" id="emailError" style="color:orange;display:none"></div>
                                <!-- Password Field -->
                                <div class="input-group">
                                    <label><?= __('Enter your Password') ?></label>
                                    <input type="password" name="password" placeholder="<?= __('Enter your Password') ?>" class="text password-field" required=""/>
                                    <span class="show-password-icon" onclick="togglePasswordVisibility()"><i class="fa fa-eye"></i></span>
                                </div>

                                <div class="forgot-password text-center">
                                    <a href="<?= $this->Url->build(['controller' => 'Customer', 'action' => 'forgotPassword','type' => 'post']) ?>" class="forgot_pwd"><span><?= __('Forgot password?') ?></span></a>
                                </div>

                                <!-- Terms -->
                                <div class="form-check mt-3">
                                    <label class="form-check-label terms-condition" for="terms">
                                        <?= __('By signing up you agree to our ') ?><a href="/terms-conditions"><?= __('Terms & Conditions') ?></a></span>
                                    </label>
                                </div>

                                <!-- Login Button -->
                                <button id="submit-login-form" class="btn-glow btn-login my-5"><?= __('Log In') ?></button>
                                </form>
                                <!-- Register Button -->
                                <button class="btn-glow btn-register" id="btn-register" data-bs-target="#loginmodal2"
                                    data-bs-toggle="modal" data-bs-dismiss="modal" aria-label="Close"><?= __('Register here!') ?></button>

                                <!-- Footer -->
                                <div class="footer-text">
                                    <?= __("If you don't have an account, you can register here.") ?>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!--Register Modal -->
    <div class="modal fade" id="loginmodal2" aria-hidden="true" aria-labelledby="loginmodalLabel2"
        tabindex="-1">
        <div class="modal-dialog modal-fullscreen modal-dialog-right">
            <div class="modal-content login">

                <div class="login-container">
                    <div class="position-relative">
                        <div class="container">
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><img
                                    src="../../img/ozone/close-circle.png" width="40" height="40" /></button>
                            <div class="form-login my-5">
                                <div class="logo">
                                    <img src="../../img/ozone/logo.png" class="img-fluid">
                                    <span style="font-weight: 600;"><?= __('Register') ?></span>
                                </div>
                                <!-- Social Icons -->
                                <div class="social-icons my-5">
                                    <a href="<?= $this->Url->build(['controller' => 'Customer', 'action' => 'googleLogin']) ?>"><button><img src="https://img.icons8.com/color/24/google-logo.png" /><span> <?= __('Sign in with Google') ?></span></button></a>
                                    <button><img src="https://img.icons8.com/ios-filled/24/facebook-new.png" /><span>
                                            <?= __('Sign in with Facebook') ?></span></button>
                                    <!-- <button><img src="https://img.icons8.com/ios-filled/24/mac-os.png" /><span><?= __('Sign in with Apple') ?></span></button> -->
                                </div>

                            <!-- Divider -->
                            <div class="divider"><?= __('OR') ?></div>
                            <form id="register-form">
                                <label><?= __('Enter your email address') ?></label>
                                <div class="d-flex my-4 ">
                                    <!-- Email Field -->
                                    <div class="input-group email ">
                                        <input type="email" id="email" name="email" placeholder="<?= __('Username or email address') ?>" required/>
                                    </div>
                                    <button class="verify-btn ms-2" id="send_code_btn">
                                        <span class="btn-text"><?= __('Send Code') ?></span>
                                        <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
                                    </button>
                                </div>
                                <div class="text-center mb-3" id="emailErrorRegister" style="color:orange;display:none"></div>
                                <div class="d-flex align-items-center mb-3">
                                    <!-- Email Field -->
                                    <div class="input-group email ">
                                        <input type="text" id="verification_code" placeholder="<?= __('Verification Code') ?>" />
                                        <span class="timer-inside" id="countdown">05:00</span>
                                    </div>
                                    <button class="verify-btn" id="verify-btn"><?= __('Verify Email') ?></button>
                                </div>
                                
                                <div class="forgot-password text-center">
                                    <a style="text-decoration:underline;" onclick="openLoginModal()"><span><?= __('Already have an account? Login') ?></span></a>
                                </div>
                                
                                <div class="error-text mb-5"></div>
                                <!-- Email -->

                                <div id="show_registration_form" style="display:none;">
                                    <!-- Full Name -->
                                    <div class="my-4 input-group ">
                                        <label class="form-label"><?= __('Full Name') ?></label>
                                        <input type="text" name="name" id="name" class="form-control rounded-pill"
                                            placeholder="<?= __('Enter your full name') ?>" required/>
                                    </div>
                                    <!-- Password -->
                                    <div class="mb-4 input-group ">
                                        <label class="form-label"><?= __('Password') ?></label>
                                        <input type="password" name="password" id="password" placeholder="<?= __('Enter Password') ?>" class="text rounded-pill" required=""/>
                                        <span class="show-password-icon" onclick="togglePasswordVisibilityRegister()"><i class="fa fa-eye"></i></span>
                                    </div>
                                    <div class="text-center mb-3" id="passwordError" style="color:orange;display:none"></div>
                                    <!-- Confirm Password -->
                                    <div class="mb-4 input-group ">
                                        <label class="form-label"><?= __('Confirm Password') ?></label>
                                        <input type="password" name="confirm_password" id="confirm_password" placeholder="<?= __('Confirm Password') ?>" class="text rounded-pill" required=""/>
                                        <span class="show-password-icon" onclick="toggleConfirmPasswordVisibilityRegister()"><i class="fa fa-eye"></i></span>
                                    </div>
                                    <div class="text-center mb-3 password_error" style="color:orange;display:none"><?= __('Confirm Password must be same as Password') ?></div>
                                    <!-- Terms -->
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="terms" required/>
                                        <label class="form-check-label terms-condition" for="terms">
                                            <?= __('By signing up you agree to our ') ?><a href="/terms-conditions"><?= __('Terms & Conditions') ?></a></span>
                                        </label>
                                    </div>
                                    <!-- Register Button -->
                                    <button id="submit-register-form" class="btn-glow btn-login my-5"><?= __('Register') ?></button>
                                </div>
                            </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!--Remove Coupon Modal -->
    <div class="modal fade" id="removeCouponModal" tabindex="-1" aria-labelledby="removeCouponLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="removeCouponLabel"><?= __('Remove Coupon') ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <?= __('Are you sure you want to remove this coupon?') ?>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?= __('Cancel') ?></button>
                <button type="button" class="btn btn-danger" id="confirmRemoveCoupon"><?= __('Yes, Remove') ?></button>
            </div>
            </div>
        </div>
    </div>

<!-- Country Change Confirmation Modal -->
<div class="modal fade" id="countryChangeModal" tabindex="-1" aria-labelledby="countryChangeModalLabel" aria-hidden="true"> 
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header border-0 pb-0">
                <h5 class="modal-title fw-bold text-green-medium" id="countryChangeModalLabel">
                    <i class="fas fa-exclamation-triangle me-2 icon-green"></i>
                    <?= __('Change Country?') ?>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body py-4">
                <div class="text-center mb-3">
                    <i class="fas fa-shopping-cart icon-green" style="font-size: 3rem;"></i>
                </div>
                <div class="alert border-0 bg-green-light">
                    <h6 class="fw-bold mb-2 text-green-dark">
                        <i class="fas fa-info-circle me-1 icon-green"></i>
                        <?= __('Important Notice') ?>
                    </h6>
                    <p class="mb-2 text-green-dark" id="countryChangeMessage">
                        <?= __('You have items in your cart. Changing the country will:') ?>
                    </p>
                    <ul class="mb-0 ps-3 text-green-dark">
                        <li id="cartWarning"><?= __('Clear all items from your cart') ?></li>
                        <li id="wishlistWarning"><?= __('Clear all items from your wishlist') ?></li>
                        <li id="couponWarning" style="display: none;"><?= __('Remove any applied coupons') ?></li>
                    </ul>
                </div>
                <p class="text-muted mb-0">
                    <?= __('Are you sure you want to continue?') ?>
                </p>
            </div>
            <div class="modal-footer border-0 pt-0">
                <button type="button" class="btn btn-secondary px-4" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>
                    <?= __('Cancel') ?>
                </button>
                <button type="button" class="btn px-4" style="background-color: #3C9252; color: white;" id="confirmCountryChange">
                    <i class="fas fa-check me-1"></i>
                    <?= __('Yes, Change Country') ?>
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        $('#login-form').find('#email').on('keyup', function () {
            var email = $(this).val().trim();
            var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

            if (email === '') {
                $('#emailError').text('Email is required.').show();
                $('.verify-btn').prop('disabled',true);
                $('#send_code_btn').prop('disabled',true);
                $('.btn-login').prop('disabled',true);
            } else if (!emailRegex.test(email)) {
                $('#emailError').text('Please enter a valid email address.').show();
                $('.verify-btn').prop('disabled',true);
                $('#send_code_btn').prop('disabled',true);
                $('.btn-login').prop('disabled',true);
            } else {
                $('#emailError').text('').show();
                $('.verify-btn').prop('disabled',false);
                $('#send_code_btn').prop('disabled',false);
                $('.btn-login').prop('disabled',false);
            }
        });
        $('#register-form').find('#email').on('keyup', function () {
            var email = $(this).val().trim();
            var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

            if (email === '') {
                $('#emailErrorRegister').text('Email is required.').show();
                $('.verify-btn').prop('disabled',true);
                $('#send_code_btn').prop('disabled',true);
                $('.btn-login').prop('disabled',true);
            } else if (!emailRegex.test(email)) {
                $('#emailErrorRegister').text('Please enter a valid email address.').show();
                $('.verify-btn').prop('disabled',true);
                $('#send_code_btn').prop('disabled',true);
                $('.btn-login').prop('disabled',true);
            } else {
                $('#emailErrorRegister').text('').show();
                $('.verify-btn').prop('disabled',false);
                $('#send_code_btn').prop('disabled',false);
                $('.btn-login').prop('disabled',false);
            }
        });

        $('#loginmodal').on('hidden.bs.modal', function () {
            $(this).find('form')[0].reset();
        });
        $('#loginmodal2').on('hidden.bs.modal', function () {
            $(this).find('form')[0].reset();
        });
    });

    function openLoginModal(){
        $('#loginmodal2').modal('hide');
        $('#loginmodal').modal('show');
    }

    function togglePasswordVisibility() {
        const passwordField = document.querySelector('.password-field');
        const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordField.setAttribute('type', type);
    }

    function togglePasswordVisibilityRegister() {
        const password = document.getElementById('password');
        const type = password.getAttribute('type') === 'password' ? 'text' : 'password';
        password.setAttribute('type', type);
    }

    function toggleConfirmPasswordVisibilityRegister() {
        const confirmPassword = document.getElementById('confirm_password');
        const type = confirmPassword.getAttribute('type') === 'password' ? 'text' : 'password';
        confirmPassword.setAttribute('type', type);
    }

    function setLanguageDirection(lang) {
        const body = document.body;
        if (lang === 'arabic') {
            body.classList.add('rtl');
            body.classList.remove('ltr');
            document.documentElement.setAttribute('dir', 'rtl');
            document.documentElement.setAttribute('lang', 'ar');
        } else {
            body.classList.remove('rtl');
            body.classList.add('ltr');
            document.documentElement.setAttribute('dir', 'ltr');
            document.documentElement.setAttribute('lang', 'en');
        }
    }

    // Call this on page load using PHP language
    document.addEventListener('DOMContentLoaded', function () {
        const currentLang = '<?= strtolower($currentLang) ?>';
        setLanguageDirection(currentLang);

        // Initialize cart count
        if (window.CartUpdater && typeof window.CartUpdater.refreshCartCount === 'function') {
            window.CartUpdater.refreshCartCount();
        }
    });


    function selectDropdownItem() {
        const langBtn = document.getElementById('languageToggleBtn');
        const currentText = langBtn.textContent.trim();
        const newLang = currentText === 'عربي' ? 'arabic' : 'english';

        // Update direction
        setLanguageDirection(newLang);
        // Update direction
        setLanguageDirection(newLang);

        // Toggle button text
        langBtn.textContent = newLang === 'arabic' ? 'English' : 'عربي';
    }

    // Global variables to store pending country change
    let pendingCountryChange = null;
    let pendingLanguageChange = null;

    /**
     * Enhanced selectDropdownItem function with cart checking
     */
    function selectDropdownItemWithCartCheck(element, lang, country) {
        // Get current country from PHP session
        const currentCountry = '<?= $country ?>';

        // Check if the selected country is the same as current country
        if (country.toLowerCase() === currentCountry.toLowerCase()) {
            // Same country selected, no need to show modal or change anything
            console.log('Same country selected, no action needed');
            return;
        }

        // Store the pending changes
        pendingCountryChange = country;
        pendingLanguageChange = lang;

        // Check if cart has items before proceeding
        checkCartBeforeCountryChange(country, lang);
    }

    /**
     * Check if cart has items before changing country
     */
    function checkCartBeforeCountryChange(newCountry, newLang) {
        // Get CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                         document.querySelector('meta[name="csrfToken"]')?.getAttribute('content') ||
                         document.querySelector('input[name="_csrfToken"]')?.value;

        $.ajax({
            url: '/Cart/checkCartBeforeCountryChange',
            method: 'POST',
            headers: {
                'X-CSRF-Token': csrfToken
            },
            success: function(response) {
                console.log(response);

                if (response.success && response.has_items) {
                    // Cart or wishlist has items, show confirmation modal
                    showCountryChangeModal(response.has_coupon, response.has_cart_items, response.has_wishlist_items);
                } else {
                    // Cart and wishlist are empty, proceed with country change
                    proceedWithCountryChange(newCountry, newLang);
                }
            },
            error: function(xhr, status, error) {
                console.error('Error checking cart:', error);
                // On error, proceed with country change anyway
                // proceedWithCountryChange(newCountry, newLang);
            }
        });
    }

    /**
     * Show country change confirmation modal
     */
    function showCountryChangeModal(hasCoupon, hasCartItems, hasWishlistItems) {
        // Show/hide coupon warning based on whether coupon is applied
        const couponWarning = document.getElementById('couponWarning');
        if (hasCoupon) {
            couponWarning.style.display = 'list-item';
        } else {
            couponWarning.style.display = 'none';
        }

        // Update modal message based on what items exist
        const modalMessage = document.getElementById('countryChangeMessage');
        const cartWarning = document.getElementById('cartWarning');
        const wishlistWarning = document.getElementById('wishlistWarning');

        if (hasCartItems && hasWishlistItems) {
            modalMessage.textContent = '<?= __('You have items in your cart and wishlist. Changing the country will:') ?>';
            cartWarning.style.display = 'list-item';
            wishlistWarning.style.display = 'list-item';
        } else if (hasCartItems) {
            modalMessage.textContent = '<?= __('You have items in your cart. Changing the country will:') ?>';
            cartWarning.style.display = 'list-item';
            wishlistWarning.style.display = 'list-item'; // Still show wishlist warning as it will be cleared
        } else if (hasWishlistItems) {
            modalMessage.textContent = '<?= __('You have items in your wishlist. Changing the country will:') ?>';
            cartWarning.style.display = 'list-item'; // Still show cart warning for consistency
            wishlistWarning.style.display = 'list-item';
        }

        // Show the modal
        const modal = new bootstrap.Modal(document.getElementById('countryChangeModal'));
        modal.show();
    }

    /**
     * Proceed with country change (original functionality)
     */
    function proceedWithCountryChange(country, lang) {
        localStorage.setItem('user_country', country);
        localStorage.setItem('user_language', lang);

        $.ajax({
            url: '/Home/getSiteSettings',
            method: 'POST',
            contentType: 'application/json',
            headers: {
                'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                               document.querySelector('meta[name="csrfToken"]')?.getAttribute('content')
            },
            data: JSON.stringify({
                country: country,
                lang: lang.toLowerCase()
            }),
            success: function (data) {
                window.location.reload();
            },
            error: function (xhr, status, error) {
                console.error('Error:', error);
                if (xhr.status === 403) {
                    alert('CSRF token validation failed. Please refresh the page and try again.');
                }
            }
        });
    }

    // Handle modal confirmation
    $(document).ready(function() {
        $('#confirmCountryChange').on('click', function() {
            // Get CSRF token
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                             document.querySelector('meta[name="csrfToken"]')?.getAttribute('content') ||
                             document.querySelector('input[name="_csrfToken"]')?.value;

            // Clear cart and coupons first
            $.ajax({
                url: '/Cart/clearCartForCountryChange',
                method: 'POST',
                headers: {
                    'X-CSRF-Token': csrfToken
                },
                success: function(response) {
                    if (response.success) {
                        // Cart cleared successfully, now proceed with country change
                        $('#countryChangeModal').modal('hide');
                        proceedWithCountryChange(pendingCountryChange, pendingLanguageChange);
                    } else {
                        console.error('Error clearing cart:', response.message);
                        // Still proceed with country change
                        $('#countryChangeModal').modal('hide');
                        proceedWithCountryChange(pendingCountryChange, pendingLanguageChange);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error clearing cart:', error);
                    // Still proceed with country change
                    $('#countryChangeModal').modal('hide');
                    proceedWithCountryChange(pendingCountryChange, pendingLanguageChange);
                }
            });
        });
    });

    $(document).ready(function() {
        let countdownInterval;
        function startCountdown(durationInSeconds) {
            let timer = durationInSeconds;
            const countdownEl = $('#countdown');

            clearInterval(countdownInterval);

            countdownInterval = setInterval(function () {
                const minutes = String(Math.floor(timer / 60)).padStart(2, '0');
                const seconds = String(timer % 60).padStart(2, '0');
                countdownEl.text(`${minutes}:${seconds}`);

                if (--timer < 0) {
                    clearInterval(countdownInterval);
                    countdownEl.text("00:00");
                }
            }, 1000);
        }

        $('#submit-login-form').on('click', function(e){
            e.preventDefault();
            var form = $('#login-form')[0];
            var formData = new FormData(form);

            $.ajax({
                url: '<?= $this->Url->build(['controller' => 'Customer', 'action' => 'login']) ?>',
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(response) {
                    if (response.success) {
                        showToastMessage(response.message,'success');
                        $('#loginmodal').modal('hide');
                        setTimeout(() => {
                            location.reload();
                        }, 1000);
                    } else {
                        showToastMessage(response.message,'error');         
                    }
                },
                error: function() {
                    alert('An error occurred while login.');
                }
            });
        });

        $('#send_code_btn').on('click', function() {
            const $btn = $(this);
            const $spinner = $btn.find('.spinner-border');
            const $btnText = $btn.find('.btn-text');

            const csrfToken = $('#csrf-token-login').val();
            $email = $('#loginmodal2 #email').val();

            if($email == ''){
                $('#emailError').text('Email is required.').show();
                return false;
            }

            $btn.prop('disabled', true);
            $btnText.addClass('d-none');
            $spinner.removeClass('d-none');

            $.ajax({
                url: '<?= $this->Url->build(['controller' => 'Customer', 'action' => 'sendEmailOtp']) ?>',
                method: 'POST',
                data: { email: $email, _csrfToken: csrfToken },
                success: function(response) {
                    if (response.success) {
                        showToastMessage(response.message,'success');
                        startCountdown(300);
                    } else {
                        showToastMessage(response.message,'error');         
                    }
                },
                error: function() {
                    alert('An error occurred while checking user email.');
                },
                complete: function () {
                    $btn.prop('disabled', false);
                    $spinner.addClass('d-none');
                    $btnText.removeClass('d-none');
                }
            });
        });

        $('#verify-btn').on('click', function() {
            const csrfToken = $('#csrf-token-login').val();
            $verificationCode = $('#verification_code').val();
            $email = $('#loginmodal2 #email').val();
            if($email == ''){
                $('#emailError').text('Email is required.').show();
                return false;
            }
            if ($verificationCode == '') {
                $('.error-text').text('Please enter the verification code.').show();
                return false;
            }
            $.ajax({
                url: '<?= $this->Url->build(['controller' => 'Customer', 'action' => 'verifyEmail']) ?>',
                method: 'POST',
                data: { email: $email, verification_code: $verificationCode, _csrfToken: csrfToken },
                success: function(response) {
                    if (response.success) {
                        clearInterval(countdownInterval);
                        $('#countdown').text('00:00');
                        $('#show_registration_form').show();
                        $('#verify-btn').prop('disabled', true);
                        $('.error-text').text('');
                        showToastMessage(response.message,'success');
                    } else {
                        showToastMessage(response.message,'error');
                    }
                },
                error: function() {
                    showToastMessage('An Error Occured while processing your request.','error');
                }
            });
        });

        $('#submit-register-form').on('click', function(e){
            e.preventDefault();
            if (!$('#terms').is(':checked')) {
                showToastMessage('Please accept the terms and conditions.','error');
                return false;
            }
            var form = $('#register-form')[0];
            var formData = new FormData(form);

            $.ajax({
                url: '<?= $this->Url->build(['controller' => 'Customer', 'action' => 'signup']) ?>',
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(response) {
                    if (response.success) {
                        showToastMessage(response.message,'success');
                        $('#loginmodal2').modal('hide');
                        $('#loginmodal').modal('show');
                    } else {
                        showToastMessage(response.message,'error');         
                    }
                },
                error: function() {
                    alert('An error occurred while creating your account.');
                }
            });
        });

        //this will check password and confirm password match on keyup event
        $('#confirm_password').on('keyup', function() {
            const password = $('#password').val();
            const confirmPassword = $('#confirm_password').val();
            if (password !== confirmPassword) {
                $('.password_error').show();
                $('.btn-login').prop('disabled', true);
            } else {
                $('.password_error').hide();
                $('.btn-login').prop('disabled', false);
            }
        });

        $('#name').on('input', function () {
            var cleanValue = $(this).val().replace(/[^A-Za-z\s\-]/g, '');
            $(this).val(cleanValue);
        });

        $('#show_registration_form #password').on('keyup', function () {
            var password = $(this).val();
            var errorMsg = '';

            if (password.length < 8) {
                $('#passwordError').text('Password must be at least 8 characters long.').show();
                $('.verify-btn').prop('disabled',true);
                $('#send_code_btn').prop('disabled',true);
                $('.btn-login').prop('disabled',true);
            } else if (!/[A-Z]/.test(password)) {
                $('#passwordError').text('Password must include at least one uppercase letter.').show();
                $('.verify-btn').prop('disabled',true);
                $('#send_code_btn').prop('disabled',true);
                $('.btn-login').prop('disabled',true);
            } else if (!/[a-z]/.test(password)) {
                $('#passwordError').text('Password must include at least one lowercase letter.').show();
                $('.verify-btn').prop('disabled',true);
                $('#send_code_btn').prop('disabled',true);
                $('.btn-login').prop('disabled',true);
            } else if (!/[0-9]/.test(password)) {
                $('#passwordError').text('Password must include at least one number.').show();
                $('.verify-btn').prop('disabled',true);
                $('#send_code_btn').prop('disabled',true);
                $('.btn-login').prop('disabled',true);
            } else if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
                $('#passwordError').text('Password must include at least one special character.').show();
                $('.verify-btn').prop('disabled',true);
                $('#send_code_btn').prop('disabled',true);
                $('.btn-login').prop('disabled',true);
            }
            else{
                $('#passwordError').text('').hide()
                $('.verify-btn').prop('disabled',false);
                $('#send_code_btn').prop('disabled',false);
                $('.btn-login').prop('disabled',false);
            }
        });

        document.addEventListener("DOMContentLoaded", function () {
            const hash = window.location.hash;
            if (hash === "#loginmodal") {
                const loginModal = new bootstrap.Modal(document.getElementById('loginmodal'));
                loginModal.show();
            }
            if (hash === "#loginmodal2") {
                const loginModal = new bootstrap.Modal(document.getElementById('loginmodal2'));
                loginModal.show();
            }

            const triggerEl = document.querySelector(`a[href="${hash}"]`);
            if (triggerEl) {
                const tab = new bootstrap.Tab(triggerEl);
                tab.show();
            }
        });
    });

</script>

<script src="../../javascript/ozone-dev.js"></script>
<script src="../../javascript/ozone.js"></script>
<!-- Bootstrap JS -->
<script src="../../bundles/bootstrap/js/bootstrap.bundle.min.js"></script>
<!-- <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script> -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"
        integrity="sha512-v2CJ7UaYy4JwqLDIrZUI/4hqeoQieOmAZNXBeQyjo21dadnwR+8ZaIJVT8EE2iyI61OV8e6M8PP2/4hpQINQ/g=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
 <script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/jquery.validate.js"></script>

    <!-- vendors -->
     <?php 
     /****
    <script src="<?= $this->Url->build('carousel/assets/vendors/highlight.js') ?>"></script>
    <script src="<?= $this->Url->build('carousel/assets/js/app.js') ?>"></script>
    ****/ ?>
    <script src="https://cdn.jsdelivr.net/npm/nouislider@15.7.0/dist/nouislider.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js"></script>





    <!-- Toast Message Script -->
<script>
    $(document).ready(function() {
        // Check for toast message from session
        <?php
        $toastMessage = $session->read('toast_message');
        if ($toastMessage):
            // Clear the message from session after reading
            $session->delete('toast_message');
        ?>
        showToastMessage('<?= h($toastMessage['message']) ?>', '<?= h($toastMessage['type']) ?>');
        <?php endif; ?>
    });

    function showToastMessage(message, type) {
        // Create message container with enhanced styling
        const messageContainer = $(`
            <div class="alert alert-dismissible fade show" style="
                margin-bottom: 10px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                border-radius: 8px;
                border: none;
                animation: slideInRight 0.3s ease-out;
            "></div>
        `);

        // Set message type and styling
        const alertClass = type === 'success' ? 'alert-success' :
                          type === 'error' ? 'alert-danger' :
                          type === 'warning' ? 'alert-warning' : 'alert-info';

        messageContainer.addClass(alertClass);

        // Add message content with icon
        const icon = type === 'success' ? 'fas fa-check-circle' :
                    type === 'error' ? 'fas fa-exclamation-circle' :
                    type === 'warning' ? 'fas fa-exclamation-triangle' : 'fas fa-info-circle';

        messageContainer.html(`
            <div class="alert-body d-flex align-items-center">
                <i class="${icon} me-2"></i>
                <span><b>${message}</b></span>
                <button type="button" class="btn-close ms-auto" onclick="$(this).closest('.alert').fadeOut(300, function(){ $(this).remove(); })"></button>
            </div>
        `);

        // Add to container and show with animation
        $('#toast-message-container').append(messageContainer);

        // Auto hide after 4 seconds
        setTimeout(() => {
            messageContainer.fadeOut(300, function() {
                $(this).remove();
            });
        }, 4000);
    }

    // Common quantity update functionality for cart and product pages
    // Global variables for rate limiting and request management
    window.updateRequests = window.updateRequests || new Map();
    window.lastUpdateTime = window.lastUpdateTime || new Map();
    const UPDATE_COOLDOWN = 1000; // 1 second cooldown between updates

    // Common function to update quantity dynamically
    function updateQuantityDynamic(inputId, change, options = {}) {
        const installationCharge = $('#installationChargeCheckbox').is(':checked') ? 1 : 0;
        const input = document.getElementById(inputId);
        if (!input) {
            console.error('Input element not found:', inputId);
            return;
        }

        // Get identifier for tracking (cart_item_id for cart page, product_id for product page)
        const cartItemId = input.dataset.cartItemId;
        const productId = input.dataset.productId;
        const identifier = cartItemId || productId;
        const isCartPage = !!cartItemId;
        const isProductPage = !!productId;

        // Check if there's already an ongoing request for this item
        if (window.updateRequests.has(identifier)) {
            return;
        }

        // Check cooldown period
        const lastUpdate = window.lastUpdateTime.get(identifier) || 0;
        const now = Date.now();
        if (now - lastUpdate < UPDATE_COOLDOWN) {
            showToastMessage('Please wait before updating quantity again', 'warning');
            return;
        }

        let value = parseInt(input.value) || 1;
        const newValue = Math.max(1, value + change);

        // Update input value immediately for better UX
        input.value = newValue;

        if (!identifier) {
            showToastMessage('Error: Item ID not found', 'error');
            input.value = value; // Revert value
            return;
        }

        // Get CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                         document.querySelector('meta[name="csrfToken"]')?.getAttribute('content') ||
                         document.querySelector('input[name="_csrfToken"]')?.value;

        if (!csrfToken) {
            showToastMessage('Error: Security token not found', 'error');
            input.value = value; // Revert value
            return;
        }

        // Mark request as ongoing and disable buttons
        window.updateRequests.set(identifier, true);
        window.lastUpdateTime.set(identifier, now);

        // Disable quantity buttons if function exists
        if (typeof disableQuantityButtons === 'function') {
            disableQuantityButtons(inputId, true);
        }

        // Prepare request data - same endpoint for both cart and product pages
        const requestData = {
            quantity: newValue
        };

        // Add the appropriate ID field
        if (isCartPage) {
            requestData.cart_item_id = cartItemId;
        } else if (isProductPage) {
            requestData.product_id = productId;
        }
        requestData.installation_charge = installationCharge;
        // Use same endpoint for both pages
        const fetchUrl = '<?= $this->Url->build(['controller' => 'Cart', 'action' => 'updateQuantity']) ?>';

        // Make the fetch request
        fetch(fetchUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': csrfToken
            },
            body: JSON.stringify(requestData)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.text(); // Get as text first
        })
        .then(text => {
            try {
                return JSON.parse(text);
            } catch (e) {
                console.error('Invalid JSON response:', text);
                if (text.includes('<div class')) {
                    throw new Error('Server returned HTML error page instead of JSON');
                }
                throw new Error('Invalid JSON response from server');
            }
        })
        .then(data => {
            if (data.success) {
                console.log('Quantity updated successfully:', data.message);
              //  showToastMessage('Quantity updated successfully', 'success');
                  showToastMessage(data.message, 'success');

                // Update cart count if provided
                if (data.cartCount !== undefined && window.CartUpdater) {
                    window.CartUpdater.updateCartCount(data.cartCount);
                }

                // Handle page-specific actions
                if (isCartPage) {
                    // Reload cart page after short delay
                    setTimeout(() => {
                        location.reload();
                    }, 500);
                } else if (isProductPage) {
                    // Update product page elements
                    input.setAttribute('data-original-value', newValue);

                    // Update cart button if function exists
                    if (typeof updateCartButton === 'function') {
                        updateCartButton(true, newValue);
                    }
                }
            } else {
                showToastMessage('Failed to update quantity: ' + (data.message || 'Unknown error'), 'error');
                input.value = value; // Revert value
            }
        })
        .catch(err => {
            console.error('Fetch error:', err);
            showToastMessage('Error updating quantity: ' + err.message, 'error');
            input.value = value; // Revert value
        })
        .finally(() => {
            // Always cleanup after request completes
            window.updateRequests.delete(identifier);

            // Re-enable quantity buttons if function exists
            if (typeof disableQuantityButtons === 'function') {
                disableQuantityButtons(inputId, false);
            }
        });
    }

    // Simple quantity update function for UI only (no API call)
    function updateQuantitySimple(inputId, change) {
        const input = document.getElementById(inputId);
        if (!input) return;

        let value = parseInt(input.value) || 1;
        const newValue = Math.max(1, value + change);
        input.value = newValue;
    }

    // ========== WISHLIST FUNCTIONALITY ==========

    // Wishlist event handlers
    $(document).on('click', '.add-wishlist-btn', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const productId = $(this).closest('.wishlist-container, .wishlist, .product-card').data('product-id');
        const wishlistContainer = $(this).closest('.wishlist-container, .wishlist');
        const button = $(this);

        // Show loading state - different for buttons vs icons
        if (button.hasClass('btn')) {
            button.prop('disabled', true).html(`
                <i class="fas fa-spinner fa-spin me-2"></i> Adding to Wishlist...
            `);
        } else {
            button.css('opacity', '0.5');
        }

        addToWishlist(productId)
            .then(response => {
                if (response.status === 'success') {
                    // Update UI based on container type
                    if (wishlistContainer.hasClass('wishlist-container')) {
                        // Product page style (button)
                        wishlistContainer.html(`
                            <button class="btn btn-wishlist w-100 d-flex justify-content-center align-items-center remove-wishlist-btn">
                                <i class="fas fa-heart text-danger me-2"></i> Remove from Wishlist
                            </button>
                        `);
                    } else {
                        // List page style (heart icon)
                        wishlistContainer.html(`
                            <span class="wishlist-icon remove-wishlist-btn heart-filled" title="Remove from wishlist"><img src="../../img/ozone/heart_filled.png" class="img-fluid wishlist-img"/></span>
                        `);
                    }

                    // Show success message
                    showToastMessage(response.message || 'Added to wishlist successfully!', 'success');

                    // Update wishlist count in header if exists
                    updateWishlistCount(1);
                } else {
                    // Reset button state
                    if (button.hasClass('btn')) {
                        button.prop('disabled', false).html(`
                            <i class="far fa-heart text-success me-2"></i> Add to Wishlist
                        `);
                    } else {
                        button.css('opacity', '1');
                    }
                    showToastMessage(response.message || 'Failed to add to wishlist', 'error');
                }
            })
            .catch(error => {
                console.error('Error adding to wishlist:', error);
                // Reset button state
                if (button.hasClass('btn')) {
                    button.prop('disabled', false).html(`
                        <i class="far fa-heart text-success me-2"></i> Add to Wishlist
                    `);
                } else {
                    button.css('opacity', '1');
                }
                showToastMessage('Failed to add to wishlist', 'error');
            });
    });

    $(document).on('click', '.remove-wishlist-btn', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const productId = $(this).closest('.wishlist-container, .wishlist, .product-card').data('product-id');
        const wishlistContainer = $(this).closest('.wishlist-container, .wishlist');
        const button = $(this);

        // Show loading state - different for buttons vs icons
        if (button.hasClass('btn')) {
            button.prop('disabled', true).html(`
                <i class="fas fa-spinner fa-spin me-2"></i> Removing...
            `);
        } else {
            button.css('opacity', '0.5');
        }

        removeFromWishlist(productId)
            .then(response => {
                if (response.status === 'success') {
                    // Update UI based on container type
                    if (wishlistContainer.hasClass('wishlist-container')) {
                        // Product page style (button)
                        wishlistContainer.html(`
                            <button class="btn btn-wishlist w-100 d-flex justify-content-center align-items-center add-wishlist-btn">
                                <i class="far fa-heart text-success me-2"></i> Add to Wishlist
                            </button>
                        `);
                    } else {
                        // List page style (heart icon)
                        wishlistContainer.html(`
                            <span class="wishlist-icon add-wishlist-btn heart-empty" title="Add to wishlist"><img src="../../img/ozone/heart.png" class="img-fluid wishlist-img"/></span>
                        `);
                    }

                    // Show success message
                    showToastMessage(response.message || 'Removed from wishlist successfully!', 'success');

                    // Update wishlist count in header if exists
                    updateWishlistCount(-1);
                } else {
                    // Reset button state
                    if (button.hasClass('btn')) {
                        button.prop('disabled', false).html(`
                            <i class="fas fa-heart text-danger me-2"></i> Remove from Wishlist
                        `);
                    } else {
                        button.css('opacity', '1');
                    }
                    showToastMessage(response.message || 'Failed to remove from wishlist', 'error');
                }
            })
            .catch(error => {
                console.error('Error removing from wishlist:', error);
                // Reset button state
                if (button.hasClass('btn')) {
                    button.prop('disabled', false).html(`
                        <i class="fas fa-heart text-danger me-2"></i> Remove from Wishlist
                    `);
                } else {
                    button.css('opacity', '1');
                }
                showToastMessage('Failed to remove from wishlist', 'error');
            });
    });

    // Helper functions for wishlist
    function addToWishlist(productId) {
        return new Promise((resolve, reject) => {
            // Get CSRF token
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                             document.querySelector('meta[name="csrfToken"]')?.getAttribute('content') ||
                             document.querySelector('input[name="_csrfToken"]')?.value ||
                             '<?= $this->request->getAttribute('csrfToken') ?>';

            $.ajax({
                headers: {
                    'X-CSRF-Token': csrfToken
                },
                url: "<?= $this->Url->build(['controller' => 'Cart', 'action' => 'addToWishlist']) ?>",
                type: 'POST',
                data: {product_id: productId},
                success: function (response) {
                    resolve(response);
                },
                error: function (xhr, status, error) {
                    reject('An error occurred: ' + error);
                }
            });
        });
    }

    function removeFromWishlist(productId) {
        return new Promise((resolve, reject) => {
            // Get CSRF token
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                             document.querySelector('meta[name="csrfToken"]')?.getAttribute('content') ||
                             document.querySelector('input[name="_csrfToken"]')?.value ||
                             '<?= $this->request->getAttribute('csrfToken') ?>';

            $.ajax({
                headers: {
                    'X-CSRF-Token': csrfToken
                },
                url: "<?= $this->Url->build(['controller' => 'Cart', 'action' => 'removeFromWishlist']) ?>",
                type: 'POST',
                data: {product_id: productId},
                success: function (response) {
                    resolve(response);
                },
                error: function (xhr, status, error) {
                    reject('An error occurred: ' + error);
                }
            });
        });
    }

    function updateWishlistCount(change) {
        const countElement = $('.wishlist-count, .cart-superscript');
        if (countElement.length > 0) {
            const currentCount = parseInt(countElement.text()) || 0;
            const newCount = Math.max(0, currentCount + change);
            countElement.text(newCount);
        }
    }
</script>

<!-- Product Search Modal -->
<div class="modal fade" id="productSearchModal" tabindex="-1" aria-labelledby="productSearchModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered" style="max-width:500px;">
    <div class="modal-content" style="border-radius:16px;">
      <div class="modal-header border-0 pb-0">
        <h5 class="modal-title" id="productSearchModalLabel"><?= __('Search Products') ?></h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body pt-0">
        <div class="input-group mb-3">
          <input type="text" id="product-search-input" class="form-control" placeholder="<?= __('Type to search...') ?>" autocomplete="off" autofocus>
          <button class="btn btn-outline-secondary" type="button" id="product-search-clear" style="display:none;">&times;</button>
        </div>
        <div id="product-search-results" style="max-height:300px;overflow-y:auto;"></div>
      </div>
    </div>
  </div>
</div>
<style>
/* Minimal styling for search modal */
#product-search-results .search-item {
    padding: 12px 0;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    transition: background-color 0.2s ease;
}
#product-search-results .search-item:last-child {
    border-bottom: none;
}
#product-search-results .search-item:hover {
    background: #f8f9fa;
}
#product-search-results .search-item .flex-grow-1 {
    min-width: 0; /* Allows text truncation */
}
#product-search-results .search-item .flex-grow-1 > div {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
#product-search-results .search-item .flex-grow-1 > div:nth-child(2) {
    /* Description can wrap to multiple lines */
    white-space: normal;
    overflow: visible;
    text-overflow: initial;
    line-height: 1.3;
}
#product-search-results .search-item .flex-grow-1 > div:last-child {
    /* Price should not truncate */
    white-space: nowrap;
    overflow: visible;
    text-overflow: initial;
}
</style>
<script>
$(function(){
    // Show modal on search icon click
    $('#product-search-trigger').on('click', function(e){
        e.preventDefault();
        var modal = new bootstrap.Modal(document.getElementById('productSearchModal'));
        modal.show();
        setTimeout(function(){
            $('#product-search-input').focus();
        }, 300);
    });

    // Clear input button
    $('#product-search-clear').on('click', function(){
        $('#product-search-input').val('').focus();
        $('#product-search-results').empty();
        $(this).hide();
    });

    // Search as you type (AJAX)
    let searchTimeout;
    $('#product-search-input').on('input', function(){
        const query = $(this).val().trim();
        if(query.length > 0){
            $('#product-search-clear').show();
        } else {
            $('#product-search-clear').hide();
            $('#product-search-results').empty();
            return;
        }
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(function(){
            // Show loading indicator
            $('#product-search-results').html('<div class="text-center py-3"><i class="fas fa-spinner fa-spin"></i> <?= __('Searching...') ?></div>');

            $.ajax({
                url: '/Home/search',
                method: 'GET',
                data: { q: query },
                success: function(res){
                    // Debug logging to see the actual response
                    // console.log('Search API Response:', res);
                    // console.log('Response type:', typeof res);

                    // If response is a string, try to parse as JSON
                    if (typeof res === 'string') {
                        try {
                            res = JSON.parse(res);
                            //console.log('Parsed JSON response:', res);
                        } catch (e) {
                            console.error('JSON parsing error:', e);
                            $('#product-search-results').html('<div class="text-danger py-3 text-center"><?= __('Error parsing search results.') ?></div>');
                            return;
                        }
                    }

                    let html = '';
                    // console.log('Products array:', res.products);
                    // console.log('Products length:', res.products ? res.products.length : 'undefined');

                    if(res && res.products && res.products.length){
                        console.log('Processing products...');
                        res.products.forEach(function(item, index){
                            // console.log(`Product ${index}:`, item);
                            // console.log(`Product URL field:`, item.url);
                            // console.log(`Product slug_url field:`, item.slug_url);
                            // console.log(`Product url_key field:`, item.url_key);
                            // console.log(`Product image:`, item.product_image);

                            // Use url_key or slug_url for the URL
                            const productUrl = item.url || item.slug_url || item.url_key || '#';
                            const productImage = item.product_image || '../../img/no-image.jpg';
                            const productDescription = item.description || '';
                            
                            // Truncate description if it's too long
                            const truncatedDescription = productDescription.length > 100 ? 
                                productDescription.substring(0, 100) + '...' : productDescription;

                            // Format prices
                            const salesPrice = parseFloat(item.sales_price || 0);
                            const promotionPrice = parseFloat(item.promotion_price || 0);
                            const discount = parseFloat(item.discount || 0);
                            
                            // Create price display
                            let priceHtml = '';
                            if (promotionPrice > 0 && promotionPrice < salesPrice) {
                                // Show discounted price
                                priceHtml = `
                                    <div style="font-size:13px;color:#28a745;font-weight:600;">
                                        ${promotionPrice.toFixed(2)} 
                                        <span style="text-decoration:line-through;color:#999;font-weight:400;margin-left:5px;">
                                            ${salesPrice.toFixed(2)}
                                        </span>
                                        ${discount > 0 ? `<span style="color:#dc3545;font-size:11px;margin-left:5px;">(${discount.toFixed(0)}% off)</span>` : ''}
                                    </div>
                                `;
                            } else if (salesPrice > 0) {
                                // Show regular price
                                priceHtml = `<div style="font-size:13px;color:#28a745;font-weight:600;">${salesPrice.toFixed(2)}</div>`;
                            }

                            html += `<div class="search-item" onclick="window.location='/product/${productUrl}'">
                                <div class="d-flex align-items-center">
                                    <img src="${productImage}" alt="${item.product_name}" style="width:40px;height:40px;object-fit:cover;border-radius:4px;margin-right:10px;">
                                    <div class="flex-grow-1">
                                        <div style="white-space: inherit !important;font-weight:500;color:#333;">${item.display_name || item.product_name}</div>
                                        ${truncatedDescription ? `<div style="font-size:12px;color:#666;margin-top:2px;">${truncatedDescription}</div>` : ''}
                                        ${priceHtml}
                                    </div>
                                </div>
                            </div>`;
                        });
                    } else {
                        console.log('No products found or empty response');
                        html = '<div class="text-muted py-3 text-center"><?= __('No products found.') ?></div>';
                    }
                    $('#product-search-results').html(html);
                },
                error: function(xhr, status, error){
                    console.error('Search AJAX Error:', {
                        xhr: xhr,
                        status: status,
                        error: error,
                        responseText: xhr.responseText
                    });
                    $('#product-search-results').html('<div class="text-danger py-3 text-center"><?= __('Error searching products.') ?></div>');
                }
            });
        }, 300);
    });

    // Autofocus input when modal shown
    $('#productSearchModal').on('shown.bs.modal', function () {
        $('#product-search-input').focus();
    });
});
</script>

<!-- Global translations for JavaScript -->
<script>
window.translations = {
    'error_adding_item_to_cart': '<?= __('Error adding item to cart') ?>',
    'server_error_html': '<?= __('Server returned HTML error page instead of JSON') ?>',
    'invalid_json_response': '<?= __('Invalid JSON response from server') ?>',
    'network_error': '<?= __('Network error') ?>',
    'please_enter_coupon_code': '<?= __('Please enter a coupon code') ?>',
    'security_token_not_found': '<?= __('Security token not found. Please refresh the page and try again.') ?>',
    'error_applying_coupon': '<?= __('An error occurred while applying the coupon') ?>',
    'error_removing_coupon': '<?= __('An error occurred while removing the coupon') ?>'
};
</script>
<script>

    function getCsrfToken() {
    // First try the global variable set by PHP
    if (window.csrfToken) {
        return window.csrfToken;
    }

    // Try to get CSRF token from meta tags (most common)
    let csrfToken = document.querySelector('meta[name="csrfToken"]')?.getAttribute('content') ||
                   document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

    // If not found in meta tags, try to get from hidden input in forms
    if (!csrfToken) {
        const csrfInput = document.querySelector('input[name="_csrfToken"]');
        if (csrfInput) {
            csrfToken = csrfInput.value;
        }
    }

    // If still not found, try to get from any form on the page
    if (!csrfToken) {
        const forms = document.querySelectorAll('form');
        for (let form of forms) {
            const tokenInput = form.querySelector('input[name="_csrfToken"]');
            if (tokenInput) {
                csrfToken = tokenInput.value;
                break;
            }
        }
    }

    // Debug logging
    if (!csrfToken) {
        // console.warn('CSRF token not found. This may cause AJAX requests to fail.');
        // console.log('Available meta tags:', document.querySelectorAll('meta[name*="csrf"], meta[name*="CSRF"]'));
        // console.log('Available CSRF inputs:', document.querySelectorAll('input[name="_csrfToken"]'));
        // console.log('Global csrfToken:', window.csrfToken);
    }

    return csrfToken || '';
}

// ========================================
// COMMON COUPON FUNCTIONALITY
// ========================================

/**
 * Apply coupon from input field
 */
function applyCoupon() {
    const couponInput = document.getElementById('coupon-code-input');
    const couponCode = couponInput.value.trim();

    if (!couponCode) {
        showToastMessage(window.translations?.please_enter_coupon_code || 'Please enter a coupon code', 'error');
        return;
    }

    applyCouponCode(couponCode);
}

/**
 * Apply specific coupon code
 */
function applyCouponCode(couponCode) {
    const applyBtn = document.getElementById('apply-coupon-btn');
    const btnText = applyBtn.querySelector('.btn-text');
    const spinner = applyBtn.querySelector('.spinner-border');

    // Show loading state
    btnText.textContent = 'Applying...';
    spinner.classList.remove('d-none');
    applyBtn.disabled = true;

    // Get CSRF token
    const csrfToken = getCsrfToken();

    if (!csrfToken) {
        showToastMessage(window.translations?.security_token_not_found || 'Security token not found. Please refresh the page and try again.', 'error');
        // Reset button state
        btnText.textContent = 'Apply';
        spinner.classList.add('d-none');
        applyBtn.disabled = false;
        return;
    }

    fetch('/cart/applyCoupon', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-Token': csrfToken
        },
        body: JSON.stringify({
            coupon_code: couponCode
        })
    })
    .then(response => response.json())
    .then(data => {
      
        if (data.success) {
           
            showToastMessage(data.message, 'success');
            // Show loading message and reload page
            btnText.textContent = 'Applied! Refreshing...';
            showPageReloadMessage("<?= __('Coupon applied successfully! Updating cart...') ?>");
            setTimeout(() => {
              
                window.location.reload();
            }, 1500);
        } else {
          
            showToastMessage(data.message, 'error');
            // Reset button state
            btnText.textContent = 'Apply';
            spinner.classList.add('d-none');
            applyBtn.disabled = false;
        }
    })
    .catch(error => {

        showToastMessage(window.translations?.error_applying_coupon || 'An error occurred while applying the coupon', 'error');
        // Reset button state
        btnText.textContent = 'Apply';
        spinner.classList.add('d-none');
        applyBtn.disabled = false;
    });
}

/**
 * Remove applied coupon
 */
function removeCoupon_oold() {
    if (!confirm('Are you sure you want to remove this coupon?')) {
        return;
    }

    // Show loading state on remove button if it exists
    const removeBtn = document.querySelector('[onclick="removeCoupon()"]');
    if (removeBtn) {
        removeBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Removing...';
        removeBtn.disabled = true;
    }

    // Get CSRF token
    const csrfToken = getCsrfToken();

    if (!csrfToken) {
        showToastMessage(window.translations?.security_token_not_found || 'Security token not found. Please refresh the page and try again.', 'error');
        return;
    }

    fetch('/cart/removeCoupon', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-Token': csrfToken
        }
    })
    .then(response => response.json())
    .then(data => {
      
        if (data.success) {
           
            showToastMessage(data.message, 'success');
            // Show loading message and reload page
             showPageReloadMessage("<?= __('Coupon removed successfully! Updating cart...') ?>");
            // showPageReloadMessage('Coupon removed successfully! Updating cart...');
            setTimeout(() => {
             
                window.location.reload();
            }, 1500);
        } else {
           
            showToastMessage(data.message, 'error');
        }
    })
    .catch(error => {

        showToastMessage(window.translations?.error_removing_coupon || 'An error occurred while removing the coupon', 'error');
    });
}

function removeCoupon() {
    const modal = new bootstrap.Modal(document.getElementById('removeCouponModal'));
    modal.show();

    // Remove previous click handlers to avoid stacking
    const confirmBtn = document.getElementById('confirmRemoveCoupon');
    confirmBtn.replaceWith(confirmBtn.cloneNode(true));
    const newConfirmBtn = document.getElementById('confirmRemoveCoupon');

    newConfirmBtn.addEventListener('click', function () {
        modal.hide();

        // Show loading state on remove button if it exists
        const removeBtn = document.querySelector('[onclick="removeCoupon()"]');
        if (removeBtn) {
            removeBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Removing...';
            removeBtn.disabled = true;
        }

        const csrfToken = getCsrfToken();
        if (!csrfToken) {
            showToastMessage(window.translations?.security_token_not_found || 'Security token not found. Please refresh the page and try again.', 'error');
            return;
        }

        fetch('/cart/removeCoupon', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-Token': csrfToken
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToastMessage(data.message, 'success');
                 showPageReloadMessage("<?= __('Coupon removed successfully! Updating cart...') ?>");
                
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                showToastMessage(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error removing coupon:', error);
            showToastMessage(window.translations?.error_removing_coupon || 'An error occurred while removing the coupon', 'error');
        });
    });
}



/**
 * Toggle available coupons display
 */
function toggleAvailableCoupons() {
    const container = document.getElementById('available-coupons');
   // const toggleText = document.getElementById('available-coupons-toggle-text');
    const toggleIcon = document.getElementById('available-coupons-toggle-icon');

    if (container.style.display === 'none') {
        container.style.display = 'block';
        // toggleText.textContent = 'Hide available coupons';
        toggleIcon.classList.remove('fa-chevron-down');
        toggleIcon.classList.add('fa-chevron-up');
    } else {
        container.style.display = 'none';
        // toggleText.textContent = 'View available coupons';
        toggleIcon.classList.remove('fa-chevron-up');
        toggleIcon.classList.add('fa-chevron-down');
    }
}

/**
 * Show coupon terms and conditions modal
 */
function showCouponTerms(couponCode, termsConditions) {
    document.getElementById('couponCodeTitle').textContent = couponCode;
    document.getElementById('couponTermsContent').innerHTML = termsConditions.replace(/\n/g, '<br>');

    const modal = new bootstrap.Modal(document.getElementById('couponTermsModal'));
    modal.show();
}

/**
 * Show page reload message with loading indicator
 */
function showPageReloadMessage(message) {
    // Create or update loading overlay
    let overlay = document.getElementById('page-reload-overlay');
    if (!overlay) {
        overlay = document.createElement('div');
        overlay.id = 'page-reload-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            color: white;
            font-size: 18px;
            text-align: center;
        `;
        document.body.appendChild(overlay);
    }

    overlay.innerHTML = `
        <div style="background: white; padding: 30px; border-radius: 10px; color: #333; box-shadow: 0 4px 20px rgba(0,0,0,0.3);">
            <div style="margin-bottom: 20px;">
                <i class="fas fa-spinner fa-spin" style="font-size: 24px; color: #28a745;"></i>
            </div>
            <div style="font-weight: bold; margin-bottom: 10px;">${message}</div>
            <div style="color: #666; font-size: 14px;">Please wait...</div>
        </div>
    `;
    overlay.style.display = 'flex';
}

/**
 * Initialize coupon functionality
 */
function initializeCouponFunctionality() {
    // Handle Enter key press in coupon input
    const couponInput = document.getElementById('coupon-code-input');
    if (couponInput) {
        couponInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                applyCoupon();
            }
        });
    }
}

/**
 * Debug function to test CSRF token detection
 * Call debugCsrfToken() in browser console to test
 */
function debugCsrfToken() {
    const token = getCsrfToken();
   
    return token;
}

// Make debug function globally available
window.debugCsrfToken = debugCsrfToken;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeCouponFunctionality();

    // Log CSRF token status on page load
    // console.log('CSRF Token available:', getCsrfToken() ? 'YES' : 'NO');
});
</script>

<style>
/* Global wishlist image styles */
.wishlist-img {
    width: 24px;
    height: 24px;
    transition: opacity 0.2s ease, transform 0.2s ease;
}

.wishlist-icon {
    transition: opacity 0.2s ease, transform 0.2s ease;
    display: inline-block;
    line-height: 1;
}

.wishlist-icon:hover {
    opacity: 0.8;
    transform: scale(1.1);
}

.heart-filled .wishlist-img {
    filter: none;
}

.heart-empty .wishlist-img {
    filter: grayscale(100%);
}

/* Responsive wishlist images */
@media (max-width: 768px) {
    .wishlist-img {
        width: 20px;
        height: 20px;
    }
}
</style>

  <?= $this->Html->script('cart-updates') ?>
  <!-- <?= $this->Html->script('custom') ?> -->
 <?php echo $this->fetch('add_js'); ?>
    
</body>


</html>
<!-- <div class="carousel-item active">
<div class="row align-items-center">
<div class="col-lg-6"><img alt="Technicians installing AC" class="mobile img-fluid rounded mb-0 px-5" src="../../img/ozone/success-history.png" />
<h2 class="section-title-abut-install my-5">Installation Services</h2>

<div class="card-box-shadow my-3"><img class="img-fluid" src="../../img/ozone/Ticksquare.png" />
<p class="mb-0">Windows, wall mounted, ducted, and decorative type air conditioners</p>
</div>

<div class="card-box-shadow my-3"><img class="img-fluid" src="../../img/ozone/Ticksquare.png" />
<p class="mb-0">Package units, ahus &amp; fresh air handling units(DX type)</p>
</div>

<div class="card-box-shadow my-3"><img class="img-fluid" src="../../img/ozone/Ticksquare.png" />
<p class="mb-0">Variable refrigerant flow (VRF) units</p>
</div>

<div class="card-box-shadow my-3"><img class="img-fluid" src="../../img/ozone/Ticksquare.png" />
<p class="mb-0">Assembling and installation of AHUS and FCUs</p>
</div>

<div class="card-box-shadow my-3"><img class="img-fluid" src="../../img/ozone/Ticksquare.png" />
<p class="mb-0">Installation, testing &amp; commissioning of chilled water system (air cooled &amp; centrifugal chillers)</p>
</div>

<div class="card-box-shadow my-3"><img class="img-fluid" src="../../img/ozone/Ticksquare.png" />
<p class="mb-0">Air conditioning ductworks</p>
</div>

<div class="card-box-shadow mt-3 mb-5"><img class="img-fluid" src="../../img/ozone/Ticksquare.png" />
<p class="mb-0">Copper pipe with insulation</p>
</div>
<!-- more card-box-shadow divs --></div>

<div class="col-lg-6 desktop"><img alt="Technicians installing AC" class="img-fluid rounded" src="../../img/ozone/success-history.png" /></div>
</div>
</div> -->
