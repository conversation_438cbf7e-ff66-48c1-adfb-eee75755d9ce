<?php
declare(strict_types=1);

namespace App\Controller;

use Cake\Controller\Controller;
use Cake\ORM\TableRegistry;
use Cake\Routing\Router;
use Cake\Core\Configure;
use Cake\Utility\Security;
use Cake\I18n\Time;
use Cake\I18n\FrozenTime;
use App\Mailer\UserMailer;
use Authentication\PasswordHasher\DefaultPasswordHasher;
use Cake\Http\Client;
use Cake\Http\Exception\BadRequestException;
use Cake\Http\Response;
use Cake\Utility\Text;
use Mpdf\Mpdf;

/**
 * Users Controller
 *
 */
class AccountController extends Controller
{
    protected $Cities;
    protected $Categories;
    protected $Users;
    protected $Customers;
    protected $OtpVerifications;
    protected $Banners;
    protected $BannerAds;
    protected $Offers;
    protected $Widgets;
    protected $Products;
    protected $ApiRequestLogs;
    protected $PaymentMethods;
    protected $Carts;
    protected $CartItems;
    protected $WidgetCategoryMappings;
    protected $Orders;
    protected $OrderItems;
    protected $SiteSettings;
    protected $Wishlists;
    protected $Reviews;
    protected $Showrooms;
    protected $Brands;
    protected $CustomerAddresses;
    protected $Loyalty;
    protected $ReviewImages;
    protected $ContentPages;
    protected $CustomerCards;
    protected $Invoices;
    protected $DeliveryCharges;
    protected $Transactions;
    protected $FaqCategories;
    protected $Faqs;
    protected $Wallets;
    protected $OrderCancellationCategories;
    protected $OrderCancellations;
    protected $OrderReturnCategories;
    protected $OrderReturns;
    protected $OrderItemReviews;
    protected $OrderTrackingHistories;
    protected $ProductImages;
    protected $OrderReturnImages;
    protected $ContactQueryTypes;
    protected $CartItemAttributes;
    protected $Countries;
    protected $States;

    public function initialize(): void
    {
        parent::initialize();

        $this->CartItemAttributes = $this->fetchTable('CartItemAttributes');
        $this->Cities = $this->fetchTable('Cities');
        $this->Countries = $this->fetchTable('Countries');
        $this->States = $this->fetchTable('States');
        $this->Categories = $this->fetchTable('Categories');
        $this->Users = $this->fetchTable('Users');
        $this->Customers = $this->fetchTable('Customers');
        $this->OtpVerifications = $this->fetchTable('OtpVerifications');
        $this->Banners = $this->fetchTable('Banners');
        $this->BannerAds = $this->fetchTable('BannerAds');
        $this->Offers = $this->fetchTable('Offers');
        $this->Widgets = $this->fetchTable('Widgets');
        $this->Products = $this->fetchTable('Products');
        $this->Reviews = $this->fetchTable('Reviews');
        //$this->Showrooms = $this->fetchTable('Showrooms');
        $this->Brands = $this->fetchTable('Brands');
        $this->CustomerAddresses = $this->fetchTable('CustomerAddresses');
        $this->ApiRequestLogs = $this->fetchTable('ApiRequestLogs');
        $this->PaymentMethods = $this->fetchTable('PaymentMethods');
        $this->Carts = $this->fetchTable('Carts');
        $this->CartItems = $this->fetchTable('CartItems');
        $this->WidgetCategoryMappings = $this->fetchTable('WidgetCategoryMappings');
        $this->Orders = $this->fetchTable('Orders');
        $this->OrderItems = $this->fetchTable('OrderItems');
        $this->SiteSettings = $this->fetchTable('SiteSettings');
        $this->Wishlists = $this->fetchTable('Wishlists');
        //$this->Loyalty = $this->fetchTable('Loyalty');
        $this->ReviewImages = $this->fetchTable('ReviewImages');
        $this->ContentPages = $this->fetchTable('ContentPages');
        //$this->DeliveryCharges = $this->fetchTable('DeliveryCharges');
        $this->Transactions = $this->fetchTable('Transactions');
        //$this->CustomerCards = $this->fetchTable('CustomerCards');
        $this->Invoices = $this->fetchTable('Invoices');
        $this->FaqCategories = $this->fetchTable('FaqCategories');
        $this->Faqs = $this->fetchTable('Faqs');
        $this->Wallets = $this->fetchTable('Wallets');
        $this->OrderCancellationCategories = $this->fetchTable('OrderCancellationCategories');
        $this->OrderCancellations = $this->fetchTable('OrderCancellations');
        $this->OrderReturnCategories = $this->fetchTable('OrderReturnCategories');
        $this->OrderReturns = $this->fetchTable('OrderReturns');
        $this->OrderItemReviews = $this->fetchTable('OrderItemReviews');
        //$this->OrderTrackingHistories = $this->fetchTable('OrderTrackingHistories');
        $this->ProductImages = $this->fetchTable('ProductImages');
        $this->OrderReturnImages = $this->fetchTable('OrderReturnImages');
        //$this->ContactQueryTypes = $this->fetchTable('ContactQueryTypes');

        $this->loadComponent('Global');
        $this->loadComponent('Media');
        $this->loadComponent('CustomPaginator');
        $this->loadComponent('Website');
        $this->loadComponent('Flash');
        //$this->loadComponent('Mtn');
        //$this->loadComponent('Wave');
        $this->loadComponent('WebsiteFunction');
        $this->viewBuilder()->setLayout('website');

    }


    public function beforeRender(\Cake\Event\EventInterface $event)
    {
        parent::beforeRender($event);
        $this->WebsiteFunction->getSeoData();
    }

    public function myAccount()
    {
        $account = $this->request->getSession()->read('Auth.User');
        if($account){

            $users = $this->Users->find()
            ->contain(['Customers'])
            ->where(['Users.status' => 'A'])
            ->where(['Users.id' => $account->id])
            ->first();
            if ($users->customer->profile_photo) {
                $users->customer->profile_photo = $this->Media->getCloudFrontURL($users->customer->profile_photo);
            }

            // Add date_of_birth to user as dob
            if ($users->customer->date_of_birth) {
                $users->dob = $users->customer->date_of_birth->format('Y-m-d');
                // Print year from date_of_birth
//                echo date('Y', strtotime((string)$users->customer->date_of_birth));
            }
         //   dd($users->dob);

            // $orders = $this->Orders->find()
            // ->contain([
            //     'OrderItems' => [
            //         'Products' => [
            //             'Reviews' => function ($q) use ($users) {
            //                 return $q->where(['Reviews.customer_id' => $users->customer->id]);
            //             }
            //         ]
            //     ]
            // ])
            // ->where(['Orders.customer_id' => $users->customer->id])
            // ->order(['Orders.created' => 'DESC'])
            // ->limit(5)
            // ->toArray();

            // $wishlistItemsOld = $this->Wishlists->find()
            // ->contain(['Products'])
            // ->where(['Wishlists.customer_id' => $users->customer->id])
            // ->toArray();/
            $orders = $this->Orders->getCustomerRecentOrders($users->customer->id);
           // dd($orders);

            $wishlistItems = $this->Wishlists->getCustomerWishlistItems($users->customer->id);
           
            $customerAddresses = $this->CustomerAddresses->find()
            ->contain(['Countries', 'States', 'Cities'])
            ->where(['CustomerAddresses.customer_id' => $users->customer->id])
            ->toArray();

            $countries = $this->Countries->find('list', [
                'keyField' => 'id',
                'valueField' => 'name'
            ])->where(['status' => 'A'])->toArray();
  
            $this->set(compact('users', 'orders', 'wishlistItems', 'customerAddresses', 'countries'));
            $this->viewBuilder()->setTemplatePath('account');
            $this->render('account');
        }
        else{
            $this->request->getSession()->write('toast_message', [
                    'message' => __('User is not logged in.'),
                    'type' => 'error'
            ]);
            return $this->redirect(['controller' => 'Home', 'action' => 'home']);
        }
    }

    public function getStatesByCountry()
    {
        $this->request->allowMethod(['post']);
        $this->autoRender = false;
        $this->response = $this->response->withType('application/json');

        $countryId = $this->request->getData('country_id');

        $states = $this->States->find('list', [
            'keyField' => 'id',
            'valueField' => 'state_name'
        ])->where(['country_id' => $countryId, 'status' => 'A'])->toArray();

        echo json_encode(['success' => true, 'states' => $states]);
    }

    public function getCitiesByState()
    {
        $this->request->allowMethod(['post']);
        $this->autoRender = false;
        $this->response = $this->response->withType('application/json');

        $stateId = $this->request->getData('state_id');

        $cities = $this->Cities->find('list', [
            'keyField' => 'id',
            'valueField' => 'city_name'
        ])->where(['state_id' => $stateId, 'status' => 'A'])->toArray();

        echo json_encode(['success' => true, 'cities' => $cities]);
    }
    
    public function loadMoreOrders()
    {
        $this->request->allowMethod(['get']);
        $this->autoRender = false;

        try {
            // Check authentication
            $account = $this->request->getSession()->read('Auth.User');
            if (!$account) {
                $this->response = $this->response->withStatus(401);
                return $this->response->withStringBody('Unauthorized');
            }

            // Get user and customer
            $users = $this->Users->find()
                ->contain(['Customers'])
                ->where(['Users.status' => 'A', 'Users.id' => $account->id])
                ->first();

            if (!$users || !$users->customer) {
                $this->response = $this->response->withStatus(404);
                return $this->response->withStringBody('Customer not found');
            }

            // Get pagination parameters
            $offset = (int) $this->request->getQuery('offset', 0);
            $limit = 5;

            // Get orders using the existing method
            $orders = $this->Orders->getCustomerLoadMoreOrders($users->customer->id, $limit, $offset);

            if (empty($orders)) {
                return $this->response->withStringBody('');
            }

            // Set data for template and render
            $this->set(compact('orders'));
            $this->viewBuilder()->setLayout('ajax');

            // Render template and return HTML
            $view = $this->createView();
            $html = $view->render('ajax_orders');

            return $this->response->withStringBody($html);

        } catch (\Exception $e) {
            \Cake\Log\Log::error('LoadMoreOrders error: ' . $e->getMessage());
            \Cake\Log\Log::error('Stack trace: ' . $e->getTraceAsString());
            $this->response = $this->response->withStatus(500);
            return $this->response->withStringBody('Server error');
        }
    }

    public function changePassword()
    {
        if ($this->request->is('post')) {
            $data = $this->request->getData();
            $identity = $this->request->getSession()->read('Auth.User.id');

            if (!$identity) {
                $this->request->getSession()->write('toast_message', [
                            'message' => __('User is not authenticated.'),
                            'type' => 'error'
                ]);
                return $this->redirect(['controller' => 'Account', 'action' => 'myAccount', '#' => 'changePassword']);
            }

            $user = $this->Users->find()
                ->where(['id' => $identity, 'status' => 'A'])
                ->first();

            if (!$user) {
                $this->request->getSession()->write('toast_message', [
                            'message' => __('User not found or inactive.'),
                            'type' => 'error'
                ]);
                return $this->redirect(['controller' => 'Account', 'action' => 'myAccount', '#' => 'changePassword']);
            }

            $oldPassword = $user->password ?? '';

            if (!empty($user->password) && empty($data['currentPassword'])) {
                $this->request->getSession()->write('toast_message', [
                            'message' => __('Old password is required.'),
                            'type' => 'error'
                ]);
                return $this->redirect(['controller' => 'Account', 'action' => 'myAccount', '#' => 'changePassword']);
            }

            if (empty($data['newPassword']) || empty($data['confirmPassword'])) {
                $this->request->getSession()->write('toast_message', [
                    'message' => __('New password fields cannot be empty.'),
                    'type' => 'error'
                ]);
                return $this->redirect(['controller' => 'Account', 'action' => 'myAccount', '#' => 'changePassword']);
            }

            if ($data['newPassword'] !== $data['confirmPassword']) {
                $this->request->getSession()->write('toast_message', [
                    'message' => __('New password and confirm password do not match'),
                    'type' => 'error'
                ]);
                return $this->redirect(['controller' => 'Account', 'action' => 'myAccount', '#' => 'changePassword']);
            }

            // Call password change function, skipping old password check if it's empty in DB
            $changePasswordResult = $this->Users->changeUserPassword(
                $identity,
                !empty($user->password) ? $data['currentPassword'] : '', // Check old password only if it's set
                $data['newPassword']
            );

            if ($changePasswordResult['status'] === 'success') {
                $this->request->getSession()->write('toast_message', [
                            'message' => __('Password changed successfully.'),
                            'type' => 'success'
                ]);
            } else {
                $this->request->getSession()->write('toast_message', [
                    'message' => __('Old password is incorrect. Please try again.'),
                    'type' => 'error'
                ]);
            }

            return $this->redirect(['controller' => 'Account', 'action' => 'myAccount', '#' => 'changePassword']);
        }

        $this->request->getSession()->write('toast_message', [
            'message' => __('Method not allowed'),
            'type' => 'error'
        ]);
        return $this->redirect(['controller' => 'Account', 'action' => 'myAccount']);
    }

    public function updateMyAccount()
    {
        if ($this->request->is('post')) {
            $userId = $this->request->getSession()->read('Auth.User')['id'];
            $user = $this->Users->get($userId, [
                'contain' => ['Customers']
            ]);
            $data = $this->request->getData();
            
            $nameParts = explode(' ', $data['name'], 2);
            $userInfo = [
                'first_name' => trim($nameParts[0]), 
                'last_name' => trim($nameParts[1] ?? ''),
                'email' => $data['email'],
                'mobile_no' => $data['phone'],
                'country_code'=>$data['country_code']
            ];

            $this->Users->patchEntity($user, $userInfo, [
                'fields' => ['first_name', 'email', 'mobile_no','country_code']
            ]);

            if ($this->Users->save($user)) {
                // Handle profile photo upload
                $profile_photo = $data['profile_photo'] ?? null;
                $profilePhotoPath = null;

                if (!empty($profile_photo) && $profile_photo->getError() === 0) {
                    // Get file details
                    $fileName = $profile_photo->getClientFilename();
                    $rand = strtoupper(substr(uniqid(sha1((string)time()), true), -5));
                    $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                    $baseName = pathinfo($fileName, PATHINFO_FILENAME);
                    $newFileName = $baseName . '_' . $rand . '.' . $ext;
                    
                    // Set upload paths
                    $folderPath = 'uploads/profiles/';
                    $targetDir = WWW_ROOT . $folderPath;

                    // Create directory if it doesn't exist
                    if (!file_exists($targetDir)) {
                        mkdir($targetDir, 0777, true);
                    }

                    // Move uploaded file to temp location
                    $targetPath = $targetDir . $newFileName;
                    $profile_photo->moveTo($targetPath);

                    // Upload to S3 using MediaComponent
                    $s3UploadResult = $this->Media->awsUpload(
                        $targetPath, // Source file path
                        $newFileName, // Target filename
                        'profiles/', // S3 folder path
                        1
                    );

                    if ($s3UploadResult === 'Success') {
                        // Store the S3 path in database
                        $profilePhotoPath = 'profiles/' . $newFileName;

                        // Delete local temp file
                        if (file_exists($targetPath)) {
                            unlink($targetPath);
                        }
                    } else {
                        $this->request->getSession()->write('toast_message', [
                            'message' => __('Failed to upload profile photo'),
                            'type' => 'error'
                        ]);
                        return $this->redirect(['controller' => 'Account', 'action' => 'myAccount']);
                    }
                }

                $customer = $this->Customers->find()->where(['user_id' => $user->id])->first();
                if ($customer) {
                    $customerData = [];

                    // Only update profile photo if upload was successful
                    if ($profilePhotoPath) {
                        $customerData['profile_photo'] = $profilePhotoPath;
                    }

                    if ($data['gender']) {
                        $customerData['gender'] = $data['gender'];
                    }

                    if ($data['dob']) {
                        $customerData['date_of_birth'] = $data['dob'];
                    }

                    if ($data['phone']) {
                        $customerData['phone_number'] = $data['phone'];
                    }

                    $customer = $this->Customers->patchEntity($customer, $customerData);
                    if ($this->Customers->save($customer)) {
                        $this->request->getSession()->write('toast_message', [
                            'message' => __('Profile updated successfully'),
                            'type' => 'success'
                        ]);
                    } else {
                        $this->request->getSession()->write('toast_message', [
                            'message' => __('Failed to update profile'),
                            'type' => 'error'
                        ]);
                    }
                }
            }

            return $this->redirect(['controller' => 'Account', 'action' => 'myAccount']);
        }
    }

    public function removeWishlistData()
    {
        $this->request->allowMethod(['post']); // Ensure only POST requests are allowed
        if($this->request->is('post')){
            $user_id = $this->request->getSession()->read('Auth.User')['id'];
            if (!$user_id) {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'success' => false,
                    'message' => __('User not authenticated.')
                ]));
            }

            $customer_id = $this->request->getData('customer_id');
            if (!$customer_id) {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'success' => false,
                    'message' => __('Invalid Customer ID.')
                ]));
            }

            $product_id = $this->request->getData('product_id'); // Match parameter name

            if (!$product_id) {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'success' => false,
                    'message' => __('Invalid Product ID.')
                ]));
            }

            $removed = $this->Wishlists->removeFromWishlist((int)$customer_id, (int)$product_id);

            if ($removed) {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'success' => true,
                    'message' => __('Removed successfully from wishlist.')
                ]));
            } else {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'success' => false,
                    'message' => __('Failed to remove from wishlist.')
                ]));
            }
        }
    }

    public function addToCartFromWishlist()
    {
        $this->request->allowMethod(['post']);
        if ($this->request->is('post')) {
            $user_id = $this->request->getSession()->read('Auth.User')['id'];
            if (!$user_id) {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'success' => false,
                    'message' => __('User not authenticated.')
                ]));
            }
            
            $customer_id = $this->request->getData('customer_id');
            if (!$customer_id) {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'success' => false,
                    'message' => __('Invalid Customer ID.')
                ]));
            }

            $productId = $this->request->getData('product_id');
            if (!$productId) {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'success' => false,
                    'message' => __('Invalid Product ID.')
                ]));
            }

            $quantity = $this->request->getData('quantity', 1);
            $result = $this->Carts->addToCart($productId, $customer_id, $quantity, null);

            if ($result) {
                $removed = $this->Wishlists->removeFromWishlist((int)$customer_id, (int)$productId);
                
                if (!$removed) {
                    return $this->response->withType('application/json')->withStringBody(json_encode([
                        'success' => false,
                        'message' => __('Failed to remove item from wishlist after adding to cart.')
                    ]));
                }

                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'success' => true,
                    'message' => __('Product added to cart successfully.')
                ]));
            } else {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'success' => false,
                    'message' => __('Failed to add product to cart.')
                ]));
            }
        }
    }

    public function addAddress()
    {
        if ($this->request->is('post')) {
            $auth = $this->request->getSession()->read('Auth.User');
            if (!$auth) {
                $this->request->getSession()->write('toast_message', [
                    'message' => __('User Not Logged In.'),
                    'type' => 'error'
                ]);
                return $this->redirect(['controller' => 'Home', 'action' => 'home']);
            }

            $users = $this->Users->find()
                ->contain(['Customers'])
                ->where(['Users.status' => 'A'])
                ->where(['Users.id' => $auth->id])
                ->first();

            $address = $this->CustomerAddresses->newEmptyEntity();
            $data = $this->request->getData();
            $address->name = $data['name'];
            $address->type = $data['address_type'];
            $address->house_no = $data['house_no'];
            $address->address_line1 = $data['address1'];
            $address->address_line2 = $data['address2'];
            $address->landmark = $data['landmark'];
            $address->country_id = (int)$data['country'];
            $address->state_id = (int)$data['state'];
            $address->city_id = (int)$data['city'];
            $address->zipcode = $data['zip'];
            $address->customer_id = $users->customer->id;
            $address->phone_no1 = $users->customer->phone_number;
            $address->status = 'A'; // Set default status as Active

            if ($this->CustomerAddresses->save($address)) {
                $this->request->getSession()->write('toast_message', [
                    'message' => __('Address has been Saved Successfully.'),
                    'type' => 'success'
                ]);
            }
            else{
                $this->request->getSession()->write('toast_message', [
                    'message' => __('Address could not be saved.'),
                    'type' => 'error'
                ]);
            }
            return $this->redirect(['controller' => 'Account', 'action' => 'myAccount', '#' => 'billing-tab']);
        }
    }

    public function getAddressById(){
        if ($this->request->is('post')) {
            if ($this->request->is('post')) {
                $auth = $this->request->getSession()->read('Auth.User');
                if (!$auth) {
                    $this->request->getSession()->write('toast_message', [
                        'message' => __('User Not Logged In.'),
                        'success' => false
                    ]);
                    return $this->redirect(['controller' => 'Home', 'action' => 'home']);
                }

                $addressId = $this->request->getData('address_id');
                $address = $this->CustomerAddresses->find()
                    ->where(['CustomerAddresses.status' => 'A'])
                    ->where(['CustomerAddresses.id' => $addressId])
                    ->first();
                
                if (!$address) {
                    $this->request->getSession()->write('toast_message', [
                        'message' => __('Address Not Found.'),
                        'success' => false
                    ]);
                    return $this->redirect(['controller' => 'Account', 'action' => 'myAccount']);
                }
                else{
                    $this->response = $this->response->withType('application/json');
                    return $this->response->withStringBody(json_encode([
                        'success' => true,
                        'address' => $address
                    ]));
                }
            }
        }

    }

    public function editAddress()
    {
        if ($this->request->is('post')) {
                $auth = $this->request->getSession()->read('Auth.User');
                if (!$auth) {
                    $this->request->getSession()->write('toast_message', [
                        'message' => __('User Not Logged In.'),
                        'type' => 'error'
                    ]);
                    return $this->redirect(['controller' => 'Home', 'action' => 'home']);
                }

                $data = $this->request->getData();
                $address = $this->CustomerAddresses->find()->where(['id' => $data['address_id']])->first();
                
                if (!$address) {
                    $this->request->getSession()->write('toast_message', [
                        'message' => __('Address Not Found.'),
                        'type' => 'error'
                    ]);
                    return $this->redirect(['controller' => 'Account', 'action' => 'myAccount', '#' => 'billing-tab']);
                }

                $address->name = $data['name'];
                $address->type = $data['address_type'];
                $address->house_no = $data['house_no'];
                $address->address_line1 = $data['address1'];
                $address->address_line2 = $data['address2'];
                $address->landmark = $data['landmark'];
                $address->country_id = (int)$data['country'];
                $address->state_id = (int)$data['state'];
                $address->city_id = (int)$data['city'];
                $address->zipcode = $data['zip'];
                
                if ($this->CustomerAddresses->save($address)) {
                    $this->request->getSession()->write('toast_message', [
                        'message' => __('The address has been updated.'),
                        'type' => 'success'
                    ]);
                }
                else{
                    $this->request->getSession()->write('toast_message', [
                        'message' => __('The address could not be updated. Please try again.'),
                        'type' => 'error'
                    ]);
                }
                return $this->redirect(['controller' => 'Account', 'action' => 'myAccount', '#' => 'billing-tab']);
        }
    }

    public function deleteAddress()
    {
        $this->request->allowMethod(['post', 'delete']);
        $auth = $this->request->getSession()->read('Auth.User');
        if (!$auth) {
            $this->request->getSession()->write('toast_message', [
                'message' => __('User not authenticated.'),
                'success' => false
            ]);
            return $this->redirect(['controller' => 'Account', 'action' => 'myAccount', '#' => 'billing-tab']);
        }

        $address = $this->CustomerAddresses->get($this->request->getData('address_id'));
        $users = $this->Users->find()
            ->contain(['Customers'])
            ->where(['Users.status' => 'A'])
            ->where(['Users.id' => $auth->id])
            ->first();

        if ($address->customer_id !== $users->customer->id) {
            $this->request->getSession()->write('toast_message', [
                'message' => __('User not Logged In.'),
                'success' => false
            ]);
            return $this->redirect(['controller' => 'Account', 'action' => 'myAccount', '#' => 'billing-tab']);
        }

        if ($this->CustomerAddresses->delete($address)) {
            $this->request->getSession()->write('toast_message', [
                'message' => __('The address has been deleted.'),
                'success' => true
            ]);
        } else {
            $this->request->getSession()->write('toast_message', [
                'message' => __('The address could not be deleted. Please try again.'),
                'success' => false
            ]);
        }
        return $this->redirect(['controller' => 'Account', 'action' => 'myAccount', '#' => 'billing-tab']);
    }

    public function downloadInvoice($id = null)
    {
        $order = $this->Orders->get($id, [
            'contain' => [
                'Customers' => ['Users'],
                'CustomerAddresses' => ['Cities'],
                'Countries',
                'Offers',
               // 'Showrooms' => ['Cities'],
                'OrderItems' => [
                    'Products' => [
                        'ProductImages' => function ($q) {
                            return $q->where(['image_default' => 1, 'status' => 'A']);
                        }
                    ],
                    'ProductVariants' => [
                        'ProductVariantImages' => function ($q) {
                            return $q->where(['image_default' => 1, 'status' => 'A']);
                        }
                    ]
                ],
                //'Transactions'
            ]
        ]);

        $customer_care_no = $this->SiteSettings->find()
            ->select(['customer_support_no'])
            ->first()->customer_support_no;
        $customer_care_email = $this->SiteSettings->find()
            ->select(['support_email'])
            ->first()->support_email;
        $call_center_no = $this->SiteSettings->find()
            ->select(['contact_no'])
            ->first()->contact_no;
        $whatsapp_no = $this->SiteSettings->find()
            ->select(['contact_no'])
            ->first()->contact_no;
        $after_sales_no = $this->SiteSettings->find()
            ->select(['contact_no'])
            ->first()->contact_no;

        // Assign the CloudFront URL to product images
        foreach ($order->order_items as $item) {
            if (!empty($item->product->product_images)) {
                foreach ($item->product->product_images as $image) {
                    $image->image = $this->Media->getCloudFrontURL($image->image);
                }
            }
            if (!empty($item->product_variant->product_variant_images)) {
                foreach ($item->product_variant->product_variant_images as $image) {
                    $image->image = $this->Media->getCloudFrontURL($image->image);
                }
            }
        }

        $orderStatusMap = Configure::read('Constants.ORDER_STATUSES_MAP');
        $orderStatusProgress  = Configure::read('Constants.ORDER_STATUS_PROGRESS_COLOR');
        $orderStatusProgressBar  = Configure::read('Constants.ORDER_STATUS_PROGRESS_BAR');

        $paymentStatusProgress  = Configure::read('Constants.PAYMENT_STATUS_PROGRESS_COLOR');
        $paymentStatusProgressBar  = Configure::read('Constants.PAYMENT_STATUS_PROGRESS_BAR');

        $shippedStatusProgress  = Configure::read('Constants.SHIPPED_STATUS_PROGRESS_COLOR');
        $shippedStatusProgressBar  = Configure::read('Constants.SHIPPED_STATUS_PROGRESS_BAR');

        $deliveryStatusProgress  = Configure::read('Constants.DELIVERY_STATUS_PROGRESS_COLOR');
        $deliveryStatusProgressBar  = Configure::read('Constants.DELIVERY_STATUS_PROGRESS_BAR');

        $orderStatus = Configure::read('Constants.ORDER_STATUSES');

        // Get currency based on country_id from order through currencies table
        $currencySymbol = '';
        $currencyCode = '';

        if (!empty($order->country_id)) {
            // Get currency from the currencies table using country_id
            $currenciesTable = $this->getTableLocator()->get('Currencies');
            $currency = $currenciesTable->find()
                ->where(['country_id' => $order->country_id])
                ->first();

            if ($currency) {
                $currencyCode = $currency->code ?? '';
                $currencySymbol = $currency->symbol ?? $currency->code ?? '';
            }
        }

        // Fallback to configuration if no currency found from country
        if (empty($currencySymbol)) {
            $currencyConfig = Configure::read('Settings.Currency.format');
            $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        }

        $orderItemCount = $this->Orders->OrderItems->find()
            ->where(['order_id' => $id])
            ->count();

        // $this->log('test'.$orderStatusProgress,'debug');

      
        $this->set(compact('order', 'currencySymbol', 'currencyCode', 'orderItemCount', 'orderStatusMap', 'orderStatus', 'orderStatusProgress', 'orderStatusProgressBar', 'paymentStatusProgressBar', 'paymentStatusProgress', 'shippedStatusProgress', 'shippedStatusProgressBar', 'deliveryStatusProgress', 'deliveryStatusProgressBar', 'customer_care_no', 'customer_care_email', 'call_center_no', 'whatsapp_no', 'after_sales_no'));
        $this->render('/account/invoice');
    }

    public function printInvoicePdf()
    {
        // Increase execution time and memory limit for PDF generation
        ini_set('max_execution_time', 300); // 5 minutes
        ini_set('memory_limit', '512M');

        $this->request->allowMethod(['post']); // Only allow POST requests
        $htmlContent = $this->request->getData('html');

        if (empty($htmlContent)) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => 'error',
                'message' => 'No HTML content received'
            ]));
        }

        try {
            // Initialize MPDF with optimized settings
            $mpdf = new Mpdf([
                'mode' => 'utf-8',
                'format' => 'A4',
                'orientation' => 'P',
                'margin_left' => 15,
                'margin_right' => 15,
                'margin_top' => 16,
                'margin_bottom' => 16,
                'margin_header' => 9,
                'margin_footer' => 9,
                'tempDir' => TMP . 'mpdf'
            ]);

            // Only load essential CSS to reduce processing time
            $essentialCss = '
                body { font-family: Arial, sans-serif; font-size: 12px; }
                table { width: 100%; border-collapse: collapse; }
                th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
                .text-center { text-align: center; }
                .text-right { text-align: right; }
                .font-weight-bold { font-weight: bold; }
            ';

            // Apply minimal styles for faster processing
            $mpdf->WriteHTML($essentialCss, \Mpdf\HTMLParserMode::HEADER_CSS);
            $mpdf->WriteHTML($htmlContent, \Mpdf\HTMLParserMode::HTML_BODY);

            // Output PDF
            $pdfFileName = "Invoice_" . time() . ".pdf";
            $mpdf->Output($pdfFileName, "I"); // Open in browser

        } catch (\Exception $e) {
            // Log the error for debugging
            $this->log('PDF Generation Error: ' . $e->getMessage(), 'error');

            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => 'error',
                'message' => 'PDF generation failed: ' . $e->getMessage()
            ]));
        }

        return $this->response->withType('application/pdf');
    }

    public function rateProduct()
    {
        $this->request->allowMethod(['post']);
        if ($this->request->is('post')) {
            $data = $this->request->getData();
            $product_id = $data['product_id'];
            $rating = $data['rating'];
            $review = $data['review'];
            $user_id = $this->request->getSession()->read('Auth.User')['id'];
            $users = $this->Users->find()
            ->contain(['Customers'])
            ->where(['Users.status' => 'A'])
            ->where(['Users.id' => $user_id])
            ->first();

            if (!$users) {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => 'User is not authenticated'
                ]));
            }
            $reviewData = [
                'product_id' => $product_id,
                'customer_id' => $users->customer->id,
                'rating' => $rating,
                'comment' => $review,
                'status' => 'A'
            ];

            $reviewEntity = $this->Reviews->newEntity($reviewData);
            if ($this->Reviews->save($reviewEntity)) {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'status' => 'success',
                    'message' => 'Review added successfully'
                ]));
            } else {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => 'Failed to add review',
                    'errors' => $reviewEntity->getErrors()
                ]));
            }
        }
    }

    public function cancelOrder(){
        $this->request->allowMethod(['post']);
        if ($this->request->is('post')) {
            $user_id = $this->request->getSession()->read('Auth.User')['id'];
            $users = $this->Users->find()
            ->contain(['Customers'])
            ->where(['Users.status' => 'A'])
            ->where(['Users.id' => $user_id])
            ->first();

            if (!$users) {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => 'User is not authenticated'
                ]));
            }

            $data = $this->request->getData();
            $order_id = $data['order_id'];
            $order = $this->Orders->find()
            ->contain(['OrderItems'])
            ->where(['id' => $order_id])
            ->first();

            if (!$order) {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => 'Order not found'
                ]));
            }

            $order->status = 'Cancellation Requested'; // 
            if ($this->Orders->save($order)) {
                // Get a default cancellation category
                // $defaultCategory = $this->OrderCancellationCategories->find()
                //     ->first();
                
                // if (!$defaultCategory) {
                //     return $this->response->withType('application/json')->withStringBody(json_encode([
                //         'status' => 'error',
                //         'message' => 'No cancellation category found'
                //     ]));
                // }

                $orderCancelData = [
                    'order_id' => $order->id,
                    'customer_id' => $users->customer->id,
                    'order_item_id' => $order->order_items[0]->id,
                   // 'order_cancellation_category_id' => $defaultCategory->id,
                    'status' => 'Pending',
                    'reason' => $data['reason'],
                    'canceled_at' => Date('Y-m-d H:i:s')
                ];

                $orderCancelEntity = $this->OrderCancellations->newEntity($orderCancelData);
                if ($this->OrderCancellations->save($orderCancelEntity)) {
                    return $this->response->withType('application/json')->withStringBody(json_encode([
                        'status' => 'success',
                        'message' => 'Order cancelled successfully'
                    ]));
                }
                else{
                    return $this->response->withType('application/json')->withStringBody(json_encode([
                        'status' => 'error',
                        'message' => 'Failed to cancel order',
                        'errors' => $orderCancelEntity->getErrors()
                    ]));
                }
            } else {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => 'Failed to cancel order'
                ]));
            }
        }
    }

    public function returnOrder(){
        $this->request->allowMethod(['post']);
        if ($this->request->is('post')) {
            $user_id = $this->request->getSession()->read('Auth.User')['id'];
            $users = $this->Users->find()
            ->contain(['Customers'])
            ->where(['Users.status' => 'A'])
            ->where(['Users.id' => $user_id])
            ->first();

            if (!$users) {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => 'User is not authenticated'
                ]));
            }

            $data = $this->request->getData();
            $order_id = $data['order_id'];
            $reason = $data['reason'];

            $order = $this->Orders->find()
            ->contain(['OrderItems'])
            ->where(['id' => $order_id])
            ->first();

            if (!$order) {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => 'Order not found'
                ]));
            }

            $order->status = 'Return Requested';
            if ($this->Orders->save($order)) {
                $orderReturnData = [
                    'order_id' => $order->id,
                    'customer_id' => $users->customer->id,
                    'order_item_id' => $order->order_items[0]->id,
                    'reason' => $reason,
                    'status' => 'Pending',
                    'return_amount'=> $order->total_amount, // Assuming 
                    'requested_at' => date('Y-m-d H:i:s')
                ];

                $orderReturnEntity = $this->OrderReturns->newEntity($orderReturnData);
                if ($this->OrderReturns->save($orderReturnEntity)) {
                    return $this->response->withType('application/json')->withStringBody(json_encode([
                        'status' => 'success',
                        'message' => 'Order Return requested successfully'
                    ]));
                }
                else{
                    return $this->response->withType('application/json')->withStringBody(json_encode([
                        'status' => 'error',
                        'message' => 'Failed to cancel order',
                        'errors' => $orderReturnEntity->getErrors()
                    ]));
                }
            } else {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => 'Failed to request Return'
                ]));
            }
        }
    }

    public function getCancellationCategories()
    {
        $this->request->allowMethod(['get']);

        try {
            $categories = $this->OrderCancellationCategories->find('all')
                ->select(['id', 'name'])
                ->where(['status' => 'A'])
                ->order(['name' => 'ASC'])
                ->toArray();

            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => 'success',
                'categories' => $categories
            ]));
        } catch (\Exception $e) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => 'error',
                'message' => __('Failed to load cancellation categories')
            ]));
        }
    }

    public function cancelOrderWithReason()
    {
        $this->request->allowMethod(['post']);

        if (!$this->request->is('post')) {
            return $this->response->withType('application/json')->withStatus(405)
                ->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => __('Method not allowed')
                ]));
        }

        $data = $this->request->getData();
        $orderId = $data['order_id'] ?? null;

        if (!$orderId) {
            return $this->response->withType('application/json')->withStatus(400)
                ->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => __('Order ID is required')
                ]));
        }

        // Get authenticated user
        $identity = $this->request->getSession()->read('Auth.User');
        if (!$identity) {
            return $this->response->withType('application/json')->withStatus(401)
                ->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => __('User not authenticated')
                ]));
        }

        try {
            // Get order with customer verification
            $order = $this->Orders->get($orderId, [
                'contain' => ['OrderItems', 'Customers']
            ]);

            $users = $this->Users->find()
                ->contain(['Customers'])
                ->select(['Users.id'])
                ->where(['Users.status' => 'A'])
                ->where(['Users.id' => $identity->id])
                ->first();

            $customerId = $users->customer->id;

            // Verify order ownership
            if ($order->customer_id !== $customerId) {
                return $this->response->withType('application/json')->withStatus(403)
                    ->withStringBody(json_encode([
                        'status' => 'error',
                        'message' => __('Unauthorized to cancel this order')
                    ]));
            }

            // Check if order can be cancelled
            if ($order->payment_method == 'Credit') {
                return $this->response->withType('application/json')->withStatus(400)
                    ->withStringBody(json_encode([
                        'status' => 'error',
                        'message' => __('The order cannot be canceled as it was made on credit')
                    ]));
            }

            // Check if any items are shipped/delivered
            foreach ($order->order_items as $item) {
                if (in_array($item->status, ['Shipped', 'Delivered'])) {
                    return $this->response->withType('application/json')->withStatus(400)
                        ->withStringBody(json_encode([
                            'status' => 'error',
                            'message' => __('Cannot cancel order as one or more items have been shipped/delivered')
                        ]));
                }
            }

            // Check for existing cancellation
            $existingCancellation = $this->OrderCancellations->find()
                ->where(['order_id' => $orderId])
                ->first();

            if ($existingCancellation) {
                $messages = [
                    'Pending' => __('Cancellation has already been initiated'),
                    'Approved' => __('Cancellation is already in progress'),
                    'Completed' => __('This order has already been cancelled')
                ];

                return $this->response->withType('application/json')->withStatus(400)
                    ->withStringBody(json_encode([
                        'status' => 'error',
                        'message' => $messages[$existingCancellation->status] ?? __('Order cancellation issue')
                    ]));
            }

            // Create cancellation record
            $cancellationData = [
                'order_id' => $orderId,
                'customer_id' => $customerId,
                'order_item_id' => $order->order_items[0]->id ?? null,
                'order_cancellation_category_id' => $data['reason_id'],
                'reason' => $data['reason'] ?? null,
                'status' => 'Pending',
                'canceled_at' => date('Y-m-d H:i:s')
            ];

            // Update order status
            $order->status = 'Pending Cancellation';
            if (!$this->Orders->save($order)) {
                return $this->response->withType('application/json')->withStatus(500)
                    ->withStringBody(json_encode([
                        'status' => 'error',
                        'message' => __('Failed to update order status')
                    ]));
            }

            // Save cancellation record
            $cancellation = $this->OrderCancellations->newEntity($cancellationData);
            if (!$this->OrderCancellations->save($cancellation)) {
                return $this->response->withType('application/json')->withStatus(500)
                    ->withStringBody(json_encode([
                        'status' => 'error',
                        'message' => __('Failed to create cancellation record')
                    ]));
            }

            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => 'success',
                'message' => __('Cancellation request submitted successfully. We will review your request and get back to you soon.')
            ]));

        } catch (\Exception $e) {
            return $this->response->withType('application/json')->withStatus(500)
                ->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => __('An error occurred while processing your cancellation request')
                ]));
        }
    }

    public function checkPassword($storedPassword, $inputPassword)
    {
        $hasher = new \Cake\Auth\DefaultPasswordHasher();
        return $hasher->check($inputPassword, $storedPassword);
    }


    public function addWishlist()
    {

        $identity = $this->request->getSession()->read('Auth.User');

        if (!$identity) {
            $result = ['status' => __('error'), 'message' => __('You must be logged in to add items to your wishlist.')];
        } else {
            $users = $this->Users->find()
                ->contain([
                    'Customers' => function ($q) {
                        return $q->select(['id']); // Select only the Customer.id field
                    }
                ])
                ->select(['Users.id']) // Select the necessary fields from Users
                ->where(['Users.status' => 'A'])
                ->where(['Users.id' => $identity->id])
                ->first();

            $customer_id = $users->customer->id;
            $data = $this->request->getData();
            $product_id = $data['product_id'];
            $result = $this->Wishlists->addToWishlist($customer_id, $product_id);
            if (!$result) {
                $result = ['status' => __('error'), 'message' => __('Please try again!')];
            }
        }
        $this->response = $this->response->withType('application/json');

        return $this->response->withStringBody(json_encode($result));
    }

    public function removeWishlist()
    {
        $identity = $this->request->getSession()->read('Auth.User');

        if (!$identity) {
            $result = ['status' => __('error'), 'message' => __('User is not authenticated')];
        } else {
            $users = $this->Users->find()
                ->contain([
                    'Customers' => function ($q) {
                        return $q->select(['id']); // Select only the Customer.id field
                    }
                ])
                ->select(['Users.id']) // Select the necessary fields from Users
                ->where(['Users.status' => 'A'])
                ->where(['Users.id' => $identity->id])
                ->first();

            $customer_id = $users->customer->id;
            $data = $this->request->getData();
            $product_id = $data['product_id'];
            $result = $this->Wishlists->removeFromWishlist($customer_id, $product_id);
            if ($result) {
                $result = ['status' => __('success'), 'message' => __('Item removed successfully.')];
            } else {
                $result = ['status' => __('error'), 'message' => __('Item already removed.')];
            }
        }
        $this->response = $this->response->withType('application/json');

        return $this->response->withStringBody(json_encode($result));
    }


    public function addToCart222()
    {
        $this->response = $this->response->withType('application/json');
        if (!$this->request->is('post')) {
            return $this->response->withStringBody(json_encode([
                'status' => __('error'),
                'message' => __('Method Not Allowed')
            ]));
        }

        $data = $this->request->getData();
        $guestToken = $this->request->getSession()->read('cartId') ?? null;
        $identity = $this->request->getSession()->read('Auth.User');

        // Generate Guest Token if not logged in and no guest-token exists
        if (!$identity && !$guestToken) {
            $guestToken = Text::uuid();
            $this->request->getSession()->write('cartId', $guestToken);
        }

        // Validate Required Fields
        $requiredFields = ['product_id', 'quantity'];
        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                return $this->response->withStringBody(json_encode([
                    'status' => __('error'),
                    'code' => 400,
                    'message' => __("Field '{0}' is required", $field)
                ]));
            }
        }

        // Determine Customer ID or Use Guest Token
        $customerId = $identity ? $identity->id : null;
        if (!empty($customerId)) {
            $user = $this->Users->find()
                ->contain(['Customers'])
                ->where(['Users.status' => 'A', 'Users.id' => $customerId])
                ->first();
            $customerId = $user->customer->id ?? null;
        }

        // Find or Create Cart
        $cartConditions = $customerId ? ['customer_id' => $customerId] : ['guest_token' => $guestToken];
        $cart = $this->Carts->find()->where($cartConditions)->first();
        if (!$cart) {
            $cart = $this->Carts->newEntity([
                'customer_id' => $customerId,
                'guest_token' => $guestToken,
            ]);
            if (!$this->Carts->save($cart)) {
                return $this->response->withStringBody(json_encode([
                    'status' => __('error'),
                    'code' => 400,
                    'message' => __('Failed to create cart')
                ]));
            }
        }

        // Start Transaction
        $connection = $this->CartItems->getConnection();
        $connection->begin();

        try {
            // Check if item already exists
            $conditions = ['cart_id' => $cart->id, 'product_id' => $data['product_id']];
            if (!empty($data['product_variant_id'])) {
                $conditions['product_variant_id'] = $data['product_variant_id'];
            }

            $existingItem = $this->CartItems->find()->where($conditions)->first();

            if ($existingItem) {
                $existingItem->quantity += $data['quantity'];
                if (!$this->CartItems->save($existingItem)) {
                    throw new \Exception(__('Failed to update cart item'));
                }
            } else {
                $newItemData = [
                    'cart_id' => $cart->id,
                    'product_id' => $data['product_id'],
                    'quantity' => $data['quantity'],
                ];
                if (!empty($data['product_variant_id'])) {
                    $newItemData['product_variant_id'] = $data['product_variant_id'];
                }

                $newItem = $this->CartItems->newEntity($newItemData);
                if (!$this->CartItems->save($newItem)) {
                    throw new \Exception(__('Failed to add cart item'));
                }
            }

            // Get Cart Item ID
            $cartItemId = $existingItem->id ?? $newItem->id;

            // Insert Attributes If Available
            if (!empty($data['attributes']) && is_array($data['attributes'])) {
                foreach ($data['attributes'] as $attributeId) {
                    $cartItemAttribute = $this->CartItemAttributes->newEntity([
                        'cart_id' => $cart->id,
                        'cart_item_id' => $cartItemId,
                        'product_id' => $data['product_id'],
                        'product_attribute_id' => $attributeId,
                        'status' => 1, // Active
                    ]);

                    if (!$this->CartItemAttributes->save($cartItemAttribute)) {
                        throw new \Exception(__('Failed to save cart item attributes'));
                    }
                }
            }

            // Commit Transaction
            $connection->commit();

            return $this->response->withStringBody(json_encode([
                'status' => __('success'),
                'code' => 200,
                'message' => __('Cart item added successfully'),
                'cart_item_id' => $cartItemId,
                'cart_id' => $cart->id,
                'guest_token' => $guestToken
            ]));

        } catch (\Exception $e) {
            $connection->rollback();
            return $this->response->withStringBody(json_encode([
                'status' => __('error'),
                'code' => 400,
                'message' => $e->getMessage()
            ]));
        }
    }

    public function cart()
    {

        $data = $this->request->getData();
        $guestToken = $this->request->getSession()->read('cartId') ?? null; // 7d1c54ba-05d8-4a5b-bad4-ed4381486858
        $totalSalePrice = 0;
        $customerAddress = '';
        $loyaltyDetails = "";
        $identity = $this->request->getSession()->read('Auth.User');
        // Generate Guest Token if not logged in and no guest-token exists
        if (!$identity && !$guestToken) {
            $guestToken = Text::uuid();
            $this->request->getSession()->write('cartId', $guestToken);
        }

        // Determine Customer ID or Use Guest Token
        $customerId = $identity ? $identity->id : null;
        if (!empty($customerId)) {
            $users = $this->Users->find()
                ->contain(['Customers']) // Include related Customers
                ->where(['Users.status' => 'A'])
                ->where(['Users.id' => $customerId])
                ->first();
            $customerId = $users->customer->id;
            $customerAddress = $this->CustomerAddresses->listAddress($customerId);
        }

        if (isset($customerId) && !empty($customerId)) {
            $loyaltyDetails = $this->Loyalty->calculateLoyaltyPoints($customerId);
        }
        // Find or Create Cart
        $cartConditions = $customerId
            ? ['customer_id' => $customerId]
            : ['guest_token' => $guestToken];

        $cart = $this->Carts->find()
            ->where($cartConditions)
            ->contain(['CartItems' => ['Products']])  // ,'ProductVariants'
            ->first();

        $totalPrice = 0;
        $totalDiscountedPrice = 0;
        $cartItems = [];
        $cartOutItems = [];

        if ($cart) {

            foreach ($cart->cart_items as $item) {

                $price = null;
                $saleprice = null;
                if ($item->product_variant_id) {
                    $productVariant = $this->CartItems->ProductVariants->find()
                        ->select(['promotion_price', 'sales_price'])
                        ->where(['id' => $item->product_variant_id])
                        ->first();
                    $price = $productVariant ? $productVariant->promotion_price : null;
                    $saleprice = $productVariant ? $productVariant->sales_price : null;
                }else{
                    $price = $this->Products->getProductPrice($item->product_id);
                }

                if (!$price) {
                    $product = $this->CartItems->Products->find()
                        ->select(['promotion_price', 'sales_price'])
                        ->where(['id' => $item->product_id])
                        ->first();
                    $price = $product ? $product->promotion_price : null;
                    $saleprice = $product ? $product->sales_price : null;
                }



                if ($price !== null) {
                    $image = $this->ProductImages->getDefaultProductImage($item->product_id);
                    $getAvailableStatus = $this->Products->getAvailabilityStatus($item->product_id);
                    if ($image) {
                        $image = $this->Media->getCloudFrontURL($image);
                    }

                    if ($getAvailableStatus == "In Stock") {
                        $salepriceWithCheckVariant = $saleprice ? $saleprice : $item->product->sales_price;

                        $totalPrice = $totalPrice + number_format($item->quantity * $price, 2, '.', '');
                        $totalSalePrice = $totalSalePrice + ($totalDiscountedPrice + number_format($item->quantity * $salepriceWithCheckVariant, 2, '.', ''));
                        $cartItems[] = [
                            'sale' => $saleprice ? $saleprice : $item->product->sales_price,
                            'discount' => $this->Products->getDiscountProduct($item->product_id, $item->product_variant_id),
                            'product_image' => $image,
                            'cart_item_id' => $item->id,
                            'product_id' => $item->product_id,
                            'product_variant_id' => $item->product_variant_id,
                            'product_name' => $item->product->name,
                            'variant_name' => $item->product_variant->name ?? $item->product->name,
                            'quantity' => $item->quantity,
                            'price' => $price,
                            'sale_price' => number_format($item->quantity * $saleprice, 2, '.', ''),
                            'get_available_status' => $getAvailableStatus,
                            'total_price' => number_format($item->quantity * $price, 2, '.', '')
                        ];
                    } else {
                        $cartOutItems[] = [
                            'sale' => $saleprice ? $saleprice : $item->product->sales_price,
                            'discount' => $this->Products->getDiscountProduct($item->product_id),
                            'product_image' => $image,
                            'cart_item_id' => $item->id,
                            'product_id' => $item->product_id,
                            'product_variant_id' => $item->product_variant_id,
                            'product_name' => $item->product->name,
                            'variant_name' => $item->product_variant->name ?? $item->product->name,
                            'quantity' => $item->quantity,
                            'price' => $price,
                            'sale_price' => number_format($item->quantity * $saleprice, 2, '.', ''),
                            'get_available_status' => $getAvailableStatus,
                            'total_price' => number_format($item->quantity * $price, 2, '.', '')
                        ];
                    }
                }
            }
        }
        $cart_id = $cart->id ?? 0;
        $total_items = count($cartItems);

        $totalDiscountedPrice = 0;
        //        if ($totalSalePrice >= $totalPrice) {
        $totalDiscountedPrice = $totalSalePrice - $totalPrice;
        //        }

        $checkCartCount = $total_items + count($cartOutItems);



        $this->set(compact('checkCartCount','customerAddress', 'customerId', 'loyaltyDetails', 'totalDiscountedPrice', 'totalPrice', 'cartOutItems', 'cartItems', 'cart_id', 'total_items'));
        $this->viewBuilder()->setTemplatePath('carts');
        $this->render('cart');
    }

    public function updateCartItem()
    {
        $this->response = $this->response->withType('application/json');

        // Validate HTTP Method
        if (!$this->request->is(['patch', 'post', 'put'])) {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            return $this->response->withStatus(405)->withStringBody(json_encode($result));
        }

        $data = $this->request->getData();
        $guestToken = $this->request->getSession()->read('cartId') ?? null;
        $identity = $this->request->getSession()->read('Auth.User');

        // Determine Customer ID or Use Guest Token
        $customerId = $identity ? $identity->id : null;
        if (!empty($customerId)) {
            $users = $this->Users->find()
                ->contain(['Customers']) // Include related Customers
                ->where(['Users.status' => 'A'])
                ->where(['Users.id' => $customerId])
                ->first();
            $customerId = $users->customer->id;
        }

        if (!$customerId && !$guestToken) {
            $result = [
                'status' => __('error'),
                'code' => 400,
                'message' => __('Customer ID or Guest Token is required')
            ];
            return $this->response->withStatus(400)->withStringBody(json_encode($result));
        }

        // Validate Input Data
        if (empty($data['cart_item_id']) || empty($data['quantity'])) {
            $result = [
                'status' => __('error'),
                'code' => 400,
                'message' => __('Cart item ID and quantity are required')
            ];
            return $this->response->withStatus(400)->withStringBody(json_encode($result));
        }

        $cartConditions = $customerId
            ? ['customer_id' => $customerId]
            : ['guest_token' => $guestToken];

        // Find Cart
        $cart = $this->Carts->find()->where($cartConditions)->first();
        if (!$cart) {
            $result = [
                'status' => __('error'),
                'code' => 404,
                'message' => __('Cart not found')
            ];
            return $this->response->withStatus(404)->withStringBody(json_encode($result));
        }

        $connection = $this->CartItems->getConnection();
        $connection->begin();
        try {
            // Find Cart Item
            $cartItem = $this->CartItems->find()
                ->where([
                    'cart_id' => $cart->id,
                    'id' => $data['cart_item_id']
                ])
                ->first();

            if (!$cartItem) {
                $result = [
                    'status' => __('error'),
                    'code' => 404,
                    'message' => __('Cart item not found')
                ];
                return $this->response->withStatus(404)->withStringBody(json_encode($result));
            }

            // Update Quantity
            $cartItem->quantity = $data['quantity'];

            if (!$this->CartItems->save($cartItem)) {
                $result = [
                    'status' => __('error'),
                    'code' => 500,
                    'message' => __('Failed to update cart item')
                ];
                return $this->response->withStatus(500)->withStringBody(json_encode($result));
            }

            // Retrieve Price
            $price = null;
            if ($cartItem->product_variant_id) {
                $productVariant = $this->CartItems->ProductVariants->find()
                    ->select(['promotion_price'])
                    ->where(['id' => $cartItem->product_variant_id])
                    ->first();

                $price = $productVariant ? $productVariant->promotion_price : null;
            }

            if (!$price) {
                $product = $this->CartItems->Products->find()
                    ->select(['promotion_price'])
                    ->where(['id' => $cartItem->product_id])
                    ->first();

                $price = $product ? $product->promotion_price : null;
            }

            if ($price !== null) {
                $cartItem->price = $price;
                $cartItem->total_price = number_format($cartItem->quantity * $price, 2, '.', '');
            } else {
                $result = [
                    'status' => __('error'),
                    'code' => 400,
                    'message' => __('Price not found for the selected product/variant')
                ];
                return $this->response->withStatus(400)->withStringBody(json_encode($result));
            }

            $connection->commit();

            $result = [
                'status' => __('success'),
                'code' => 200,
                'message' => __('Cart item updated successfully'),
                'data' => $cartItem
            ];
            return $this->response->withStringBody(json_encode($result));
        } catch (\Exception $e) {
            $connection->rollback();
            $result = [
                'status' => __('error'),
                'code' => 500,
                'message' => __('Failed to update cart')
            ];
            return $this->response->withStatus(500)->withStringBody(json_encode($result));
        }
    }

    public function deleteCartItem()
    {
        $this->response = $this->response->withType('application/json');

        // Validate HTTP Method
        if (!$this->request->is(['patch', 'post', 'put'])) {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            return $this->response->withStatus(405)->withStringBody(json_encode($result));
        }

        $data = $this->request->getData();
        $guestToken = $this->request->getSession()->read('cartId') ?? null;
        $identity = $this->request->getSession()->read('Auth.User');

        // Determine Customer ID or Use Guest Token
        $customerId = $identity ? $identity->id : null;
        if (!empty($customerId)) {
            $users = $this->Users->find()
                ->contain(['Customers']) // Include related Customers
                ->where(['Users.status' => 'A'])
                ->where(['Users.id' => $customerId])
                ->first();
            $customerId = $users->customer->id;
        }

        if (!$customerId && !$guestToken) {
            $result = [
                'status' => __('error'),
                'code' => 400,
                'message' => __('Customer ID or Guest Token is required')
            ];
            return $this->response->withStatus(400)->withStringBody(json_encode($result));
        }

        // Validate cart item ID
        if (empty($data['cart_item_id'])) {
            $result = [
                'status' => __('error'),
                'code' => 400,
                'message' => __('Cart Item ID is required')
            ];
            return $this->response->withStatus(400)->withStringBody(json_encode($result));
        }

        $cartConditions = $customerId
            ? ['customer_id' => $customerId]
            : ['guest_token' => $guestToken];

        // Fetch the cart
        $cart = $this->Carts->find()->where($cartConditions)->first();
        if (!$cart) {
            $result = [
                'status' => __('error'),
                'code' => 400,
                'message' => __('Cart not found')
            ];
            return $this->response->withStatus(400)->withStringBody(json_encode($result));
        }

        // Fetch the cart item
        $cartItem = $this->CartItems->find()
            ->where([
                'cart_id' => $cart->id,
                'id' => $data['cart_item_id']
            ])
            ->first();

        if (!$cartItem) {
            $result = [
                'status' => __('error'),
                'code' => 400,
                'message' => __('Cart item not found or invalid cart item ID')
            ];
            return $this->response->withStatus(400)->withStringBody(json_encode($result));
        }

        // Attempt to delete the cart item
        if (!$this->CartItems->delete($cartItem)) {
            $result = [
                'status' => __('error'),
                'code' => 400,
                'message' => __('Failed to delete cart item')
            ];
            return $this->response->withStatus(400)->withStringBody(json_encode($result));
        }
        $result = [
            'status' => __('success'),
            'code' => 200,
            'message' => __('Cart item deleted successfully')
        ];
        return $this->response->withStatus(200)->withStringBody(json_encode($result));
    }

    public function checkLoyaltyPoint()
    {
        $this->response = $this->response->withType('application/json');

        if (!$this->request->is('post')) {
            $result = ['status' => __('error'), 'message' => __('Method Not Allowed')];
            return $this->response->withStringBody(json_encode($result));
        }

        try {
            $identity = $this->request->getSession()->read('Auth.User');

            // Determine Customer ID or Use Guest Token
            $customerId = $identity ? $identity->id : null;

            if (!empty($customerId)) {
                $users = $this->Users->find()
                    ->contain(['Customers']) // Include related Customers
                    ->where(['Users.status' => 'A'])
                    ->where(['Users.id' => $customerId])
                    ->first();


                if (!$users || empty($users->customer)) {
                    throw new \Exception(__('Customer not found.'));
                }

                $customerId = $users->customer->id;
            }

            if (isset($customerId) && !empty($customerId)) {
                $loyaltyDetails = $this->Loyalty->calculateLoyaltyPoints($customerId);
            } else {
                throw new \Exception(__('Login rquired to check points.'));
            }
            if (empty($loyaltyDetails)) {
                throw new \Exception(__('Failed to retrieve loyalty details.'));
            }
            $result = $loyaltyDetails;
        } catch (\Exception $e) {
            $result = [
                'status' => __('error'),
                'code' => 500,
                'message' => $e->getMessage(),
                'data' => null,
            ];
        }

        return $this->response->withStringBody(json_encode($result));
    }

    public function applyofferCheck($coupon_code, $subTotal)
    {

        $this->response = $this->response->withType('application/json');
        try {
            $identity = $this->request->getSession()->read('Auth.User');
            $customerId = $identity ? $identity->id : null;

            if (!empty($customerId)) {
                $users = $this->Users->find()
                    ->contain(['Customers']) // Include related Customers
                    ->where(['Users.status' => 'A'])
                    ->where(['Users.id' => $customerId])
                    ->first();

                if (!$users || empty($users->customer)) {
                    throw new \Exception(__('Customer not found.'));
                }
                $customerId = $users->customer->id;
            }

            $couponCode = $coupon_code;
            $subTotal = $subTotal;

            $orderDate = date('Y-m-d H:i:s');

            $offerQuery = $this->Offers->find()
                ->where([
                    'offer_code' => $couponCode,
                    'status' => 'A',
                    'redeem_mode IN' => ['Online', 'Both'],
                    'offer_start_date <=' => $orderDate,
                    'AND' => [
                        'offer_end_date IS NOT' => null,
                        'offer_end_date >=' => $orderDate,
                        'OR' => [
                            ['min_cart_value IS' => null],
                            ['min_cart_value' => 0],
                            ['min_cart_value >' => 0, 'min_cart_value <=' => $subTotal]
                        ]
                    ]
                ]);
            $offer = $offerQuery->first();

            if (!$offer) {
                $result = [
                    'status' => __('error'),
                    'code' => 400,
                    'message' => __('No offer found matching the given coupon code.'),
                ];
                return $result;
            }

            $discountedAmount = $this->getDiscountAmount($subTotal, $offer->offer_type, $offer->discount, $offer->max_amt_per_disc_value);

            $result = [
                'status' => __('success'),
                'code' => 200,
                'message' => __('Coupon applied successfully'),
                'data' => ['coupon_amount' => $discountedAmount, 'offer_id' => $offer->id],
            ];
            return $result;

        } catch (\Exception $e) {
            // Catch any exceptions that occur during processing
            $result = [
                'status' => __('error'),
                'message' => $e->getMessage(), // Display the error message
            ];

            return $result;
        }

    }

    public function applyoffer()
    {
        if ($this->request->is('post')) {
            $this->response = $this->response->withType('application/json');
            try {
                $identity = $this->request->getSession()->read('Auth.User');


                $customerId = $identity ? $identity->id : null;

                if (!empty($customerId)) {
                    $users = $this->Users->find()
                        ->contain(['Customers']) // Include related Customers
                        ->where(['Users.status' => 'A'])
                        ->where(['Users.id' => $customerId])
                        ->first();

                    if (!$users || empty($users->customer)) {
                        throw new \Exception(__('Customer not found.'));
                    }
                    $customerId = $users->customer->id;
                }

                $couponCode = $this->request->getData('coupon_code');
                $subTotal = $this->request->getData('subTotal');

                $orderDate = date('Y-m-d H:i:s');

                $offerQuery = $this->Offers->find()
                    ->where([
                        'offer_code' => $couponCode,
                        'status' => 'A',
                        'redeem_mode IN' => ['Online', 'Both'],
                        'offer_start_date <=' => $orderDate,
                        'AND' => [
                            'offer_end_date IS NOT' => null,
                            'offer_end_date >=' => $orderDate,
                            'OR' => [
                                ['min_cart_value IS' => null],
                                ['min_cart_value' => 0],
                                ['min_cart_value >' => 0, 'min_cart_value <=' => $subTotal]
                            ]
                        ]
                    ]);
                $offer = $offerQuery->first();

                if (!$offer) {
                    $result = [
                        'status' => __('error'),
                        'code' => 400,
                        'message' => __('No offer found matching the given coupon code.'),
                    ];
                    return $this->response->withStringBody(json_encode($result));
                }

                $discountedAmount = $this->getDiscountAmount($subTotal, $offer->offer_type, $offer->discount, $offer->max_amt_per_disc_value);

                $result = [
                    'status' => __('success'),
                    'code' => 200,
                    'message' => __('Coupon applied successfully'),
                    'data' => ['coupon_amount' => $discountedAmount],
                ];
                return $this->response->withStringBody(json_encode($result));

            } catch (\Exception $e) {
                // Catch any exceptions that occur during processing
                $result = [
                    'status' => __('error'),
                    'message' => $e->getMessage(), // Display the error message
                ];
                $this->response = $this->response->withStatus(400); // Bad Request status code
                return $this->response->withStringBody(json_encode($result));
            }
        }
    }


    public function getDiscountAmount($cartAmount, $offerType, $discountAmount, $maxDiscount = null)
    {
        // Check if the cart amount is greater than the given amount
        if ($cartAmount <= $discountAmount) {
            throw new \Exception("Cart amount must be greater than the given amount.");
        }

        // Check offer type: Percentage or Flat
        if ($offerType === 'Percentage') {
            // Check if max discount is provided and ensure the discount is within limits
            if ($maxDiscount !== null && $discountAmount > $maxDiscount) {
                throw new \Exception("The discount exceeds the maximum allowed discount.");
            }

            // Calculate the percentage discount based on the cart amount
            $calculatedDiscount = ($cartAmount * $discountAmount) / 100;

            // If there's a max discount, apply it
            if ($maxDiscount !== null && $calculatedDiscount > $maxDiscount) {
                $calculatedDiscount = $maxDiscount;
            }

            return $calculatedDiscount;

        } elseif ($offerType === 'Flat') {
            // For flat discount, just apply the given discount amount
            return $discountAmount;
        } else {
            throw new \Exception("Invalid offer type.");
        }
    }

    public function getCartShowRoomAddress()
    {
        $this->response = $this->response->withType('application/json');
        if (!$this->request->is(['post'])) {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            return $this->response->withStatus(405)->withStringBody(json_encode($result));
        }
        try {
            $search_str = $this->request->getData('search_str');
            $city_name = $this->request->getData('city_name');
            $data = $this->Showrooms->searchCartShowRoom($search_str, $city_name);

            $result = [
                'status' => __('success'),
                'code' => 200,
                'message' => __('Address item retrieved successfully'),
                'data' => $data
            ];

            // Return success response
            return $this->response->withStatus(200)->withStringBody(json_encode($result));
        } catch (\Exception $e) {
            $result = [
                'status' => __('error'),
                'code' => 500,
                'message' => __('An unexpected error occurred: ') . $e->getMessage()
            ];
            return $this->response->withStatus(500)->withStringBody(json_encode($result));
        }
    }

    /*****  Checkout Page Code start *******/
    public function checkout()
    {
        $identity = $this->request->getSession()->read('Auth.User');

        // Check if the user is not authenticated
        if (empty($identity)) {
            $this->Flash->websiteError(__('Login required for checkout.'));
            return $this->redirect(['controller' => 'Customer', 'action' => 'login']);
        }

        $guestToken = $this->request->getSession()->read('cartId') ?? null;
        $totalSalePrice = 0;
        $customerAddress = '';
        $loyaltyDetails = "";
        $identity = $this->request->getSession()->read('Auth.User');
        // Generate Guest Token if not logged in and no guest-token exists
        if (!$identity && !$guestToken) {
            $guestToken = Text::uuid();
            $this->request->getSession()->write('cartId', $guestToken);
        }

        // Determine Customer ID or Use Guest Token
        $customerId = $identity ? $identity->id : null;
        if (!empty($customerId)) {
            $users = $this->Users->find()
                ->contain(['Customers']) // Include related Customers
                ->where(['Users.status' => 'A'])
                ->where(['Users.id' => $customerId])
                ->first();
            $customerId = $users->customer->id;
            $customerAddress = $this->CustomerAddresses->listAddress($customerId);
        }

        // $deliveryCharges = $this->getDeliveryChargeFromCity('','standard');


        if (isset($customerId) && !empty($customerId)) {
            $loyaltyDetails = $this->Loyalty->calculateLoyaltyPoints($customerId);
        }
        // Find or Create Cart

        $savedCards = $this->CustomerCards->find()
            ->where(['customer_id' => $customerId])
            ->select(['id', 'card_holder_name', 'card_number', 'expiry_date', 'cvv'])
            ->toArray();


        $cartConditions = $customerId ? ['customer_id' => $customerId] : ['guest_token' => $guestToken];

        $cart = $this->Carts->find()
            ->where($cartConditions)
            ->contain(['CartItems' => ['Products']]) // , 'ProductVariants'  // Include cart items with product and variant data
            ->first();
        if (empty($cart->cart_items)) {
            return $this->redirect(['controller' => 'Account', 'action' => 'cart']);
        }

        $totalPrice = 0;
        $totalDiscountedPrice = 0;
        $cartItems = [];
        $cartOutItems = [];
        $weightQuantityArray = [];
        if ($cart) {
            foreach ($cart->cart_items as $k => $item) {

                $price = null;
                $saleprice = null;
                if ($item->product_variant_id) {
                    $productVariant = $this->CartItems->ProductVariants->find()
                        ->select(['promotion_price', 'sales_price'])
                        ->where(['id' => $item->product_variant_id])
                        ->first();

                    $price = $productVariant ? $productVariant->promotion_price : null;
                    $saleprice = $productVariant ? $productVariant->sales_price : null;
                }else{
                    $price = $this->Products->getProductPrice($item->product_id);
                }

                if (!$price) {
                    $product = $this->CartItems->Products->find()
                        ->select(['promotion_price', 'sales_price'])
                        ->where(['id' => $item->product_id])
                        ->first();

                    $price = $product ? $product->promotion_price : null;
                    $saleprice = $product ? $product->sales_price : null;
                }

                if ($price !== null) {
                    $image = $this->ProductImages->getDefaultProductImage($item->product_id);
                    $getAvailableStatus = $this->Products->getAvailabilityStatus($item->product_id);
                    if ($image) {
                        $image = $this->Media->getCloudFrontURL($image);
                    }

                    if ($getAvailableStatus == "In Stock") {
                        $salepriceWithCheckVariant = $saleprice ? $saleprice : $item->product->sales_price;
                        $weightQuantityArray[] = ['weight' => $item->product->product_weight, 'quantity' => $item->quantity];

                        $totalPrice = $totalPrice + number_format($item->quantity * $price, 2, '.', '');
                        $totalSalePrice = $totalSalePrice + ($totalDiscountedPrice + number_format($item->quantity * $salepriceWithCheckVariant, 2, '.', ''));
                        $cartItems[] = [
                            'discount' => $this->Products->getDiscountProduct($item->product_id, $item->product_variant_id),
                            'sale' => $saleprice ? $saleprice : $item->product->sales_price,
                            'product_image' => $image,
                            'cart_item_id' => $item->id,
                            'product_id' => $item->product_id,
                            'product_variant_id' => $item->product_variant_id,
                            'product_name' => $item->product->name,
                            'variant_name' => $item->product_variant->name ?? "",
                            'quantity' => $item->quantity,
                            'price' => $price,
                            'sale_price' => number_format($item->quantity * $saleprice, 2, '.', ''),
                            'get_available_status' => $getAvailableStatus,
                            'total_price' => number_format($item->quantity * $price, 2, '.', '')
                        ];
                    } else {
                        $cartOutItems[] = [
                            'sale' => $saleprice ? $saleprice : $item->product->sales_price,
                            'discount' => $this->Products->getDiscountProduct($item->product_id),
                            'product_image' => $image,
                            'cart_item_id' => $item->id,
                            'product_id' => $item->product_id,
                            'product_variant_id' => $item->product_variant_id,
                            'product_name' => $item->product->name,
                            'variant_name' => $item->product_variant && $item->product_variant->name
                                ? $item->product_variant->name
                                : $item->product->name,
                            'quantity' => $item->quantity,
                            'price' => $price,
                            'sale_price' => number_format($item->quantity * $saleprice, 2, '.', ''),
                            'get_available_status' => $getAvailableStatus,
                            'total_price' => number_format($item->quantity * $price, 2, '.', '')
                        ];
                    }
                }
            }
        }
        $cart_id = $cart->id ?? 0;
        $total_items = count($cartItems);

        $totalDiscountedPrice = 0;

        $totalDiscountedPrice = $totalSalePrice - $totalPrice;

        $methods = $this->PaymentMethods->listMethods();
        $mobile = $users->country_code . $users->mobile_no;
        if (empty($mobile)) {
            $mobile = '';
        }

        $this->set(compact('weightQuantityArray', 'mobile', 'methods', 'savedCards', 'customerAddress', 'customerId', 'loyaltyDetails', 'totalDiscountedPrice', 'totalPrice', 'cartOutItems', 'cartItems', 'cart_id', 'total_items'));
        $this->viewBuilder()->setTemplatePath('carts');
        $this->render('checkout');
    }

    public function addNewCardDetails()
    {
        $this->response = $this->response->withType('application/json');
        if (!$this->request->is(['post'])) {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            return $this->response->withStatus(405)->withStringBody(json_encode($result));
        }
        try {
            $identity = $this->request->getSession()->read('Auth.User');
            $customerId = $identity ? $identity->id : null;
            if (!empty($customerId)) {
                $users = $this->Users->find()
                    ->contain(['Customers']) // Include related Customers
                    ->where(['Users.status' => 'A'])
                    ->where(['Users.id' => $customerId])
                    ->first();

                if (!$users || empty($users->customer)) {
                    throw new \Exception(__('Customer not found.'));
                }
                $customerId = $users->customer->id;
            }
            $data['customer_id'] = $customerId;
            $data['card_holder_name'] = $this->request->getData('card_holder_name');
            $data['card_number'] = $this->request->getData('card_number');
            $data['cvv'] = $this->request->getData('cvv');
            $data['expiry_date'] = $this->request->getData('expiry_date');
            $cardId = $this->CustomerCards->add_record($data);

            if ($cardId) {
                $result = [
                    'status' => 'success',
                    'code' => 201,
                    'data' => ['id' => $cardId],
                    'message' => __('Card added successfully.')
                ];
            } else {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Failed to add card. Please try again.')
                ];
            }
            // Return success response
            return $this->response->withStatus(200)->withStringBody(json_encode($result));
        } catch (\Exception $e) {
            $result = [
                'status' => __('error'),
                'code' => 500,
                'message' => __('An unexpected error occurred: ') . $e->getMessage()
            ];
            return $this->response->withStatus(500)->withStringBody(json_encode($result));
        }
    }

    public function editNewCardDetails()
    {
        $this->response = $this->response->withType('application/json');
        if (!$this->request->is(['post'])) {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            return $this->response->withStatus(405)->withStringBody(json_encode($result));
        }
        try {
            $identity = $this->request->getSession()->read('Auth.User');
            $customerId = $identity ? $identity->id : null;
            if (!empty($customerId)) {
                $users = $this->Users->find()
                    ->contain(['Customers']) // Include related Customers
                    ->where(['Users.status' => 'A'])
                    ->where(['Users.id' => $customerId])
                    ->first();

                if (!$users || empty($users->customer)) {
                    throw new \Exception(__('Customer not found.'));
                }
                $customerId = $users->customer->id;
            }
            $data['customer_id'] = $customerId;
            $data['card_holder_name'] = $this->request->getData('card_holder_name');
            $data['card_number'] = $this->request->getData('card_number');
            $data['cvv'] = $this->request->getData('cvv');
            $data['expiry_date'] = $this->request->getData('expiry_date');
            $cardId = $this->CustomerCards->update_record($this->request->getData('id'), $data);

            if ($cardId) {
                $result = [
                    'status' => 'success',
                    'code' => 201,
                    'data' => ['id' => $cardId],
                    'message' => __('Card updated successfully.')
                ];
            } else {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Failed to add card. Please try again.')
                ];
            }
            // Return success response
            return $this->response->withStatus(200)->withStringBody(json_encode($result));
        } catch (\Exception $e) {
            $result = [
                'status' => __('error'),
                'code' => 500,
                'message' => __('An unexpected error occurred: ') . $e->getMessage()
            ];
            return $this->response->withStatus(500)->withStringBody(json_encode($result));
        }
    }

    public function deleteCard()
    {
        $this->response = $this->response->withType('application/json');
        if (!$this->request->is(['post'])) {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            return $this->response->withStatus(405)->withStringBody(json_encode($result));
        }
        try {
            $identity = $this->request->getSession()->read('Auth.User');
            $customerId = $identity ? $identity->id : null;
            if (!empty($customerId)) {
                $users = $this->Users->find()
                    ->contain(['Customers']) // Include related Customers
                    ->where(['Users.status' => 'A'])
                    ->where(['Users.id' => $customerId])
                    ->first();

                if (!$users || empty($users->customer)) {
                    throw new \Exception(__('Customer not found.'));
                }
                $customerId = $users->customer->id;
            }
            $savedCard = $this->CustomerCards->get($this->request->getData('id'));

            if ($savedCard->customer_id !== $customerId) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Unauthorized to delete this card.')
                ];
                $this->response = $this->response->withStatus(200);
            } else {
                if ($this->CustomerCards->delete($savedCard)) {
                    $result = [
                        'status' => 'success',
                        'code' => 200,
                        'message' => __('Card deleted successfully.')
                    ];
                    $this->response = $this->response->withStatus(200);
                } else {
                    $result = [
                        'status' => 'error',
                        'code' => 200,
                        'message' => __('Failed to delete card. Please try again.')
                    ];
                    $this->response = $this->response->withStatus(200);
                }
            }

            // Return success response
            return $this->response->withStatus(200)->withStringBody(json_encode($result));
        } catch (\Exception $e) {
            $result = [
                'status' => __('error'),
                'code' => 500,
                'message' => __('An unexpected error occurred: ') . $e->getMessage()
            ];
            return $this->response->withStatus(500)->withStringBody(json_encode($result));
        }
    }


    public function createOrder()
    {
        $this->response = $this->response->withType('application/json');
        if (!$this->request->is(['post'])) {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            return $this->response->withStatus(405)->withStringBody(json_encode($result));
        }
        $redirect_url = '';

        $identity = $this->request->getSession()->read('Auth.User');
        $customerId = $identity ? $identity->id : null;
        if (!empty($customerId)) {
            $users = $this->Users->find()
                ->contain(['Customers']) // Include related Customers
                ->where(['Users.status' => 'A'])
                ->where(['Users.id' => $customerId])
                ->first();

            if (!$users || empty($users->customer)) {
                throw new \Exception(__('Customer not found.'));
            }
            $customerId = $users->customer->id;
        }
        if (!$customerId) {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('User login required or place order!')
            ];
            return $this->response->withStatus(405)->withStringBody(json_encode($result));
        }

        // Pre-pare Request Data

        $data = $this->request->getData();

        /***** Verify Payment total code start ****/

        $guestToken = $this->request->getSession()->read('cartId') ?? null;
        $cartConditions = $customerId
            ? ['customer_id' => $customerId]
            : ['guest_token' => $guestToken];

        $cart = $this->Carts->find()
            ->where($cartConditions)
            ->contain(['CartItems' => ['Products']])  // Include cart items with product and variant data
            ->first();

        $totalPrice = 0;
        $weightQuantityArray = [];
        $orderItemsData = [];
        if ($cart) {
            foreach ($cart->cart_items as $item) {
                $price = null;
                $saleprice = null;
                if ($item->product_variant_id) {
                    $productVariant = $this->CartItems->ProductVariants->find()
                        ->select(['promotion_price', 'sales_price'])
                        ->where(['id' => $item->product_variant_id])
                        ->first();

                    $price = $productVariant ? $productVariant->promotion_price : null;
                }else{
                    $price = $this->Products->getProductPrice($item->product_id);
                }
                if (!$price) {
                    $product = $this->CartItems->Products->find()
                        ->select(['promotion_price', 'sales_price'])
                        ->where(['id' => $item->product_id])
                        ->first();

                    $price = $product ? $product->promotion_price : null;
                }

                if ($price !== null) {
                    $getAvailableStatus = $this->Products->getAvailabilityStatus($item->product_id);

                    if ($getAvailableStatus == "In Stock") {
                        $weightQuantityArray[] = ['weight' => $item->product->product_weight, 'quantity' => $item->quantity];
                        $totalPrice = $totalPrice + number_format($item->quantity * $price, 2, '.', '');
                        $itemQtyTotalPrice = number_format($item->quantity * $price, 2, '.', '');

                        $orderItem = [
                            'product_id' => $item->product_id,
                            'quantity' => $item->quantity,
                            'price' => $price,
                            'total_price' => $itemQtyTotalPrice,
                            'status' => 'Pending',
                        ];

                        // Add 'product_variant_id' only if it exists
                        if (isset($item->product_variant_id) && $item->product_variant_id != null || $item->product_variant_id != 0) {
                            $orderItem['product_variant_id'] = $item->product_variant_id;
                        }

                        $orderItemsData[] = $orderItem;

                    }
                }
            }
        }
        $loyaltyDetails = $this->Loyalty->calculateLoyaltyPoints($customerId);
        $points = 0;
        if (isset($loyaltyDetails['data']['points_converted']) && $loyaltyDetails['data']['points_converted']) {
            $points = $loyaltyDetails['data']['points_converted'];
        }
        if ($points < $data['redeem_points']) {
            $result = [
                'status' => 'error',
                'code' => 200,
                'message' => __('Reddem point not valid or expired'),
            ];
            return $this->response->withStatus(200)->withStringBody(json_encode($result));
        }

        $couponAmount = $this->applyofferCheck($data['coupon_code'], $totalPrice);

        $couponDiscountedAmount = 0;
        $offerId = null;

        if (isset($couponAmount['data']['coupon_amount']) && $couponAmount['data']['coupon_amount']) {
            $couponDiscountedAmount = $couponAmount['data']['coupon_amount'];
            $offerId = $couponAmount['data']['offer_id'];
        }

        $cityId = $this->CustomerAddresses->find()->select(['city_id'])->where(['id' => $data['customer_address_id']])->first()->city_id ?? '';

        $deliveryCharges = $this->getDeliveryChargeFromCityAPI($cityId, 'standard', $weightQuantityArray);

        $deliveryChargeCost = 0;
        if (isset($deliveryCharges['total_delivery_charge']) && $deliveryCharges['total_delivery_charge']) {
            $deliveryChargeCost = $deliveryCharges['total_delivery_charge'];
        }

        $finalAmount = $totalPrice - ($couponDiscountedAmount + $data['inputPointVal']) + $deliveryChargeCost;

        if ($data['final_amount'] != $finalAmount) {
            $result = [
                'status' => 'error',
                'code' => 200,
                'data' => [$finalAmount],
                'message' => __('something went wrong in payment checkout process.'),
            ];
            return $this->response->withStatus(200)->withStringBody(json_encode($result));
        }


        /***    Verify payment code end   ***/


        $payment_method = $data['payment_method']; // "MTN MoMo"; //
        $total_amount = $finalAmount; // $data['total_amount']; // 100; //
        $mobile_no = $data['mobile_no'] ?? null; // "+917043967319";//
        $order_no = $this->Orders->generateUniqueOrderNum();
        $data['customer_id'] = $customerId;
        $data['order_number'] = $order_no;
        $data['order_date'] = date('Y-m-d H:i:s');
        $data['status'] = "Pending";
        $data['payment_method'] = $payment_method;
        $data['subtotal_amount'] = $data['total_amount'];
        $data['total_amount'] = $data['final_amount'];
        $data['delivery_mode'] = $data['delivery_mode'];

        if (isset($data['customer_address_id']) && !empty($data['customer_address_id'])) {
            $data['customer_address_id'] = $data['customer_address_id'];
        }

        if (isset($cityId) && !empty($cityId)) {
            $data['city_id'] = $cityId;
        }

        if (isset($data['showroom_id']) && !empty($data['showroom_id'])) {
            $data['showroom_id'] = $data['showroom_id'];
        }

        if ($offerId != null) {
            $data['offer_id'] = $offerId;
        }

        $data['delivery_charge'] = $deliveryChargeCost; // $data['inputPointVal'];
        $data['discount_amount'] = $couponDiscountedAmount;

        $add_order = 0;
        if ($payment_method == 'MTN MoMo') {

            $pg_result = $this->Mtn->initiatePayment($order_no, $total_amount, $mobile_no);

            if (!$pg_result || !is_array($pg_result)) {
                $result = [
                    'status' => 'error',
                    'code' => 500,
                    'message' => __('Failed to initiate MTN payment')
                ];
                return $this->response->withStatus(200)->withStringBody(json_encode($result));
            }

            // If debug info is present, remove it before sending response
            if (isset($pg_result['debug_info'])) {
                $debug_info = $pg_result['debug_info'];
                unset($pg_result['debug_info']);
            }

            $redirect_url = '/';
            $api_status = $pg_result['status'] ?? 'error';
            $httpcode = $pg_result['httpcode'] ?? 500;

            if ($api_status == 'error') {
                if ($httpcode == 200) {
                    $result = [
                        'status' => 'error',
                        'code' => $httpcode,
                        'payment_status' => $pg_result['payment_status'] ?? __('FAILED'),
                        'message' => $pg_result['reason'] ?? __('Payment failed')
                    ];
                } else {
                    $result = [
                        'status' => 'error',
                        'code' => $httpcode,
                        'error_code' => $pg_result['error_code'] ?? null,
                        'message' => $pg_result['message'] ?? __('Payment failed')
                    ];
                }
                return $this->response->withStatus(200)->withStringBody(json_encode($result));
            } else {
                $s_payment_status = $pg_result['payment_status'];

                if ($s_payment_status == 'SUCCESSFUL') {
                    $s_message = __('Payment Successful! Thank you for your payment; your order has been successfully created.');
                } else {
                    $s_message = __('Payment is still pending! your order has been successfully created.');
                }
                $result2 = [
                    'message' => $s_message,
                    'payment_status' => $s_payment_status
                ];
                $add_order = 1;
            }

        } elseif ($payment_method == 'Wave') {
            // call pg api
            $pg_result = $this->Wave->createCheckoutSession($order_no, $total_amount);
            $api_status = $pg_result['status'];
            $httpcode = $pg_result['httpcode'];
            if ($api_status == 'error') {
                $result = [
                    'status' => 'error',
                    'code' => $httpcode,
                    'error_code' => $pg_result['error_code'],
                    'message' => $pg_result['message']
                ];
                $this->response = $this->response->withStatus($httpcode);
                return $this->response->withStatus(200)->withStringBody(json_encode($result));

            } else {

                $wave_checkout_sessionID = $pg_result['data']['id'];
                $wave_checkout_status = $pg_result['data']['checkout_status'];
                $wave_client_reference = $pg_result['data']['client_reference'];
                $wave_error_url = $pg_result['data']['error_url'];
                $wave_success_url = $pg_result['data']['success_url'];
                $wave_business_name = $pg_result['data']['business_name'];
                $wave_payment_status = $pg_result['data']['payment_status'];
                $wave_launch_url = $pg_result['data']['wave_launch_url'];
                $wave_when_created = $pg_result['data']['when_created'];
                $wave_when_expires = $pg_result['data']['when_expires'];

                $result2 = [
                    'message' => __('Checkout session created'),
                    'wave_launch_url' => $wave_launch_url
                ];
                $add_order = 1;
            }

        } elseif ($payment_method == 'Cash on Delivery') {     //COD

            $result2 = [
                'message' => __('Your order has been successfully created.')
            ];
            $add_order = 1;
        } else {

            $result = [
                'status' => 'error',
                'message' => __('Please select payment method')
            ];
            return $this->response->withStatus(200)->withStringBody(json_encode($result));

        }

        // Order create related code
        if ($add_order) {
            $order = $this->Orders->newEmptyEntity();
            $transactionData = [
                'invoice_number' => $this->Transactions->generateUniqueInvoiceNum(),
                'transaction_number' => $this->Transactions->generateUniqueTransactionNum(),
                'transaction_date' => date('Y-m-d H:i:s'),
                'amount' => $total_amount,
                //'total_amount' => 100.00,
                'payment_method' => $payment_method, //$payment_method,
            ];


            $data['order_items'] = $orderItemsData;
            $data['transactions'] = [$transactionData];


            $order = $this->Orders->patchEntity($order, $data, [
                'associated' => ['OrderItems', 'Transactions']
            ]);
            $save_order = $this->Orders->save($order);
            if ($save_order) {
                $redirect_url = 'WebResponse/success';
                $result1 = [
                    'redirect_url' => $redirect_url,
                    'status' => 'success',
                    'code' => 200,
                    'message' => __('Payment Successful! Thank you for your payment; your order has been successfully created.'),
                    'data' => $order
                ];
                $result = array_merge($result1, $result2);
                $cart = $this->Carts->find()
                    ->where(['customer_id' => $customerId])
                    ->first();
                if ($cart) {
                    $this->Carts->CartItems->deleteAll(['cart_id' => $cart->id]);
                    $this->Carts->delete($cart);
                }


            } else {
                $redirect_url = 'WebResponse/error';
                $validationErrors = $order->getErrors();
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'errors' => $validationErrors,
                    'redirect_url' => $redirect_url,
                    'message' => __('There was an issue creating your order. Please try again.')
                ];
            }
        }

        return $this->response->withStatus(200)->withStringBody(json_encode($result));
    }

    public function getDeliveryChargeFromCityAPI($cityId, $deliveryMode, $weightQuantityArray)
    {
        $cityId = $cityId;
        $deliveryMode = $deliveryMode;
        $weightQuantityArray = $weightQuantityArray;

        if (empty($weightQuantityArray) || !is_array($weightQuantityArray)) {
            return [
                'status' => 'error',
                'message' => __('Invalid weight and quantity data.')
            ];
        }

        $deliveryCharges = $this->DeliveryCharges->find()
            ->where([
                'city_id' => $cityId,
                'delivery_mode' => $deliveryMode,
                'status' => 'A'
            ])
            ->order(['weight' => 'ASC'])
            ->toArray();

        if (empty($deliveryCharges)) {
            return [
                'status' => 'error',
                'message' => __('No delivery charges found for the selected criteria.')
            ];
        }

        $total_delivery_charge = 0.00;

        foreach ($weightQuantityArray as $item) {
            $weight = $item['weight'];
            $quantity = $item['quantity'];
            $applicableCharge = null;

            foreach ($deliveryCharges as $charge) {
                if ($weight > $charge->weight) {
                    $applicableCharge = $charge;
                } else {
                    break;
                }
            }

            if ($applicableCharge) {
                $quotient = $quantity / 2;
                $remainder = $quantity % 2;
                $calculated_charge = ($remainder == 0) ? ($quotient * $applicableCharge->charge) : (($quotient * $applicableCharge->charge) + (1 * $applicableCharge->charge));
                $total_delivery_charge += $calculated_charge;
            }
        }

        $result = [
            'status' => 'success',
            'total_delivery_charge' => $total_delivery_charge,
            'message' => __('Shipping charge applied.')
        ];

        return $result;
    }

    public function getDeliveryChargeFromCity()
    {
        $this->request->allowMethod(['post']);
        $data = $this->request->getData();

        $cityId = $data['cityId'];
        $deliveryMode = $data['deliveryMode'];
        $weightQuantityArray = $data['weightQuantityArray'];

        if (empty($weightQuantityArray) || !is_array($weightQuantityArray)) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => 'error',
                'message' => __('Invalid weight and quantity data.')
            ]));
        }

        $deliveryCharges = $this->DeliveryCharges->find()
            ->where([
                'city_id' => $cityId,
                'delivery_mode' => $deliveryMode,
                'status' => 'A'
            ])
            ->order(['weight' => 'ASC'])
            ->toArray();

        if (empty($deliveryCharges)) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => 'error',
                'message' => __('No delivery charges found for the selected criteria.')
            ]));
        }

        $total_delivery_charge = 0.00;

        foreach ($weightQuantityArray as $item) {
            $weight = $item['weight'];
            $quantity = $item['quantity'];
            $applicableCharge = null;

            foreach ($deliveryCharges as $charge) {
                if ($weight > $charge->weight) {
                    $applicableCharge = $charge;
                } else {
                    break;
                }
            }

            if ($applicableCharge) {
                $quotient = $quantity / 2;
                $remainder = $quantity % 2;
                $calculated_charge = ($remainder == 0) ? ($quotient * $applicableCharge->charge) : (($quotient * $applicableCharge->charge) + (1 * $applicableCharge->charge));
                $total_delivery_charge += $calculated_charge;
            }
        }

        $result = [
            'status' => 'success',
            'total_delivery_charge' => $total_delivery_charge,
            'message' => __('Shipping charge applied.')
        ];

        $this->response = $this->response->withType('application/json')->withStringBody(json_encode($result));
        return $this->response;
    }

    /*****  Checkout Page Code end *******/

    /*****  Calculate Cart Total  *******/
    private function calculateCartTotal($cartId)
    {
        $cart = $this->Carts->find()
            ->where(['id' => $cartId])
            ->contain(['CartItems' => ['Products', 'ProductVariants']])
            ->first();

        if (!$cart) {
            return [
                'status' => false,
                'message' => 'Cart not found',
                'data' => []
            ];
        }

        $totalPrice = 0;
        $totalDiscountedPrice = 0;
        $cartItems = [];
        $cartOutItems = [];

        foreach ($cart->cart_items as $item) {
            $price = null;
            $saleprice = null;

            // Get price from variant if exists
            if ($item->product_variant_id) {
                $productVariant = $this->CartItems->ProductVariants->find()
                    ->select(['promotion_price', 'sales_price'])
                    ->where(['id' => $item->product_variant_id])
                    ->first();

                $price = $productVariant ? $productVariant->promotion_price : null;
                $saleprice = $productVariant ? $productVariant->sales_price : null;
            }

            // Get price from product if no variant price
            if (!$price) {
                $product = $this->CartItems->Products->find()
                    ->select(['promotion_price', 'sales_price'])
                    ->where(['id' => $item->product_id])
                    ->first();

                $price = $product ? $product->promotion_price : null;
                $saleprice = $product ? $product->sales_price : null;
            }

            if ($price !== null) {
                $getAvailableStatus = $this->Products->getAvailabilityStatus($item->product_id);

                if ($getAvailableStatus == "In Stock") {
                    $totalPrice += number_format($item->quantity * $price, 2, '.', '');
                    $totalDiscountedPrice += number_format($item->quantity * $saleprice, 2, '.', '');
                }
            }
        }

        return [
            'status' => true,
            'data' => [
                'total_price' => $totalPrice,
                'total_discounted_price' => $totalDiscountedPrice,
                'cart_id' => $cart->id
            ]
        ];
    }

    /****  My account order ******/

    public function order()
    {
        $categoriesQuery = $this->OrderCancellationCategories->getAllCategories();
        $categoriesArray = $categoriesQuery->toArray();


        $OrderReturnCategories = $this->OrderReturnCategories->getAllCategories();
        $OrderReturnCategoriesArray = $OrderReturnCategories->toArray();
       


        $data = $this->request->getData();
        $guestToken = $this->request->getSession()->read('cartId') ?? null; // 7d1c54ba-05d8-4a5b-bad4-ed4381486858

        $identity = $this->request->getSession()->read('Auth.User');
        // Generate Guest Token if not logged in and no guest-token exists
        if (!$identity && !$guestToken) {
            $guestToken = Text::uuid();
            $this->request->getSession()->write('cartId', $guestToken);
        }

        // Determine Customer ID or Use Guest Token
        $customerId = $identity ? $identity->id : null;
        if (!empty($customerId)) {
            $users = $this->Users->find()
                ->contain(['Customers']) // Include related Customers
                ->where(['Users.status' => 'A'])
                ->where(['Users.id' => $customerId])
                ->first();
            $customerId = $users->customer->id;
        }

        // $page = 1;
        // $limit = 2;
        // $orderList = $this->Orders->orderListByUser($customerId, 1, 2);

        $page = (int)$this->request->getQuery('page', 1);
        $limit = 20;
        $orderList = $this->Orders->orderListByUser($customerId, $page, $limit);
        //  dd($orderList);
        if ($this->request->is('ajax')) {
            $this->viewBuilder()->disableAutoLayout();
            $this->set(compact('orderList'));
            $this->render('order_items');
            return;
        }
        $this->set(compact('customerId', 'orderList','categoriesArray','OrderReturnCategoriesArray'));
        $this->viewBuilder()->setTemplatePath('account');
        $this->render('order');
    }

    /****  My account order End ******/

    /**
     * Add review for order items
     */
    public function addReview()
    {
        $this->response = $this->response->withType('application/json');

        if (!$this->request->is(['post', 'ajax'])) {
            return $this->response->withStatus(405)->withStringBody(json_encode([
                'status' => 'error',
                'message' => __('Method not allowed')
            ]));
        }

        $identity = $this->request->getSession()->read('Auth.User');
        if (!$identity) {
            return $this->response->withStatus(401)->withStringBody(json_encode([
                'status' => 'error',
                'message' => __('Please login to add review')
            ]));
        }

        $users = $this->Users->find()
            ->contain(['Customers'])
            ->where(['Users.status' => 'A', 'Users.id' => $identity->id])
            ->first();

        if (!$users || !$users->customer) {
            return $this->response->withStatus(404)->withStringBody(json_encode([
                'status' => 'error',
                'message' => __('Customer not found')
            ]));
        }

        $data = $this->request->getData();

        // Debug: Log the received data
        \Cake\Log\Log::debug('Received review data: ' . json_encode($data));

        $orderId = $data['order_id'] ?? null;
        $reviews = $data['reviews'] ?? [];

        if (!$orderId || empty($reviews)) {
            return $this->response->withStatus(400)->withStringBody(json_encode([
                'status' => 'error',
                'message' => __('Invalid data provided')
            ]));
        }

        // Verify order ownership and status
        if (!$this->OrderItemReviews->canCustomerReview($users->customer->id, $orderId)) {
            return $this->response->withStatus(403)->withStringBody(json_encode([
                'status' => 'error',
                'message' => __('You can only review delivered orders')
            ]));
        }

        $connection = $this->OrderItemReviews->getConnection();
        $connection->begin();

        try {
            $savedReviews = [];

            foreach ($reviews as $reviewData) {
                $orderItemId = $reviewData['order_item_id'] ?? null;
                $productId = $reviewData['product_id'] ?? null;
                $rating = $reviewData['rating'] ?? null;
                $review = $reviewData['review'] ?? '';

                if (!$orderItemId || !$productId || !$rating) {
                    continue;
                }

                // Check if review already exists
                $existingReview = $this->OrderItemReviews->find()
                    ->where([
                        'customer_id' => $users->customer->id,
                        'order_item_id' => $orderItemId,
                        'status !=' => 'rejected'
                    ])
                    ->first();

                if ($existingReview) {
                    continue; // Skip if already reviewed
                }

                $reviewData = [
                    'customer_id' => $users->customer->id,
                    'order_id' => $orderId,
                    'order_item_id' => $orderItemId,
                    'product_id' => $productId,
                    'rating' => $rating,
                    'review' => $review,
                    'status' => 'pending' // Use new status system
                ];

                $reviewEntity = $this->OrderItemReviews->newEntity($reviewData);

                if ($this->OrderItemReviews->save($reviewEntity)) {
                    $savedReviews[] = $reviewEntity;
                } else {
                    // Log validation errors
                    \Cake\Log\Log::error('Review save failed: ' . json_encode($reviewEntity->getErrors()));
                    \Cake\Log\Log::error('Review data: ' . json_encode($reviewData));
                }
            }

            if (empty($savedReviews)) {
                $connection->rollback();
                return $this->response->withStatus(400)->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => __('No reviews were saved. Please check your data.')
                ]));
            }

            $connection->commit();

            return $this->response->withStringBody(json_encode([
                'status' => 'success',
                'message' => __('Reviews submitted successfully and are pending approval'),
                'data' => ['reviews_count' => count($savedReviews)]
            ]));

        } catch (\Exception $e) {
            $connection->rollback();
            return $this->response->withStatus(500)->withStringBody(json_encode([
                'status' => 'error',
                'message' => __('Failed to save reviews: ') . $e->getMessage()
            ]));
        }
    }

    /**
     * Get reviewable order items for a specific order
     */
    public function getReviewableItems()
    {
        $this->response = $this->response->withType('application/json');

        if (!$this->request->is(['get', 'ajax'])) {
            return $this->response->withStatus(405)->withStringBody(json_encode([
                'status' => 'error',
                'message' => __('Method not allowed')
            ]));
        }

        $identity = $this->request->getSession()->read('Auth.User');
        if (!$identity) {
            return $this->response->withStatus(401)->withStringBody(json_encode([
                'status' => 'error',
                'message' => __('Please login to view reviewable items')
            ]));
        }

        $users = $this->Users->find()
            ->contain(['Customers'])
            ->where(['Users.status' => 'A', 'Users.id' => $identity->id])
            ->first();

        if (!$users || !$users->customer) {
            return $this->response->withStatus(404)->withStringBody(json_encode([
                'status' => 'error',
                'message' => __('Customer not found')
            ]));
        }

        $orderId = $this->request->getQuery('order_id');
        if (!$orderId) {
            return $this->response->withStatus(400)->withStringBody(json_encode([
                'status' => 'error',
                'message' => __('Order ID is required')
            ]));
        }

        $reviewableItems = $this->OrderItemReviews->getReviewableOrderItems($users->customer->id, $orderId);

        $items = [];
        foreach ($reviewableItems as $item) {
            $productImage = '';
            if (!empty($item->product->product_images)) {
                $productImage = $this->Media->getImageUrl($item->product->product_images[0]->image_name, 'products');
            }

            $items[] = [
                'order_item_id' => $item->id,
                'product_id' => $item->product_id,
                'product_name' => $item->product->name,
                'product_image' => $productImage,
                'quantity' => $item->quantity,
                'price' => $item->price
            ];
        }

        return $this->response->withStringBody(json_encode([
            'status' => 'success',
            'data' => ['items' => $items]
        ]));
    }

    public function success()
    {
        $this->viewBuilder()->setTemplatePath('Payments');
        $this->render('success');
    }

    public function address()
    {

        $auth = $this->request->getSession()->read('Auth.User');
        $users = $this->Users->find()
            ->contain(['Customers']) // Include related Customers
            ->where(['Users.status' => 'A'])
            ->where(['Users.id' => $auth->id])
            ->first();

        $addresses = $this->CustomerAddresses->listAddress($users->customer->id);


        $this->set(compact('addresses'));
        $this->viewBuilder()->setTemplatePath('account');
        $this->render('address');
    }

    public function favourite()
    {

        $auth = $this->request->getSession()->read('Auth.User');
        $users = $this->Users->find()
            ->contain(['Customers']) // Include related Customers
            ->where(['Users.status' => 'A'])
            ->where(['Users.id' => $auth->id])
            ->first();

        //        $addresses = $this->CustomerAddresses->listAddress($users->customer->id);
        $wishlists = $this->Wishlists->viewWishlist($users->customer->id);

        if ($wishlists) {

            foreach ($wishlists as &$wishlist) {

                $wishlist['product']['rating'] = $this->Reviews->getAverageRating($wishlist['product']['id']);
                $wishlist['product']['total_review'] = $this->Reviews->getTotalReviews($wishlist['product']['id']);
                $wishlist['product']['discount'] = $this->Products->getDiscount($wishlist['product']['id']);

                $wishlist['product']['product_image'] = '';
                $image = $this->ProductImages->getDefaultProductImage($wishlist['product']['id']);
                if ($image) {
                    $wishlist['product']['product_image'] = $this->Media->getCloudFrontURL($image);
                }
            }
        }
        //        dd($wishlists);

        $this->set(compact('wishlists'));
        $this->viewBuilder()->setTemplatePath('account');
        $this->render('favourite');
    }

    public function wallet()
    {

        $auth = $this->request->getSession()->read('Auth.User');
        $users = $this->Users->find()
            ->contain(['Customers']) // Include related Customers
            ->where(['Users.status' => 'A'])
            ->where(['Users.id' => $auth->id])
            ->first();

        $loyaltyDetails = $this->Loyalty->calculateLoyaltyPoints($users->customer->id);
        $customerId = $users->customer->id;

        $wallet = $this->Wallets->find()
            ->select(['balance'])
            ->where(['customer_id' => $customerId])
            ->first();

        $transactions = $this->Transactions->find()
            ->innerJoinWith('Orders', function ($q) use ($customerId) {
                return $q->where(['Orders.customer_id' => $customerId]);
            })
            ->order(['Transactions.transaction_date' => 'DESC'])
            ->toArray();

        $this->set(compact('loyaltyDetails','users'));
        $this->viewBuilder()->setTemplatePath('account');
        $this->render('wallet');
    }

    public function card3()
    {

        $auth = $this->request->getSession()->read('Auth.User');
        $users = $this->Users->find()
            ->contain(['Customers']) // Include related Customers
            ->where(['Users.status' => 'A'])
            ->where(['Users.id' => $auth->id])
            ->first();

        //        $addresses = $this->CustomerAddresses->listAddress($users->customer->id);
        $wishlists = $this->Wishlists->viewWishlist($users->customer->id);

        if ($wishlists) {

            foreach ($wishlists as &$wishlist) {

                $wishlist['product']['rating'] = $this->Reviews->getAverageRating($wishlist['product']['id']);
                $wishlist['product']['total_review'] = $this->Reviews->getTotalReviews($wishlist['product']['id']);
                $wishlist['product']['discount'] = $this->Products->getDiscount($wishlist['product']['id']);

                $wishlist['product']['product_image'] = '';
                $image = $this->ProductImages->getDefaultProductImage($wishlist['product']['id']);
                if ($image) {
                    $wishlist['product']['product_image'] = $this->Media->getCloudFrontURL($image);
                }
            }
        }
        //        dd($wishlists);

        $this->set(compact('wishlists'));
        $this->viewBuilder()->setTemplatePath('account');
        $this->render('card');
    }

    public function deleteAccount()
    {
        $this->request->allowMethod(['post']);
        $auth = $this->request->getSession()->read('Auth.User.id');
        $usersTable = TableRegistry::getTableLocator()->get('Users');
        $user = $usersTable->get($auth);
        $user->status = 'D';
        if ($usersTable->delete($user)) {
            $this->Flash->success(__('Your account has been deleted successfully.'));
            return $this->redirect(['controller' => 'Website', 'action' => 'deletelogout']);
        } else {
            $this->Flash->error(__('Unable to delete your account. Please try again.'));
            return $this->redirect(['action' => 'account']);
        }
    }

    public function card()
    {
        // Ensure user is logged in
        $auth = $this->request->getSession()->read('Auth.User');
        $users = $this->Users->find()
            ->contain(['Customers']) // Include related Customers
            ->where(['Users.status' => 'A'])
            ->where(['Users.id' => $auth->id])
            ->first();
       // dd($users->customer->id);

        $account = $users->customer->id;
        if (!$account) {
            $this->Flash->websiteError(__('Please login to view your cards.'));
            return $this->redirect(['controller' => 'Customer', 'action' => 'login']);
        }

        // Fetch cards for the logged-in customer
        $cards = $this->CustomerCards->find('all')
            ->where(['customer_id' => $users->customer->id])
            ->order(['created' => 'DESC'])
            ->toArray();

        $this->set(compact('cards'));
        $this->viewBuilder()->setTemplatePath('account');
        $this->render('card');
    }

    public function cardAdd()
    {
        // Ensure user is logged in
        $account = $this->request->getSession()->read('Auth.User');
        if (!$account) {
            $this->Flash->websiteError(__('Please login to add a card.'));
            return $this->redirect(['controller' => 'Customer', 'action' => 'login']);
        }
        $users = $this->Users->find()
        ->contain(['Customers']) // Include related Customers
        ->where(['Users.status' => 'A'])
        ->where(['Users.id' => $account->id])
        ->first();

        $account = $users->customer->id;

        if ($this->request->is('post')) {
            $cardData = $this->request->getData();
            $cardData['customer_id'] = $account;

            // Format expiry date to MM/YY
            if (!empty($cardData['expiry_month']) && !empty($cardData['expiry_year'])) {
                $cardData['expiry_date'] = sprintf('%02d/%02d',
                    $cardData['expiry_month'],
                    substr($cardData['expiry_year'], -2)
                );
            }

            $card = $this->CustomerCards->newEntity($cardData);

            if ($this->CustomerCards->save($card)) {
                $this->Flash->websiteSuccess(__('Card has been saved.'));
                return $this->redirect(['action' => 'card']);
            }
            $this->Flash->websiteError(__('Unable to save the card. Please check the details and try again.'));
        }

        $this->viewBuilder()->setTemplatePath('account');
        $this->render('card_add');
    }

    public function cardEdit($id = null)
    {
        // Ensure user is logged in
        $account = $this->request->getSession()->read('Auth.User');
        $users = $this->Users->find()
        ->contain(['Customers']) // Include related Customers
        ->where(['Users.status' => 'A'])
        ->where(['Users.id' => $account->id])
        ->first();

        $account = $users->customer->id;

        if (!$account) {
            $this->Flash->websiteError(__('Please login to edit a card.'));
            return $this->redirect(['controller' => 'Customer', 'action' => 'login']);
        }

        // Find the card and ensure it belongs to the logged-in customer
        $card = $this->CustomerCards->find()
            ->where([
                'id' => $id,
                'customer_id' => $account
            ])
            ->first();
        // Parse existing expiry date
        if (!empty($card->expiry_date)) {
            // Assuming expiry_date is in 'MM/YY' format
            list($month, $year) = explode('/', $card->expiry_date);
            $card->expiry_month = (int)$month;
            $card->expiry_year = '20' . $year; // Prepend '20' to make it a full year
        }

        if (!$card) {
            $this->Flash->websiteError(__('Card not found or you are not authorized to edit this card.'));
            return $this->redirect(['action' => 'card']);
        }

        if ($this->request->is(['patch', 'post', 'put'])) {
            $cardData = $this->request->getData();
            $cardData['customer_id'] = $account;

            // Format expiry date to MM/YY
            if (!empty($cardData['expiry_month']) && !empty($cardData['expiry_year'])) {
                $cardData['expiry_date'] = sprintf('%02d/%02d',
                    $cardData['expiry_month'],
                    substr($cardData['expiry_year'], -2)
                );
            }

            $card = $this->CustomerCards->patchEntity($card, $cardData);

            if ($this->CustomerCards->save($card)) {
                $this->Flash->websiteSuccess(__('Card has been updated.'));
                return $this->redirect(['action' => 'card']);
            }
            $this->Flash->websiteError(__('Unable to update the card. Please check the details and try again.'));
        }

        $this->set(compact('card'));
        $this->viewBuilder()->setTemplatePath('account');
        $this->render('card_edit');
    }

    public function cardDelete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);

        // Ensure user is logged in
        $account = $this->request->getSession()->read('Auth.User');
        if (!$account) {
            $this->Flash->websiteError(__('Please login to delete a card.'));
            return $this->redirect(['controller' => 'Customer', 'action' => 'login']);
        }
        $users = $this->Users->find()
        ->contain(['Customers']) // Include related Customers
        ->where(['Users.status' => 'A'])
        ->where(['Users.id' => $account->id])
        ->first();

        $account = $users->customer->id;

        // Find the card and ensure it belongs to the logged-in customer
        $card = $this->CustomerCards->find()
            ->where([
                'id' => $id,
                'customer_id' => $account
            ])
            ->first();

        if (!$card) {
            $this->Flash->websiteError(__('Card not found or you are not authorized to delete this card.'));
            return $this->redirect(['action' => 'card']);
        }

        if ($this->CustomerCards->delete($card)) {
            $this->Flash->websiteSuccess(__('Card has been deleted.'));
        } else {
            $this->Flash->websiteError(__('Unable to delete the card. Please try again.'));
        }

        return $this->redirect(['action' => 'card']);
    }

    public function orderCancelWeb()
    {
       
        $result = [];
        $data = $this->request->getData();
        $orderId = $data['order_id'];
        if (!$this->request->is('post')) {
            return $this->response->withType('application/json')->withStatus(405)
                ->withStringBody(json_encode([
                    'status' => 'error',
                    'code' => 405,
                    'message' => __('Method not allowed')
                ]));
        }

        $identity = $this->request->getSession()->read('Auth.User');

        if (!$identity) {
            return $this->response->withType('application/json')->withStatus(401)
                ->withStringBody(json_encode([
                    'status' => 'error',
                    'code' => 401,
                    'message' => __('User is not authenticated')
                ]));
        }
    
        if (empty($orderId)) {
            return $this->response->withType('application/json')->withStatus(400)
                ->withStringBody(json_encode([
                    'status' => 'error',
                    'code' => 400,
                    'message' => __('Order ID is required')
                ]));
        }
    
        try {
            $order = $this->Orders->get($orderId, ['contain' => ['OrderItems']]);
        } catch (\Cake\Datasource\Exception\RecordNotFoundException $e) {
            return $this->response->withType('application/json')->withStatus(404)
                ->withStringBody(json_encode([
                    'status' => 'error',
                    'code' => 404,
                    'message' => __('Order not found')
                ]));
        }
    
        // $customerId = $identity->get('_matchingData')['Customers']['id'];
     
        $users = $this->Users->find()
        ->contain([
            'Customers' => function ($q) {
                return $q->select(['id']); // Select only the Customer.id field
            }
        ])
        ->select(['Users.id']) // Select the necessary fields from Users
        ->where(['Users.status' => 'A'])
        ->where(['Users.id' => $identity->id])
        ->first();

        $customerId = $users->customer->id;
          

        if ($order->customer_id !== $customerId) {
            return $this->response->withType('application/json')->withStatus(403)
                ->withStringBody(json_encode([
                    'status' => 'error',
                    'code' => 403,
                    'message' => __('Unauthorized to cancel this order')
                ]));
        }
    
        if ($order->payment_method == 'Credit') {
            return $this->response->withType('application/json')->withStatus(400)
                ->withStringBody(json_encode([
                    'status' => 'error',
                    'code' => 400,
                    'message' => __('The order cannot be canceled as it was made on credit')
                ]));
        }
    
        foreach ($order->order_items as $item) {
            if (in_array($item->status, ['Shipped', 'Delivered'])) {
                return $this->response->withType('application/json')->withStatus(400)
                    ->withStringBody(json_encode([
                        'status' => 'error',
                        'code' => 400,
                        'message' => __('Cannot cancel order as one or more items have been shipped/delivered')
                    ]));
            }
        }
    
        $existingCancellation = $this->OrderCancellations->find()
            ->where(['order_id' => $orderId])
            ->first();
    
        if ($existingCancellation) {
            $messages = [
                'Pending' => __('Cancellation has already been initiated'),
                'Approved' => __('Cancellation is already in progress'),
                'Completed' => __('This order has already been cancelled')
            ];
    
            return $this->response->withType('application/json')->withStatus(400)
                ->withStringBody(json_encode([
                    'status' => 'error',
                    'code' => 400,
                    'message' => $messages[$existingCancellation->status] ?? __('Order cancellation issue')
                ]));
        }
    
     
        $cancellationData = [
            'order_id' => $orderId,
            'customer_id' => $customerId, // Ensure customer_id is included
            'order_item_id' => $data['product_id'],
            'order_cancellation_category_id' => $data['reason_id'],
            'reason' => $data['reason'] ?? null,
            'status' => 'Pending',
            'canceled_at' => date('Y-m-d H:i:s')
        ];
    
        $order->status = 'Pending Cancellation';
        if (!$this->Orders->save($order)) {
            return $this->response->withType('application/json')->withStatus(500)
                ->withStringBody(json_encode([
                    'status' => 'error',
                    'code' => 500,
                    'message' => __('Failed to cancel the order')
                ]));
        }
    
        $cancellation = $this->OrderCancellations->add_record_data($cancellationData);
    
        if (!$cancellation) {
            return $this->response->withType('application/json')->withStatus(500)
                ->withStringBody(json_encode([
                    'status' => 'error',
                    'code' => 500,
                    'message' => __('Failed to cancel the order')
                ]));
        }
    
        $trackingData = [
            'order_id' => $orderId,
            'status' => 'Pending Cancellation',
            'comment' => $data['reason'] ?? null,
        ];
        $this->OrderTrackingHistories->add_record($trackingData);
    
        $category = $this->OrderCancellationCategories->find()
            ->select(['name'])
            ->where(['id' => $data['reason_id']])
            ->first();
    
        $reasonName = $category ? $category->name : __('No reason provided');
    
        $adminEmails = Configure::read('Settings.ADMIN_EMAILS');
        $to = $adminEmails[0];
        $cc = count($adminEmails) > 1 ? array_slice($adminEmails, 1) : null;
        $subject = 'Order Cancellation Request';
        $template = 'order_cancellation';
        $viewVars = [
            'order_number' => $order['order_number'],
            'customer_name' => $identity['first_name'] . ' ' . $identity['last_name'],
            'reason' => $reasonName,
            'comment' => $data['reason'] ?? 'No comment provided',
            'canceled_at' => $cancellationData['canceled_at'],
        ];
    
        $sendEmail = $this->Global->send_email($to, null, $subject, $template, $viewVars, null, $cc);
    
        if ($sendEmail) {
            return $this->response->withType('application/json')->withStatus(200)
                ->withStringBody(json_encode([
                    'status' => 'success',
                    'code' => 200,
                    'message' => __('Order cancellation has been initiated successfully, and an email notification has been sent to the admin.')
                ]));
        } else {
            return $this->response->withType('application/json')->withStatus(200)
                ->withStringBody(json_encode([
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Order cancellation has been initiated successfully, but the email notification could not be sent.')
                ]));
        }
    }
    
}
