<section>
    <div class="container">
        <div class="tab-content" id="wishlistTabContent">
            <!-- WISHLIST TAB -->
            <div class="tab-pane mb-5 fade show active" id="wishlist-home-tab-pane" role="tabpanel" aria-labelledby="wishlist-home-tab" tabindex="0">
                <div class="cart-section">
                    <div class="container py-5">
                        <div class="row g-4">
                            <!-- Wishlist Items List -->
                            <div class="col-lg-8">
                                <div class="d-flex justify-content-between align-items-center mb-4">
                                    <h4 class="fw-bold"> <?= __('My Wishlist ') ?>(<?= count($processedWishlistItems) ?> <?= __('items') ?>)</h4>
                                    <?php if (!empty($processedWishlistItems)): ?>
                                        <button class="btn btn-outline-danger btn-sm" onclick="showClearAllWishlistModal()">
                                            <?= __('Clear All') ?>
                                        </button>
                                    <?php endif; ?>
                                </div>

                                <?php if (!empty($processedWishlistItems)): ?>
                                    <?php foreach ($processedWishlistItems as $item):?>
                                          <a href="<?= $this->Url->build(['controller' => 'Home', 'action' => 'product', $item->product->id]) ?>" class="wishlist-card-link">
                                        <div class="cart-card d-flex flex-column flex-md-row align-items-start mb-4" data-wishlist-id="<?= $item->id ?>">
                                            <!-- Product Image -->
                                            <img src="<?= !empty($item->product->product_image) ? $item->product->product_image : $this->Url->webroot('img/no-img.jpg') ?>"
                                                 alt="<?= h($item->product->name) ?>"
                                                 class="img-fluid rounded-4 mb-3 mb-md-0 me-md-4"
                                                 style="width: 150px; height: 150px; object-fit: cover;" />
                                                     <!-- <img src="../../img/ozone/ASGH-product.png" alt="Air Conditioner"
                                            class="img-fluid w-100 "> -->

                                            <div class="purchase-cart flex-grow-1">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <div>
                                                        <h5 class="fw-bold"><?= h($item->product->product_name) ?></h5>
                                                        <p class="text-muted mb-2"><?= mb_strimwidth(strip_tags($item->product->product_description ?? ''), 0, 100, '...') ?></p>

                                                        <!-- Rating -->
                                                        <!-- <?php if ($item->product->rating > 0): ?>
                                                            <div class="rating mb-2">
                                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                                    <?php if ($i <= floor($item->product->rating)): ?>
                                                                        <i class="fas fa-star text-warning"></i>
                                                                    <?php elseif ($i - 0.5 <= $item->product->rating): ?>
                                                                        <i class="fas fa-star-half-alt text-warning"></i>
                                                                    <?php else: ?>
                                                                        <i class="far fa-star text-warning"></i>
                                                                    <?php endif; ?>
                                                                <?php endfor; ?>
                                                                <span class="text-muted ms-2">(<?= $item->product->total_review ?> reviews)</span>
                                                            </div>
                                                        <?php endif; ?> -->

                                                        <h4 class="text-success fw-bold mb-3">
                                                            <?= $this->Price->setPriceFormat($item->product->promotion_price) ?>
                                                            <?php if ($item->product->discount > 0): ?>
                                                                <small class="text-muted text-decoration-line-through ms-2">
                                                                    <?= $this->Price->setPriceFormat($item->product->product_price) ?>
                                                                </small>
                                                                <span class="badge bg-danger ms-2"><?= $item->product->discount ?>% OFF</span>
                                                            <?php endif; ?>
                                                        </h4>
                                                    </div>

                                                    <!-- Remove from wishlist button -->
                                                    <button class="btn btn-outline-danger btn-sm remove-wishlist-item"
                                                            data-product-id="<?= $item->product->id ?>"
                                                            data-product-name="<?= h($item->product->name) ?>"
                                                            title="Remove from wishlist">
                                                        <img src="../../img/ozone/heart_filled.png" class="img-fluid wishlist-img"/>
                                                    </button>
                                                </div>

                                                <div class="bg-light rounded-3 p-3 mb-3">
                                                    <p class="fw-bold mb-1"><?= __('Delivery and Returns') ?> </p>
                                                    <small class="text-muted"><?= __('Standard delivery 4–9 business days') ?>
                                                        <br><?= __('Orders are processed and delivered Monday–Friday (excluding public holidays)') ?></small>
                                                </div>

                                                <div class="d-flex gap-2">

                                                    <!-- <button class="btn btn-success add-to-cart-btn"
                                                            data-product-id="<?= $item->product->id ?>">
                                                        <i class="fas fa-shopping-cart me-2"></i>Add to Cart
                                                    </button> -->


                                                    <!-- <a href="<?= $this->Url->build(['controller' => 'Home', 'action' => 'product', $item->product->id]) ?>"
                                                       class="btn btn-outline-primary">
                                                        <i class="fas fa-eye me-2"></i>View Details
                                                    </a> -->
                                                </div>

                                                <small class="text-muted mt-2 d-block"><?= __('Added on') ?>  <?= $item->created->format('M d, Y') ?></small>
                                            </div>
                                        </div>
                                    </a>

                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <div class="text-center py-5">
                                        <div class="mb-4">
                                            <i class="fas fa-heart fa-5x text-muted"></i>
                                        </div>
                                        <h4 class="text-muted mb-3"><?= __('Your wishlist is empty') ?></h4>
                                        <p class="text-muted mb-4"><?= __('Save items you love by clicking the heart icon on any product') ?></p>
                                        <a href="<?= $this->Url->build(['controller' => 'Home', 'action' => 'productList']) ?>"
                                           class="btn btn-primary">
                                            <i class="fas fa-shopping-bag me-2"></i><?= __('Continue Shopping') ?>
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Wishlist Summary -->
                            <div class="col-lg-4">
                                <div class="summary-card">
                                    <h5 class="fw-bold title pb-4"><?= __('Wishlist Summary') ?></h5>

                                    <div class="d-flex justify-content-between mb-2">
                                        <span class="describtion"><?= __('Total Items') ?></span>
                                        <span class="text-success"><?= count($processedWishlistItems) ?></span>
                                    </div>

                                    <?php if (!empty($processedWishlistItems)): ?>
                                        <?php
                                        $totalValue = 0;
                                        foreach ($processedWishlistItems as $item) {
                                            $totalValue += $item->product->promotion_price;
                                        }
                                        ?>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span class="describtion"><?= __('Total Value') ?></span>
                                            <span class="text-success"><?= $this->Price->setPriceFormat($totalValue) ?></span>
                                        </div>

                                        <hr>

                                        <!-- <button class="btn btn-success w-100 mb-3" onclick="addAllToCart()">
                                            <i class="fas fa-shopping-cart me-2"></i>Add All to Cart
                                        </button>

                                        <button class="btn btn-outline-primary w-100 mb-3" onclick="shareWishlist()">
                                            <i class="fas fa-share me-2"></i>Share Wishlist
                                        </button> -->
                                    <?php endif; ?>

                                    <hr>

                                    <div class="text-center">
                                        <p class="small text-muted mb-3"><?= __('Continue shopping to discover more amazing products') ?></p>
                                        <a href="<?= $this->Url->build(['controller' => 'Home', 'action' => 'productList']) ?>"
                                           class="btn btn-outline-secondary w-100">
                                            <i class="fas fa-shopping-bag me-2"></i><?= __('Continue Shopping') ?>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Remove Item from Wishlist Confirmation Modal -->
<div class="modal fade" id="removeWishlistItemModal" tabindex="-1" aria-labelledby="removeWishlistItemModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title fw-bold" id="removeWishlistItemModalLabel"><?= __('Remove from Wishlist') ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center py-4">
                <div class="mb-3">
                    <i class="fas fa-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                </div>
                <h6 class="mb-3">
                    <?= __('Are you sure you want to remove this item from your wishlist?')?></h6>
                <p class="text-muted mb-0" id="wishlistProductNameToRemove"><?= __('Product Name') ?> </p>
            </div>
            <div class="modal-footer border-0 justify-content-center">
                <button type="button" class="btn btn-secondary px-4" data-bs-dismiss="modal"><?= __('Cancel') ?></button>
                <button type="button" class="btn btn-danger px-4" id="confirmRemoveWishlistBtn">
                    <?= __('Remove') ?></button>
            </div>
        </div>
    </div>
</div>

<!-- Clear All Wishlist Confirmation Modal -->
<div class="modal fade" id="clearAllWishlistModal" tabindex="-1" aria-labelledby="clearAllWishlistModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title fw-bold" id="clearAllWishlistModalLabel"><?= __('Clear Entire Wishlist') ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center py-4">
                <div class="mb-3">
                    <i class="fas fa-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                </div>
                <h6 class="mb-3">
                    <?= __('Are you sure you want to clear your entire wishlist?')?></h6>
                <p class="text-muted mb-0">
                    <?= __('This action cannot be undone and will remove all items from your wishlist.')?></p>
            </div>
            <div class="modal-footer border-0 justify-content-center">
                <button type="button" class="btn btn-secondary px-4" data-bs-dismiss="modal"><?= __('Cancel') ?></button>
                <button type="button" class="btn btn-danger px-4" id="confirmClearAllWishlistBtn"><?= __('Clear All') ?></button>
            </div>
        </div>
    </div>
</div>

<style>
    .wishlist-card-link {
    text-decoration: none;
    color: inherit; /* optional: keeps text color from changing */
}

.wishlist-card-link:hover {
    text-decoration: none; /* ensures hover state also has no underline */
}

/* Wishlist specific styles */
.cart-card {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    transition: box-shadow 0.3s ease;
}

.cart-card:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.summary-card {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 25px;
    position: sticky;
    top: 20px;
}

.btn-remove-wishlist {
    background: #dc3545;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

.btn-remove-wishlist:hover {
    background: #c82333;
}

.rating .fas, .rating .far {
    font-size: 14px;
}

.wishlist-empty-state {
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .cart-card {
        padding: 15px;
    }

    .summary-card {
        position: static;
        margin-top: 20px;
    }
}
</style>

<script>
// Global variable for wishlist removal
let wishlistProductToRemove = null;

// Function to show the remove wishlist item modal
function showRemoveWishlistModal(productId, productName) {
    // Validate inputs
    if (!productId) {
        console.error('No product ID provided');
        return;
    }

    wishlistProductToRemove = productId;

    // Set product name in modal
    const productNameElement = document.getElementById('wishlistProductNameToRemove');
    if (productNameElement) {
        productNameElement.textContent = productName || 'Unknown Product';
    }

    // Show the modal using Bootstrap 5
    const modalElement = document.getElementById('removeWishlistItemModal');
    if (modalElement) {
        const modal = new bootstrap.Modal(modalElement);
        modal.show();
    } else {
        console.error('Remove wishlist modal element not found');
    }
}

// Remove single item from wishlist
$(document).on('click', '.remove-wishlist-item', function(e) {
    e.preventDefault();

    const productId = $(this).data('product-id');
    const productName = $(this).data('product-name');

    // Show modal instead of confirm dialog
    showRemoveWishlistModal(productId, productName);
});

// Function to handle the actual wishlist item removal
function removeWishlistItem() {
    if (!wishlistProductToRemove) {
        console.error('No wishlist product ID to remove');
        return;
    }

    $.ajax({
        headers: {
            'X-CSRF-Token': $('meta[name="csrfToken"]').attr('content')
        },
        url: "<?= $this->Url->build(['controller' => 'Cart', 'action' => 'removeFromWishlist']) ?>",
        type: 'POST',
        data: {product_id: wishlistProductToRemove},
        success: function(response) {
            if (response.status === 'success') {
                showMessage(response.message || 'Item removed from wishlist', 'success');

                // Hide the modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('removeWishlistItemModal'));
                if (modal) {
                    modal.hide();
                }

                // Reload page to reflect changes
                location.reload();
            } else {
                showMessage(response.message || 'Failed to remove item', 'error');
            }
        },
        error: function() {
            showMessage('Error removing item from wishlist', 'error');
        }
    });

    // Reset variable
    wishlistProductToRemove = null;
}

// Add single item to cart
$(document).on('click', '.add-to-cart-btn', function(e) {
    e.preventDefault();

    const productId = $(this).data('product-id');
    const button = $(this);
    const originalText = button.html();

    // Show loading state
    button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>Adding...');

    // Use the new cart update system
    if (window.CartUpdater) {
        window.CartUpdater.addToCart(productId, 1,
            function(data) {
                // Success callback
                button.prop('disabled', false).html('<i class="fas fa-check me-2"></i>Added!');

                // Reset button after 2 seconds
                setTimeout(() => {
                    button.html(originalText);
                }, 2000);
            },
            function(error) {
                // Error callback
                button.prop('disabled', false).html(originalText);
                showMessage('<?= __('Error adding item to cart') ?>', 'error');
            }
        );
    } else {
        // Fallback to old method
        $.ajax({
            headers: {
                'X-CSRF-Token': $('meta[name="csrfToken"]').attr('content')
            },
            url: "<?= $this->Url->build(['controller' => 'Cart', 'action' => 'addToCart']) ?>/" + productId,
            type: 'GET',
            success: function(response) {
                button.prop('disabled', false).html('<i class="fas fa-check me-2"></i>Added!');
                showMessage('<?= __('Item added to cart successfully') ?>', 'success');

                // Reset button after 2 seconds
                setTimeout(() => {
                    button.html(originalText);
                }, 2000);

                // Refresh cart count
                if (window.refreshCartCount) {
                    window.refreshCartCount();
                }
            },
            error: function() {
                button.prop('disabled', false).html(originalText);
                showMessage('<?= __('Error adding item to cart') ?>', 'error');
            }
        });
    }
});

// Function to show the clear all wishlist modal
function showClearAllWishlistModal() {
    // Show the modal using Bootstrap 5
    const modal = new bootstrap.Modal(document.getElementById('clearAllWishlistModal'));
    modal.show();
}

// Clear all wishlist items
function clearAllWishlist() {
    const productIds = [];
    $('.remove-wishlist-item').each(function() {
        productIds.push($(this).data('product-id'));
    });

    // Show loading state
    $('.cart-card').addClass('loading');

    // Remove all items one by one
    let promises = productIds.map(productId => {
        return $.ajax({
            headers: {
                'X-CSRF-Token': $('meta[name="csrfToken"]').attr('content')
            },
            url: "<?= $this->Url->build(['controller' => 'Cart', 'action' => 'removeFromWishlist']) ?>",
            type: 'POST',
            data: {product_id: productId}
        });
    });

    Promise.all(promises).then(() => {
        showMessage('<?= __('Wishlist cleared successfully') ?>', 'success');

        // Hide the modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('clearAllWishlistModal'));
        if (modal) {
            modal.hide();
        }

        setTimeout(() => {
            location.reload();
        }, 1000);
    }).catch(() => {
        $('.cart-card').removeClass('loading');
        showMessage('<?= __('Error clearing wishlist') ?>', 'error');
    });
}

// Add all items to cart
function addAllToCart() {
    const productIds = [];
    $('.add-to-cart-btn').each(function() {
        productIds.push($(this).data('product-id'));
    });

    if (productIds.length === 0) {
        showMessage('<?= __('No items to add to cart') ?>', 'warning');
        return;
    }

    // Show loading state
    $('.add-to-cart-btn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>Adding...');

    // Add all items one by one using the new cart update system
    if (window.CartUpdater) {
        let addedCount = 0;
        let totalItems = productIds.length;

        productIds.forEach(productId => {
            window.CartUpdater.addToCart(productId, 1,
                function(data) {
                    // Success callback
                    addedCount++;
                    if (addedCount === totalItems) {
                        $('.add-to-cart-btn').prop('disabled', false).html('<i class="fas fa-check me-2"></i>All Added!');
                        showMessage('<?= __('All items added to cart successfully') ?>', 'success');

                        // Reset buttons after 2 seconds
                        setTimeout(() => {
                            $('.add-to-cart-btn').html('<i class="fas fa-shopping-cart me-2"></i>Add to Cart');
                        }, 2000);
                    }
                },
                function(error) {
                    // Error callback
                    $('.add-to-cart-btn').prop('disabled', false).html('<i class="fas fa-shopping-cart me-2"></i>Add to Cart');
                    showMessage('<?= __('Error adding some items to cart') ?>', 'error');
                }
            );
        });
    } else {
        // Fallback to old method
        let promises = productIds.map(productId => {
            return $.ajax({
                headers: {
                    'X-CSRF-Token': $('meta[name="csrfToken"]').attr('content')
                },
                url: "<?= $this->Url->build(['controller' => 'Cart', 'action' => 'addToCart']) ?>/" + productId,
                type: 'GET'
            });
        });

        Promise.all(promises).then(() => {
            $('.add-to-cart-btn').prop('disabled', false).html('<i class="fas fa-check me-2"></i>All Added!');
            showMessage('<?= __('All items added to cart successfully') ?>', 'success');

            // Reset buttons after 2 seconds
            setTimeout(() => {
                $('.add-to-cart-btn').html('<i class="fas fa-shopping-cart me-2"></i>Add to Cart');
            }, 2000);

            // Refresh cart count
            if (window.refreshCartCount) {
                window.refreshCartCount();
            }
        }).catch(() => {
            $('.add-to-cart-btn').prop('disabled', false).html('<i class="fas fa-shopping-cart me-2"></i>Add to Cart');
            showMessage('<?= __('Error adding some items to cart') ?>', 'error');
        });
    }
}

// Share wishlist
function shareWishlist() {
    const url = window.location.href;

    if (navigator.share) {
        navigator.share({
            title: 'My Wishlist',
            text: 'Check out my wishlist!',
            url: url
        });
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(url).then(() => {
            showMessage('<?= __('Wishlist link copied to clipboard') ?>', 'success');
        }).catch(() => {
            showMessage('<?= __('Unable to copy link') ?>', 'error');
        });
    }
}

// Update wishlist summary
function updateWishlistSummary() {
    const itemCount = $('.cart-card').length;
    $('h4:contains("My Wishlist")').text(`My Wishlist (${itemCount} items)`);
    $('.describtion:contains("Total Items")').next().text(itemCount);
}

// Show message function - using common toast functionality
function showMessage(message, type) {
    // Use the common toast functionality from website.php
    showToastMessage(message, type);
}

// Initialize page
$(document).ready(function() {
    console.log('Wishlist page loaded with <?= count($processedWishlistItems) ?> items');

    // Event listener for the confirm remove wishlist item button
    const confirmRemoveWishlistBtn = document.getElementById('confirmRemoveWishlistBtn');
    if (confirmRemoveWishlistBtn) {
        confirmRemoveWishlistBtn.addEventListener('click', function() {
            removeWishlistItem();
        });
    }

    // Event listener for the confirm clear all wishlist button
    const confirmClearAllWishlistBtn = document.getElementById('confirmClearAllWishlistBtn');
    if (confirmClearAllWishlistBtn) {
        confirmClearAllWishlistBtn.addEventListener('click', function() {
            clearAllWishlist();
        });
    }
});
</script>
