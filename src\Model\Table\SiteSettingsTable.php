<?php

declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * SiteSettings Model
 *
 * @method \App\Model\Entity\SiteSetting newEmptyEntity()
 * @method \App\Model\Entity\SiteSetting newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\SiteSetting> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\SiteSetting get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\SiteSetting findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\SiteSetting patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\SiteSetting> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\SiteSetting|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\SiteSetting saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\SiteSetting>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\SiteSetting>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\SiteSetting>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\SiteSetting> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\SiteSetting>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\SiteSetting>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\SiteSetting>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\SiteSetting> deleteManyOrFail(iterable $entities, array $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class SiteSettingsTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('site_settings');
        $this->setDisplayField('site_title');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->scalar('site_title')
            ->maxLength('site_title', 255)
            ->requirePresence('site_title', 'create')
            ->notEmptyString('site_title');

        $validator
            ->scalar('address_line1')
            ->maxLength('address_line1', 255)
            ->requirePresence('address_line1', 'create')
            ->notEmptyString('address_line1');

        $validator
            ->scalar('address_line2')
            ->maxLength('address_line2', 255)
            ->allowEmptyString('address_line2');

        $validator
            ->scalar('country')
            ->maxLength('country', 100)
            ->requirePresence('country', 'create')
            ->notEmptyString('country');

        $validator
            ->scalar('state')
            ->maxLength('state', 100)
            ->requirePresence('state', 'create')
            ->notEmptyString('state');

        $validator
            ->scalar('city')
            ->maxLength('city', 100)
            ->requirePresence('city', 'create')
            ->notEmptyString('city');

        $validator
            ->scalar('zipcode')
            ->maxLength('zipcode', 20)
            ->requirePresence('zipcode', 'create')
            ->notEmptyString('zipcode');

        $validator
            ->scalar('customer_support_no')
            ->maxLength('customer_support_no', 50)
            ->requirePresence('customer_support_no', 'create')
            ->notEmptyString('customer_support_no');

        $validator
            ->scalar('contact_no')
            ->maxLength('contact_no', 20)
            ->requirePresence('contact_no', 'create')
            ->notEmptyString('contact_no');

        $validator
            ->scalar('support_email')
            ->maxLength('support_email', 50)
            ->requirePresence('support_email', 'create')
            ->notEmptyString('support_email');

        $validator
            ->scalar('admin_email')
            ->maxLength('admin_email', 50)
            ->requirePresence('admin_email', 'create')
            ->notEmptyString('admin_email');

        $validator
            ->time('business_open_time')
            ->requirePresence('business_open_time', 'create')
            ->notEmptyTime('business_open_time');

        $validator
            ->time('business_close_time')
            ->requirePresence('business_close_time', 'create')
            ->notEmptyTime('business_close_time');

        $validator
            ->scalar('company_logo')
            ->maxLength('company_logo', 255)
            ->allowEmptyString('company_logo');

        $validator
            ->scalar('fav_icon')
            ->maxLength('fav_icon', 255)
            ->allowEmptyString('fav_icon');

        $validator
            ->scalar('facebook_url')
            ->maxLength('facebook_url', 255)
            ->allowEmptyString('facebook_url');

        $validator
            ->scalar('twitter_url')
            ->maxLength('twitter_url', 255)
            ->allowEmptyString('twitter_url');

        $validator
            ->scalar('pinterest_url')
            ->maxLength('pinterest_url', 255)
            ->allowEmptyString('pinterest_url');

        $validator
            ->scalar('youtube_url')
            ->maxLength('youtube_url', 255)
            ->allowEmptyString('youtube_url');

        $validator
            ->scalar('instagram_url')
            ->maxLength('instagram_url', 255)
            ->allowEmptyString('instagram_url');

        $validator
            ->scalar('linkedin_url')
            ->maxLength('linkedin_url', 255)
            ->allowEmptyString('linkedin_url');

        $validator
            ->integer('pagination_count')
            ->allowEmptyString('pagination_count');

        $validator
            ->decimal('credit_min_limit')
            ->allowEmptyString('credit_min_limit');
        $validator
            ->integer('product_cancel_in_days')
            ->allowEmptyString('product_cancel_in_days');
        $validator
            ->integer('	product_return_in_days')
            ->allowEmptyString('product_return_in_days');        

        $validator
            ->integer('express_delivery_order_cutoff_time')
            ->notEmptyString('express_delivery_order_cutoff_time');

        return $validator;
    }

    //S
    public function getDetails()
    {
        $details = $this->find()->select([])->first();
        return $details;
    }
}
