<?php

declare(strict_types=1);

namespace App\Model\Table;
use Cake\Controller\ComponentRegistry;
use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\ORM\TableRegistry;
use Cake\Validation\Validator;
use Cake\Datasource\EntityInterface;
use Cake\I18n\FrozenTime;
use Cake\Http\ServerRequestFactory;
/**
 * Orders Model
 *
 * @property \App\Model\Table\CustomersTable&\Cake\ORM\Association\BelongsTo $Customers
 * @property \App\Model\Table\CustomerAddressesTable&\Cake\ORM\Association\BelongsTo $CustomerAddresses
 * @property \App\Model\Table\OffersTable&\Cake\ORM\Association\BelongsTo $Offers
 * @property \App\Model\Table\ShowroomsTable&\Cake\ORM\Association\BelongsTo $Showrooms
 * @property \App\Model\Table\OrderItemsTable&\Cake\ORM\Association\HasMany $OrderItems
 * @property \App\Model\Table\ReturnsTable&\Cake\ORM\Association\HasMany $Returns
 * @property \App\Model\Table\ShipmentsTable&\Cake\ORM\Association\HasMany $Shipments
 * @property \App\Model\Table\TransactionsTable&\Cake\ORM\Association\HasMany $Transactions
 *
 * @method \App\Model\Entity\Order newEmptyEntity()
 * @method \App\Model\Entity\Order newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\Order> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\Order get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\Order findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\Order patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\Order> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\Order|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\Order saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\Order>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Order>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Order>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Order> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Order>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Order>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Order>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Order> deleteManyOrFail(iterable $entities, array $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class OrdersTable extends Table
{

    protected $language;
    protected $country;
    protected $country_id;
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);
        $request = ServerRequestFactory::fromGlobals();
        $this->language = $request->getSession()->read('siteSettings.language') ?? 'English';
        $this->country = $request->getSession()->read('siteSettings.country') ?? 'Qatar';
        $this->country_id = $request->getSession()->read('siteSettings.country_id') ?? 1;
        $this->setTable('orders');
        $this->setDisplayField('order_number');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');

        $this->belongsTo('Customers', [
            'foreignKey' => 'customer_id',
            'joinType' => 'INNER',
        ]);
        //        $this->hasMany('SupportTickets', [
        //            'foreignKey' => 'order_id',
        //        ]);
        $this->hasMany('SupportTickets', [
            'foreignKey' => 'order_id',
            'dependent' => true,
        ]);
        $this->belongsTo('CustomerAddresses', [
            'foreignKey' => 'customer_address_id',
            'joinType' => 'LEFT',
        ]);
        $this->belongsTo('Countries', [
            'foreignKey' => 'country_id',
            'joinType' => 'LEFT',
        ]);
        $this->belongsTo('Offers', [
            'foreignKey' => 'offer_id',
        ]);
        $this->hasMany('OrderItems', [
            'foreignKey' => 'order_id',
            'dependent' => true,
            'cascadeCallbacks' => true,
        ]);
        $this->hasMany('Returns', [
            'foreignKey' => 'order_id',
        ]);
        $this->hasMany('OrderReturns', [
            'foreignKey' => 'order_id',
        ]);
        $this->hasMany('OrderCancellations', [
            'foreignKey' => 'order_id',
        ]);
        $this->hasMany('Shipments', [
            'foreignKey' => 'order_id',
        ]);
        $this->hasMany('Transactions', [
            'foreignKey' => 'order_id'
        ]);

        $this->hasMany('OrderTrackingHistories', [
            'foreignKey' => 'order_id'
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->nonNegativeInteger('customer_id')
            ->notEmptyString('customer_id');

        $validator
            ->nonNegativeInteger('customer_address_id')
            ->allowEmptyString('customer_address_id');

        $validator
            ->nonNegativeInteger('offer_id')
            ->allowEmptyString('offer_id');

        $validator
            ->scalar('order_number')
            ->maxLength('order_number', 255)
            ->requirePresence('order_number', 'create')
            ->notEmptyString('order_number');

        $validator
            ->dateTime('order_date')
            ->requirePresence('order_date', 'create')
            ->notEmptyDateTime('order_date');

        $validator
            ->scalar('status')
            ->notEmptyString('status');

        $validator
            ->scalar('payment_method')
            ->maxLength('payment_method', 255)
            ->requirePresence('payment_method', 'create')
            ->notEmptyString('payment_method');

        $validator
            ->scalar('shipping_method')
            ->maxLength('shipping_method', 255);
        // ->requirePresence('shipping_method', 'create')
        // ->notEmptyString('shipping_method');

        $validator
            ->decimal('subtotal_amount')
            ->requirePresence('subtotal_amount', 'create')
            ->notEmptyString('subtotal_amount');

        $validator
            ->decimal('delivery_charge')
            ->notEmptyString('delivery_charge');

        $validator
            ->decimal('discount_amount')
            ->notEmptyString('discount_amount');

        $validator
            ->decimal('offer_amount')
            ->allowEmptyString('offer_amount');

        $validator
            ->decimal('total_amount')
            ->requirePresence('total_amount', 'create')
            ->notEmptyString('total_amount');

        $validator
            ->scalar('delivery_mode')
            ->requirePresence('delivery_mode', 'create')
            ->notEmptyString('delivery_mode');

            $validator
            ->scalar('delivery_mode_type')
            ->allowEmptyString('delivery_mode_type');

        $validator
            ->scalar('order_notes')
            ->maxLength('order_notes', 1000)
            ->allowEmptyString('order_notes');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->existsIn(['customer_id'], 'Customers'), ['errorField' => 'customer_id']);
        $rules->add($rules->existsIn(['customer_address_id'], 'CustomerAddresses'), ['errorField' => 'customer_address_id']);
        $rules->add($rules->existsIn(['offer_id'], 'Offers'), ['errorField' => 'offer_id']);

        return $rules;
    }



     public function getCustomerRecentOrders($customerId)
        {
            $isArabic = ($this->language === 'ar' || $this->language === 'Arabic' || strtolower($this->language) === 'arabic');

            // Get customer info
            $usersTable = TableRegistry::getTableLocator()->get('Users');
            $user = $usersTable->find()
                ->contain(['Customers'])
                ->where(['Customers.id' => $customerId])
                ->first();

            if (!$user || !$user->customer) {
                return [];
            }

            $orders = $this->find()
                ->contain([
                    'OrderItems' => [
                        'Products' => [
                            'Reviews' => function ($q) use ($user) {
                                return $q->where(['Reviews.customer_id' => $user->customer->id]);
                            }
                        ]
                    ]
                ])
                ->where(['Orders.customer_id' => $user->customer->id])
                ->order(['Orders.created' => 'DESC'])
                ->limit(5)
                ->toArray();

            if (!empty($orders)) {
                $productImagesTable = TableRegistry::getTableLocator()->get('ProductImages');

                // Load Media component manually
                $registry = new ComponentRegistry();
                $mediaComponent = new \App\Controller\Component\MediaComponent($registry);

                foreach ($orders as $order) {
                    foreach ($order->order_items as $item) {
                        $product = $item->product;

                        // Set localized product name
                        $item->product_name = $isArabic ? $product->name_ar : $product->name;

                        // Get product image
                        $image = $productImagesTable->getDefaultProductImage($item->product_id);
                        $item->product_image = $image ? $mediaComponent->getCloudFrontURL($image) : null;
                    }
                }
            }

            return $orders;
        }


     public function getCustomerLoadMoreOrders($customerId, $limit = 5, $offset = 0)
       {
            $isArabic = ($this->language === 'ar' || $this->language === 'Arabic' || strtolower($this->language) === 'arabic');

            // Get customer info
            $usersTable = TableRegistry::getTableLocator()->get('Users');
            $user = $usersTable->find()
                ->contain(['Customers'])
                ->where(['Customers.id' => $customerId])
                ->first();

            if (!$user || !$user->customer) {
                return [];
            }

            $orders = $this->find()
                ->contain([
                    'OrderItems' => [
                        'Products' => [
                            'Reviews' => function ($q) use ($user) {
                                return $q->where(['Reviews.customer_id' => $user->customer->id]);
                            }
                        ]
                    ]
                ])
                ->where(['Orders.customer_id' => $user->customer->id])
                ->order(['Orders.created' => 'DESC'])
                ->limit(5)
                ->offset($offset)
                ->toArray();

            if (!empty($orders)) {
                $productImagesTable = TableRegistry::getTableLocator()->get('ProductImages');

                // Load Media component manually
                $registry = new ComponentRegistry();
                $mediaComponent = new \App\Controller\Component\MediaComponent($registry);

                foreach ($orders as $order) {
                    foreach ($order->order_items as $item) {
                        $product = $item->product;

                        // Set localized product name
                        $item->product_name = $isArabic ? $product->name_ar : $product->name;

                        // Get product image
                        $image = $productImagesTable->getDefaultProductImage($item->product_id);
                        $item->product_image = $image ? $mediaComponent->getCloudFrontURL($image) : null;
                    }
                }
            }

            return $orders;
        }
        

    //new method
    public function getDetailedOrder($orderId, $countryId)
    {
         $isArabic = ($this->language === 'ar' || $this->language === 'Arabic' || strtolower($this->language) === 'arabic');

        $order = $this->find()
            ->contain(['OrderItems' => ['Products'], 'Transactions'])
            ->where(['Orders.id' => $orderId])
            ->first();

        if ($order && $order->order_items) {
            $productImagesTable = TableRegistry::getTableLocator()->get('ProductImages');

            // Load Media component manually since we're in the model
            $registry = new ComponentRegistry();
            $mediaComponent = new \App\Controller\Component\MediaComponent($registry);

            foreach ($order->order_items as $item) {
                $product = $item->product;

                // Set product name based on language
                $item->product_name = $isArabic ? $product->name_ar : $product->name;

                // Get product image
                $image = $productImagesTable->getDefaultProductImage($item->product_id);
                $item->product_image = $image ? $mediaComponent->getCloudFrontURL($image) : null;
            }
        }

        return $order;
    }
     //new method
    public function extractAppliedCoupon($order)
    {
        if (!$order || empty($order->coupon_code)) {
            return null;
        }

        return [
            'code' => $order->coupon_code,
            'discount_amount' => $order->coupon_discount_amount ?? 0,
            'coupon_type' => $order->coupon_type ?? 'fixed'
        ];
    }


    public function generateUniqueOrderNum($PrefixSource = '')
    {
        $orderNum = $this->generateRandomNumber(10);

        while ($this->exists(['order_number' => $orderNum])) {
            $orderNum = $this->generateRandomNumber(10);
        }
        if ($PrefixSource == 'Supervisor') {
            $orderNum = 'SA-' . $orderNum;
        } else if ($PrefixSource == 'Customer') {
            $orderNum = 'CA-' . $orderNum;
        } else if ($PrefixSource == 'Website') {
            $orderNum = 'WS-' . $orderNum;
        } else if ($PrefixSource == 'Admin') {
            $orderNum = 'AP-' . $orderNum;
        }

        return $orderNum;
    }

    private function generateRandomNumber($length)
    {
        $number = '';
        for ($i = 0; $i < $length; $i++) {
            $number .= mt_rand(0, 9);
        }
        return $number;
    }

    public function getOrderDetails($user_id = '', $Role = '')
    {
        $query = $this->find()
            ->select([
                'Orders.id',
                'Orders.order_number',
                'full_name' => $this->Customers->Users->find()->func()->concat([
                    "Users.first_name" => 'identifier',
                    ' ',
                    "Users.last_name" => 'identifier',
                ]),
                'phone_number' => $this->Customers->Users->find()->func()->concat([
                    "Users.country_code" => 'identifier',
                    ' ',
                    "Users.mobile_no" => 'identifier',
                ]),
                'Orders.total_amount',
                'Orders.country_id',
                'Orders.status',
                'Orders.payment_method',
                'Orders.order_date',
                'quantity' => $this->OrderItems->find()->func()->sum('OrderItems.quantity'),
                'transaction_status' => 'Transactions.payment_status',
                'Orders.delivery_mode',
                // Currency fields from relation
                'currency_code' => 'Currencies.code',
                'currency_symbol' => 'Currencies.symbol',
                'currency_name' => 'Currencies.name',
                'currency_symbol_position' => 'Currencies.symbol_position',
                'currency_decimal_places' => 'Currencies.decimal_places',
            ])
            ->innerJoinWith('Customers.Users')
            ->leftJoinWith('OrderItems')
            ->leftJoinWith('Transactions')
            ->leftJoinWith('Countries.Currencies', function ($q) {
                return $q->where(['Currencies.status' => 'A']);
            })
            ->group(['Orders.id'])
            ->order(['Orders.id' => 'DESC']);

        return $query;
    }


    public function delete(EntityInterface $order, array $options = []): bool
    {
        $order->status = 'Cancelled';
        if ($this->save($order)) {
            return true;
        }
        return false;
    }

    public function delete_order(EntityInterface $order, array $options = []): bool
    {
        $order->status = 'Deleted';
        $order->status_date = date('Y-m-d');
        $order->last_modified_by = $options['last_modified_by'];

        if ($this->save($order)) {
            return true;
        }
        return false;
    }

    //M
    public function add_record($attributes)
    {

        $new = $this->newEmptyEntity();
        foreach ($attributes as $key => $value) {
            $new->$key = $value;
        }

        if ($this->save($new)) {
            return $new->id;
        } else {
            return false;
        }
    }

    //M
    public function update_record($id, $attributes)
    {
        $old_attr = $this->get($id);
        $old_attr = $this->patchEntity($old_attr, $attributes);
        if ($this->save($old_attr)) {
            return $old_attr;
        } else {
            return false;
        }
    }


    //S
    public function getCurrentYearOrders($showroomId)
    {
        $currentYear = FrozenTime::now()->year;
        $totalOrdersQuery = $this->find()
            ->select(['total_orders' => $this->find()->func()->count('*')])
            ->where(function ($exp) use ($currentYear, $showroomId) {
                return $exp->eq('YEAR(order_date)', $currentYear)
                    ->notIn('status', ['Cancelled', 'Returned'])
                    ->eq('showroom_id', $showroomId);
            })
            ->first();
        return $totalOrders = $totalOrdersQuery ? $totalOrdersQuery->total_orders : 0;
    }

    //S
    public function getCurrentMonthOrders($showroomId)
    {
        $currentYear = FrozenTime::now()->year;
        $currentMonth = FrozenTime::now()->month;
        $totalOrdersQuery = $this->find()
            ->select(['total_orders' => $this->find()->func()->count('*')])
            ->where(function ($exp) use ($currentYear, $currentMonth, $showroomId) {
                return $exp
                    ->eq('YEAR(order_date)', $currentYear)
                    ->eq('MONTH(order_date)', $currentMonth)
                    ->notIn('status', ['Cancelled', 'Returned'])
                    ->eq('showroom_id', $showroomId);
            })
            ->first();
        return $totalOrders = $totalOrdersQuery ? $totalOrdersQuery->total_orders : 0;
    }

    //S
    public function getCurrentDayOrders($showroomId)
    {
        $today = FrozenTime::now()->format('Y-m-d');
        $totalOrdersQuery = $this->find()
            ->select(['total_orders' => $this->find()->func()->count('*')])
            ->where(function ($exp) use ($today, $showroomId) {
                return $exp->eq('DATE(order_date)', $today)
                    ->notIn('status', ['Cancelled', 'Returned'])
                    ->eq('showroom_id', $showroomId);
            })
            ->first();
        return $totalOrders = $totalOrdersQuery ? $totalOrdersQuery->total_orders : 0;
    }

    //S
    public function getCurrentYearSales($showroomId)
    {
        $currentYear = FrozenTime::now()->year;
        $totalSalesQuery = $this->find()
            ->select(['total_sales' => $this->find()->func()->sum('total_amount')])
            ->where(function ($exp) use ($currentYear, $showroomId) {
                return $exp->eq('YEAR(order_date)', $currentYear)
                    ->notIn('status', ['Cancelled', 'Returned'])
                    ->eq('showroom_id', $showroomId);
            })
            ->first();
        return $totalSales = $totalSalesQuery->total_sales ? $totalSalesQuery->total_sales : 0;
    }

    //S
    public function getCurrentMonthSales($showroomId)
    {
        $currentYear = FrozenTime::now()->year;
        $currentMonth = FrozenTime::now()->month;
        $totalSalesQuery = $this->find()
            ->select(['total_sales' => $this->find()->func()->sum('total_amount')])
            ->where(function ($exp) use ($currentYear, $currentMonth, $showroomId) {
                return $exp
                    ->eq('YEAR(order_date)', $currentYear)
                    ->eq('MONTH(order_date)', $currentMonth)
                    ->notIn('status', ['Cancelled', 'Returned'])
                    ->eq('showroom_id', $showroomId);
            })
            ->first();
        return $totalSales = $totalSalesQuery->total_sales ? $totalSalesQuery->total_sales : 0;
    }

    //S
    public function getCurrentDaySales($showroomId)
    {
        $today = FrozenTime::now()->format('Y-m-d');
        $totalSalesQuery = $this->find()
            ->select(['total_sales' => $this->find()->func()->sum('total_amount')])
            ->where(function ($exp) use ($today, $showroomId) {
                return $exp->eq('DATE(order_date)', $today)
                    ->notIn('status', ['Cancelled', 'Returned', 'Deleted'])
                    ->eq('showroom_id', $showroomId);
            })
            ->first();
        return $totalSales = $totalSalesQuery->total_sales ? $totalSalesQuery->total_sales : 0;
    }

    //S
    public function getRecentOrders($showroomId)
    {
        return $this->find()
            ->select(['Orders.id', 'Orders.order_number', 'Orders.total_amount', 'Orders.order_date', 'Orders.status', 'Customers.id', 'Users.id', 'Users.first_name', 'Users.last_name'])
            ->contain([
                'Customers' => [
                    'Users' => [
                        'fields' => ['Users.id', 'Users.first_name', 'Users.last_name']
                    ]
                ]
            ])
            ->where(['Orders.showroom_id' => $showroomId, 'Orders.status' => 'Pending'])
            ->order(['Orders.order_date' => 'DESC'])
            ->all();
    }

    //S
    public function listOrderByShowroom($showroom, $filter_order_status, $filter_payment_status, $filter_sdate, $time_period, $startDate, $endDate, $search_str, $page, $limit)
    {

        // Build the query with pagination
        $ordersQuery = $this->find()
            ->select([
                'Orders.id',
                'Orders.order_number',
                'Orders.order_date',
                'Orders.total_amount',
                'Orders.status',
                /* 'quantity' => $this->OrderItems->find()->func()->sum('OrderItems.quantity'),*/
                'Showrooms.id',
                'Showrooms.name',
                'customer_name' => $this->Customers->Users->find()->func()->concat([
                    "Users.first_name" => 'identifier',
                    ' ',
                    "Users.last_name" => 'identifier',
                ]),
                'payment_method' => 'Transactions.payment_method',
                'payment_status' => 'Transactions.payment_status'
            ])
            ->contain([
                'OrderItems' => function ($q) {
                    return $q->select([
                        'order_id',
                        'total_quantity' => $q->func()->sum('OrderItems.quantity')
                    ])
                        ->group('OrderItems.order_id'); // Group by order_id
                },
                'Customers' => [
                    'Users' => function ($q1) {
                        return $q1->select(['Users.id', 'Users.first_name', 'Users.last_name']);
                    }
                ]
            ])
            ->join([
                'Showrooms' => [
                    'table' => 'showrooms',
                    'type' => 'INNER',
                    'conditions' => 'Showrooms.id = Orders.showroom_id'
                ]
            ])
            ->join([
                'Transactions' => [
                    'table' => 'transactions',
                    'type' => 'INNER',
                    'conditions' => 'Transactions.order_id = Orders.id'
                ]
            ])
            /*->join(['OrderItems' => [
                'table' => 'order_items',
                'type' => 'INNER',
                'conditions' => 'OrderItems.order_id = Orders.id'
                ]
            ])*/
            ->where([
                'Orders.showroom_id IN' => $showroom
                /*'OR' => [
                    'Showrooms.showroom_manager' => $userId,
                    'Showrooms.showroom_supervisor' => $userId
                ]*/
            ])
            ->order(['Orders.created' => 'DESC'])
            ->page($page, $limit); // Pagination here

        // Apply filters if present
        if (!empty($filter_order_status)) {
            $ordersQuery->where(['Orders.status' => $filter_order_status]);
        }

        if (!empty($filter_payment_status)) {
            $ordersQuery->where(['Transactions.payment_status' => $filter_payment_status]);
        }

        if (!empty($search_str)) {
            $ordersQuery->where(['Orders.order_number LIKE' => '%' . $search_str . '%']);
        }

        // Filter by month
        /* if (!empty($filter_month)) {
            $ordersQuery->where(function ($exp) use ($filter_month) {
                $startOfMonth = $filter_month . '-01 00:00:00';
                $endOfMonth = date('Y-m-t 23:59:59', strtotime($startOfMonth));
                return $exp->between('Orders.order_date', $startOfMonth, $endOfMonth);
            });
        }*/

        // Filter by custom date range
        /*if (!empty($filter_sdate) && !empty($filter_edate)) {
            $ordersQuery->where(function ($exp) use ($filter_sdate, $filter_edate) {
                $start = $filter_sdate . ' 00:00:00';
                $end = $filter_edate . ' 23:59:59';
                return $exp->between('Orders.order_date', $start, $end);
            });
        }*/

        //Filter by date
        if (!empty($filter_sdate)) {
            $ordersQuery->where(['DATE(Orders.order_date)' => $filter_sdate]);
        }

        //Filter by time period
        if (!empty($time_period)) {
            $ordersQuery->where([
                'Orders.order_date >=' => $startDate,
                'Orders.order_date <=' => $endDate,
            ]);
        }

        // Execute the query
        $orders = $ordersQuery->all();
        return $orders->toArray();
    }

    //S
    public function orderDetail($orderId)
    {

        // Build the query with pagination
        $ordersQuery = $this->find()
            ->select([])
            ->leftJoinWith('Showrooms') // Ensures we still fetch data if showroom_id is NULL
            ->contain([
                'OrderItems' => [
                    'Products' => ['fields' => ['id', 'name', 'reference_name']],
                    'ProductVariants',
                    'OrderTrackingHistories' => [
                        'fields' => ['order_item_id', 'status', 'comment', 'updated'],
                    ]
                ],
                'Customers' => [
                    'fields' => ['id', 'profile_photo', 'phone_number', 'date_of_birth', 'gender'],
                    'Users' => ['fields' => ['id', 'first_name', 'last_name', 'email', 'country_code', 'mobile_no']],
                    'CustomerAddresses' => [
                        'fields' => ['customer_id', 'city_id', 'name', 'type', 'address_line1', 'address_line2', 'house_no', 'landmark', 'zipcode', 'phone_no1', 'phone_no2'],
                        'Cities' => ['fields' => ['id', 'city_name']],
                        'Municipalities' => ['fields' => ['id', 'name']]
                    ]
                ],
                /*'CustomerAddresses' => [
                    'fields' => ['city_id','name', 'type', 'address_line1','address_line2','house_no','landmark','zipcode','phone_no1', 'phone_no2'],
                    'Cities' => ['fields' => ['id', 'city_name']],
                    'Municipalities' => ['fields' => ['id', 'name']]
                ],*/
                'Transactions',
                'Offers' => ['fields' => ['offer_name', 'offer_code']],
                /*'Showrooms' => [
                    'fields' => ['city_id', 'name', 'address', 'area_sq_mts', 'image', 'email', 'contact_country_code', 'contact_number', 'showroom_timing'],
                    'Cities' => ['fields' => ['id', 'city_name']],
                    'ShowroomManager' => ['fields' => ['first_name', 'last_name']],
                    'ShowroomSupervisor' => ['fields' => ['first_name', 'last_name']],
                ],*/
                'Showrooms' => function ($q1) {
                    return $q1->select([
                        'Showrooms.id',
                        'Showrooms.city_id',
                        'Showrooms.name',
                        'Showrooms.address',
                        'Showrooms.area_sq_mts',
                        'Showrooms.image',
                        'Showrooms.email',
                        'Showrooms.contact_country_code',
                        'Showrooms.contact_number',
                        'Showrooms.showroom_timing'
                    ])
                        ->contain([
                            'Cities' => ['fields' => ['id', 'city_name']],
                            'ShowroomManager' => ['fields' => ['first_name', 'last_name']],
                            'ShowroomSupervisor' => ['fields' => ['first_name', 'last_name']]
                        ]);
                },
                'OrderTrackingHistories'
            ])
            ->where(['Orders.id' => $orderId])
            ->first();

        return $ordersQuery;
    }

    //S
    public function changeStatus($order_id, $data)
    {
        $order_entity = $this->get($order_id);
        $statusupdate['status'] = $data['status'];
        $statusupdate['last_modified_by'] = $data['last_modified_by'];
        $order_entity = $this->patchEntity($order_entity, $statusupdate);
        $res = $this->save($order_entity);
        if ($res) {
            return true;
        } else {
            return false;
        }
    }

    public function orderListByUser($customerId, $page, $limit)
    {
        $ordersQuery = $this->find()
            ->select([
                'Orders.id',
                'Orders.order_number',
                'Orders.order_date',
                'Orders.total_amount',
                'Orders.status',
                'payment_method' => 'Transactions.payment_method',
                'payment_status' => 'Transactions.payment_status'
            ])
            ->contain([
                //                'OrderItems' => function ($q) {
                //                    return $q->select([
                //                        'order_id',
                //                        'total_quantity' => $q->func()->sum('OrderItems.quantity')
                //                    ])
                //                        ->group('OrderItems.order_id');
                //                },
                'OrderItems' => [

                    'Products' => ['fields' => ['id', 'name', 'sales_price', 'promotion_price']],
                    'ProductVariants',
                    'OrderTrackingHistories' => [
                        'fields' => ['order_item_id', 'status', 'comment', 'updated'],
                    ]
                ],

                'CustomerAddresses' => [
                    'fields' => [
                        'id',
                        'customer_id',
                        'name',
                        'address_line1',
                        'address_line2',
                        'city_id',
                        'zipcode'
                    ],
                    // 'Cities' => ['fields' => ['id', 'city_name']]
                ],
                'Showrooms' => function ($q1) {
                    return $q1->select([
                        'Showrooms.id',
                        'Showrooms.city_id',
                        'Showrooms.name',
                        'Showrooms.address',
                        'Showrooms.area_sq_mts',
                        'Showrooms.image',
                        'Showrooms.email',
                        'Showrooms.contact_country_code',
                        'Showrooms.contact_number',
                        'Showrooms.showroom_timing'
                    ])
                        ->contain([
                            'ShowroomManager' => ['fields' => ['first_name', 'last_name']],
                            'ShowroomSupervisor' => ['fields' => ['first_name', 'last_name']]
                        ]);
                },
                'Offers' => ['fields' => ['offer_name', 'offer_code']],
                'Customers' => [
                    'fields' => ['id', 'profile_photo', 'phone_number', 'date_of_birth', 'gender'],
                    'Users' => ['fields' => ['id', 'first_name', 'last_name', 'email', 'mobile_no']],
                    'CustomerAddresses' => [
                        'fields' => ['customer_id', 'city_id', 'name', 'type', 'address_line1', 'address_line2', 'house_no', 'landmark', 'zipcode', 'phone_no1', 'phone_no2'],
                        'Cities' => ['fields' => ['id', 'city_name']],
                        'Municipalities' => ['fields' => ['id', 'name']]
                    ]
                ]
            ])
            ->join([
                'Transactions' => [
                    'table' => 'transactions',
                    'type' => 'INNER',
                    'conditions' => 'Transactions.order_id = Orders.id'
                ]
            ])
            ->where([
                'Orders.customer_id' => $customerId
            ])
            ->order(['Orders.created' => 'DESC'])
            ->page($page, $limit);

        $orders = $ordersQuery->all();
        return $orders->toArray();
    }

    //S
    public function salesPersonOrder($showroomId)
    {
        $today_date = date('Y-m-d');
        return $this->find()
            ->select(['Orders.id', 'Orders.order_number', 'Orders.total_amount', 'Orders.order_date', 'Orders.status', 'Orders.sales_person_id', 'Orders.sales_person_bonus',/* 'Customers.id',*/ 'SalesPersons.id', 'SalesPersons.first_name', 'SalesPersons.last_name'])
            ->contain([
                'SalesPersons',
                /*'Customers' => [
                    'Users' => [
                        'fields' => ['Users.id', 'Users.first_name', 'Users.last_name']
                    ]
                ]*/
            ])
            ->where(['Orders.showroom_id' => $showroomId, 'DATE(Orders.order_date)' => $today_date, 'Orders.sales_person_id IS NOT' => NULL,])
            ->order(['Orders.order_date' => 'DESC'])
            ->all();
    }

    /**
     * Get currency information for an order based on country_id
     * Reusable method to get currency details for order views
     *
     * @param int $countryId The country ID from the order
     * @return \App\Model\Entity\Currency|null Currency entity or null if not found
     */
    public function getOrderCurrency($countryId)
    {
        if (empty($countryId)) {
            return null;
        }

        $CurrenciesTable = TableRegistry::getTableLocator()->get('Currencies');

        $currency = $CurrenciesTable->find()
            ->where(['Currencies.country_id' => $countryId, 'Currencies.status' => 'A'])
            ->first();

        return $currency;
    }

    /**
     * Get formatted currency information for an order
     * Returns an array with currency details for easy use in views
     *
     * @param int $countryId The country ID from the order
     * @return array Currency information array
     */
    public function getOrderCurrencyInfo($countryId)
    {
        $currency = $this->getOrderCurrency($countryId);

        if (!$currency) {
            // Return default currency info if not found
            return [
                'code' => 'QAR',
                'symbol' => 'QR',
                'name' => 'Qatari Riyal',
                'symbol_position' => 'left',
                'decimal_places' => 2
            ];
        }

        return [
            'code' => $currency->code,
            'symbol' => $currency->symbol,
            'name' => $currency->name,
            'symbol_position' => $currency->symbol_position,
            'decimal_places' => $currency->decimal_places
        ];
    }

    /**
     * Format amount with currency for an order
     *
     * @param float $amount The amount to format
     * @param int $countryId The country ID from the order
     * @return string Formatted amount with currency symbol
     */
    public function formatOrderAmount($amount, $countryId)
    {
        $currencyInfo = $this->getOrderCurrencyInfo($countryId);
      

        // $decimalPlaces = $currencyInfo['decimal_places'] ?? 2;
        // $formattedAmount = number_format($amount, $decimalPlaces);

        if ($currencyInfo['symbol_position'] === 'right') {
            return number_format((float)$amount, 0). ' ' . $currencyInfo['code'];
        } else {
            return  number_format((float)$amount, 0). ' ' .$currencyInfo['code'] ;
        }
    }

}
