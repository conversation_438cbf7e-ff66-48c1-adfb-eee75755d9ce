<?php

/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Order $order
 */
?>
<?php $this->append('style'); ?>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" type="image/x-icon" href="../assets/logo.png" />
    <title>Invoice Template</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap"
        rel="stylesheet">

</head>
<style>
    table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 30px;
        margin-bottom: 20px;
        table-layout: auto;
        /* Ensures dynamic width adjustment */
    }

    th,
    td {
        padding: 10px;
        border-bottom: 2px solid #f0f0f0;
    }

    th {
        text-align: left;
        color: #666;
        font-weight: bold;
    }

    td {
        color: #333;
        vertical-align: middle;
        /* Ensures proper alignment */
    }

    td:nth-child(2),
    td:nth-child(3),
    th:nth-child(2),
    th:nth-child(3) {
        text-align: center;
    }

    td:nth-child(4),
    th:nth-child(4) {
        text-align: right;
    }
</style>
<?php $this->end(); ?>

<body style="font-family: Poppins, sans-serif; background-color: #f5f5f5; padding: 20px; color: #333;">
    <div id="invoiceSection">
        <div
            style="max-width: 900px; margin: 0 auto; background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);">
            <header style="display: flex;justify-content: space-between;">
                <img src="<?= $this->Url->webroot('img/ozone/logo.png') ?>" alt="Babiken Logo" style="max-width: 150px;max-height: 100px;" />
            </header>

            <div style="display: flex;align-items: center;justify-content: space-between;max-width: 900px;flex-wrap: wrap;">
                <table>
                    <tr>
                        <td style="margin: 20px;">
                            <p style="color: #555; font-size: 14px; margin-top: 5px; margin-bottom:2px;">Customer Care No:</p>
                            <p style="color: #555; font-weight: 600; font-size: 14px; margin:0px;margin-bottom: 10px;"><?= h($customer_care_no) ?></p>
                        </td>

                        <td style="margin: 20px;">
                            <p style="color: #555; font-size: 14px; margin-bottom: 0px; margin-top: 5px;">Customer Care Email ID:</p>
                            <a style="font-size: 14px; margin:0px; margin-bottom: 10px;" href="mailto:<?= h($customer_care_email) ?>" style="color: #555;"><?= h($customer_care_email) ?></a>
                        </td>

                        <td style="margin: 20px;">
                            <p style="color: #555; font-size: 14px; margin-top: 5px; margin-bottom:2px;">Warranty Period:</p>
                            <p style="color: #555; font-weight: 600; font-size: 14px; margin:0px;margin-bottom: 10px;">12 Months</p>
                        </td>

                        <td style="margin: 20px;">
                            <p style="color: #555; font-size: 14px; margin-top: 5px; margin-bottom:2px;">Call Center No:</p>
                            <p style="color: #555; font-weight: 600; font-size: 14px; margin:0px;margin-bottom: 10px;"><?= h($call_center_no) ?></p>
                        </td>

                        <td style="margin: 20px;">
                            <p style="color: #555; font-size: 14px; margin-top: 5px; margin-bottom:2px;">WhatsApp No:</p>
                            <p style="color: #555; font-weight: 600; font-size: 14px; margin:0px;margin-bottom: 10px;"><?= h($whatsapp_no) ?></p>
                        </td>
                    </tr>
                    <tr>

                        <!-- <td style="margin: 20px;">
                            <p style="color: #555; font-size: 14px; margin-top: 5px; margin-bottom:2px;">Babiken After Sales Mobile No:</p>
                            <p style="color: #555; font-weight: 600; font-size: 14px; margin:0px;margin-bottom: 10px;"><?= h($after_sales_no) ?></p>
                        </td> -->

                        <?php if (!empty($order->showroom_id)): ?>
                            <td style="margin: 20px;">
                                <p style="color: #555; font-size: 14px; margin-top: 5px; margin-bottom:2px;">Showroom Mobile No:</p>
                                <p style="color: #555; font-weight: 600; font-size: 14px; margin:0px;margin-bottom: 10px;">
                                    <?= h('+' . $order->showroom->contact_country_code . ' ' . $order->showroom->contact_number) ?>
                                </p>
                            </td>
                        <?php endif; ?>

                    </tr>
                </table>
            </div>

            <div
                style="background-color: #f0f0f0; padding: 20px; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1);">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <h2 style="margin: 0; font-weight: 600; font-size: 24px;">Invoice</h2>
                    <div>
                        <p style="margin: 0;font-weight: 400; color: #111;">Invoice No.</p>
                        <p style="margin: 0; font-weight: bold; font-size: 18px;"><?= h($order->order_number) ?></p>
                    </div>
                </div>


                <div style="display: flex;gap:30px; align-items: flex-start; margin-top: 20px;">
                    <div>
                        <h3 style="font-size: 14px; color: #555; margin: 0;">Billed To:</h3>
                        <p style="margin: 5px 0 0; font-weight: bold; font-size: 16px;"><?= h($order->customer->user->first_name . ' ' . $order->customer->user->last_name) ?></p>
                        <?php if (!empty($order->customer_address)): ?>
                            <p style="margin: 5px 0; color: #555;">
                                <?= h($order->customer_address->house_no ?? '') ?>
                                <?= !empty($order->customer_address->address_line1) ? ', ' . h($order->customer_address->address_line1) : '' ?>
                                <?= !empty($order->customer_address->address_line2) ? ', ' . h($order->customer_address->address_line2) : '' ?>
                                <?= !empty($order->customer_address->city) ? ', ' . h($order->customer_address->city->city_name) : '' ?>
                                <!-- <?= !empty($order->customer_address->municipality) ? '- ' . h($order->customer_address->municipality->name) : '' ?> -->
                                <?= !empty($order->customer_address->zipcode) ? ', ' . h($order->customer_address->zipcode) : '' ?>
                            </p>
                        <?php endif; ?>


                    </div>
                    <div>
                        <?php if ($order->delivery_mode === 'delivery'): ?>
                            <h3 style="font-size: 14px; color: #555; margin: 0;">Delivered To:</h3>

                            <p style="margin: 5px 0 0; font-weight: bold; font-size: 16px;"><?= h($order->customer->user->first_name . ' ' . $order->customer->user->last_name) ?></p>
                            <p style="margin: 5px 0; color: #555;"><?= h($order->customer_address->house_no) ?>,
                                <?= h($order->customer_address->address_line1) ?>,
                                <?= h($order->customer_address->address_line2) ?>,
                                <?= !empty($order->customer_address->city) ? h($order->customer_address->city->city_name) : '' ?> -
                                <!-- <?= !empty($order->customer_address->municipality) ? h($order->customer_address->municipality->name) : '' ?> -->
                                <?= h($order->customer_address->zipcode) ?>
                            </p>
                        <?php elseif ($order->delivery_mode === 'pickup'): ?>
                            <h3 style="font-size: 14px; color: #555; margin: 0;">Picked Up from:</h3>
                            <p style="margin: 5px 0 0; font-weight: bold; font-size: 16px;"><?= h($order->showroom->name) ?></p>
                            <p style="margin: 5px 0; color: #555;"><?= h($order->showroom->address) ?>,
                                <?= !empty($order->showroom->city_id) ? h($order->showroom->city->name) : '' ?>
                            <?php endif; ?>

                    </div>

                </div>
                <div style="display: flex; justify-content: space-between;  align-items: flex-start; margin-top: 20px;">
                    <div>
                        <span>Mobile Number:</span>
                        <!-- <p style="margin: 5px 0; font-weight: 600; color: #555;"><?= h('(' . $order->customer->user->country_code . ') ' . $order->customer->user->mobile_no) ?></p> -->
                         <p style="margin: 5px 0; font-weight: 600; color: #555;">
                            <?= h(
                                (!empty($order->customer->user->country_code) ? '(' . $order->customer->user->country_code . ') ' : '') .
                                $order->customer->user->mobile_no
                            ) ?>
                        </p>

                    </div>
                    <div>
                        <span>E Mail ID: </span>
                        <p style="margin: 5px 0;font-weight: 600; color: #555;"><?= h($order->customer->user->email) ?></p>

                    </div>
                    <div>
                        <span>Order Date: </span>
                        <p style="margin: 5px 0;font-weight: 600; color: #555;"><?= h($order->order_date->i18nFormat('dd MMM yyyy')) ?></p>

                    </div>
                    <div>
                        <span>Order No: </span>
                        <p style="margin: 5px 0;font-weight: 600; color: #555;"><?= h($order->order_number) ?></p>

                    </div>
                    <div>
                        <span>Delivered Date: </span>
                        <p style="margin: 5px 0;font-weight: 600; color: #555;"><?= $order->delivery_date ? h($order->delivery_date->i18nFormat('dd MMM yyyy')) : '' ?>
                        </p>

                    </div>
                </div>
            </div>

            <table>
                <thead>
                    <tr>
                        <th>Item</th>
                        <th>Qty.</th>
                        <th>Unit Price</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($order->order_items as $item): ?>
                        <tr>
                            <td>
                                <?= h($item->product_variant_id ? $item->product_variant->variant_name : $item->product->name) ?><br>
                                <small><?= h($item->product_variant_id ? $item->product_variant->variant_size : $item->product->product_size) ?></small>
                            </td>
                            <td><?= h($item->quantity) ?> Unit</td>
                            <!-- <td><?= h($item->price) ?>&nbsp;<?= $currencyCode ?></td> -->
                             <td>
                                <?= $this->Price->formatOrderAmount($item->price,$order->country_id ) ?>
                             </td>
                            <td>
                                <?php $total = $item->quantity * $item->price; ?>
                                <!-- <?= h(number_format($total, 2)) ?> &nbsp;<?= $currencyCode ?> -->
                                  <?= $this->Price->formatOrderAmount($total,$order->country_id ) ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>

            <div style="display: flex; justify-content: flex-end; margin-bottom: 20px;">
                <div style="width: 100%; max-width: 400px;padding: 20px; background-color: #f5f5f5; border-radius:20px">
                    <table style="width: 100%; border-radius: 8px; text-align: left;background-color: #f5f5f5;">
                        <tbody style="background-color: #f5f5f5;">
                            <tr style="background-color: #f5f5f5;">
                                <td colspan="2" style="font-size: 16px; font-weight: bold; color: #333;">
                                    Price (<?= h($orderItemCount) ?> item(s))
                                </td>
                                <td style="font-size: 16px; font-weight: bold; text-align: right; color: #333;">
                                    <!-- <?= h($order->subtotal_amount) ?> <?= $currencyCode ?> -->
                                     <?= $this->Price->formatOrderAmount($order->subtotal_amount,$order->country_id ) ?>
                                </td>
                            </tr>
                            <tr style="background-color: #f5f5f5;">
                                <td colspan="3" style="padding: 8px 0; border-bottom: 1px solid #ddd;"></td>
                            </tr>
                            <tr style="background-color: #f5f5f5;">
                                <td colspan="2" style="font-size: 14px; padding: 8px 0; color: #666;">Discount</td>
                                <td style="text-align: right; color: #333;">
                                    <!-- <?= h($order->discount_amount) ?> <?= $currencyCode ?> -->
                                     <?= $this->Price->formatOrderAmount($order->discount_amount,$order->country_id ) ?>
                                </td>
                            </tr>
                            <!-- <tr style="background-color: #f5f5f5;">
                                <td colspan="2" style="font-size: 14px; padding: 8px 0; color: #666;">
                                    Coupons <small class="small"><?= !empty($order->offer) ? '(' . h($order->offer->offer_code) . ')' : '' ?></small>
                                </td>
                                <td style="text-align: right; color: #333;">
                                   
                                       <?= $this->Price->formatOrderAmount($order->offer_amount,$order->country_id ) ?>
                                </td>
                            </tr> -->
                            <!-- <tr style="background-color: #f5f5f5;">
                                <td colspan="2" style="font-size: 14px; padding: 8px 0; color: #666;">Redeemed Loyalty Points</td>
                                <td style="text-align: right; color: #333;">0.00 FCFA</td>
                            </tr> -->
                            <?php  if (!empty($order->tax_amount && $order->tax_amount > 0)): ?>
                            <tr style="background-color: #f5f5f5;">
                                <td colspan="2" style="font-size: 14px; padding: 8px 0; color: #666;">Tax Amount</td>
                                <td style="text-align: right; color: #333;">  <?= h($order->tax_amount) > 0 ? $this->Price->formatOrderAmount($order->tax_amount,$order->country_id ) : '' ?>  </td>
                            </tr>
                            <?php endif; ?>

                             <?php  if (!empty($order->installation_amount && $order->installation_amount > 0)): ?>
                            <tr style="background-color: #f5f5f5;">
                                <td colspan="2" style="font-size: 14px; padding: 8px 0; color: #666;">Installation Amount</td>
                                <td style="text-align: right; color: #333;">  <?= h($order->installation_amount) > 0 ? $this->Price->formatOrderAmount($order->tax_amount,$order->country_id ) : '' ?>  </td>
                            </tr>
                            <?php endif; ?>


                            <tr style="background-color: #f5f5f5;">
                                <td colspan="2" style="font-size: 14px; padding: 8px 0; color: #666;">Delivery Charges</td>
                                <td style="text-align: right; color: #333;">
            
                                       <?= h($order->delivery_charge) > 0 ? $this->Price->formatOrderAmount($order->delivery_charge,$order->country_id ) : '' ?> 
                                </td>
                            </tr>
                            <tr style="background-color: #f5f5f5;">
                                <td colspan="3" style="padding: 8px 0; border-bottom: 1px solid #ddd;"></td>
                            </tr>
                            <tr style="background-color: #f5f5f5;">
                                <td colspan="2" style="font-size: 16px; padding: 8px 0; font-weight: bold; color: #333;">Total (FCFA)</td>
                                <td style="font-size: 18px; font-weight: bold; text-align: right; color: #333;">
                                    <!-- <?= h($order->total_amount) ?> <?= $currencyCode ?> -->
                                     <?= $this->Price->formatOrderAmount($order->total_amount,$order->country_id ) ?>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

        </div>
    </div>

    <div style="text-align: center; margin-top: 20px;">
        <a href="#" id="downloadPdf"
            style="display: inline-block; min-width: 220px; padding: 10px 20px; background-color: #00a0e3; color: #fff; text-decoration: none; border-radius: 8px; margin-top: 20px; font-size: 16px;">
            Download
        </a>
    </div>

    </div>
</body>

<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/datatables/datatables.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/js/dataTables.bootstrap4.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/select2/dist/js/select2.full.min.js') ?>"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>

<script>
    $(document).ready(function() {
        $(document).on("click", "#downloadPdf", function() {
            var invoiceHtml = $("#invoiceSection").html();
             //var invoiceHtml='<div>dsdsdsdsdsdsd</div>';
            if (!invoiceHtml) {
                swal("Error", "Invoice content is empty!", "error");
                return;
            }

            // Create a form dynamically
            var form = $('<form>', {
                method: "POST",
                action: "<?= $this->Url->build(['controller' => 'Orders', 'action' => 'printInvoicePdf']) ?>",
                target: "_blank" // Open PDF in new tab
            });

            // Append the invoice HTML
            form.append($('<input>', {
                type: "hidden",
                name: "html",
                value: invoiceHtml
            }));

            // Append the CSRF token
            form.append($('<input>', {
                type: "hidden",
                name: "_csrfToken",
                value: "<?= $this->request->getAttribute('csrfToken') ?>"
            }));

            $("body").append(form);
            form.submit();
            form.remove();
        });
    });
</script>


<?php $this->end(); ?>