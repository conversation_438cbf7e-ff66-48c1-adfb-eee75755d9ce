<?php
/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\OrderItemReview $review
 */
?>

<div class="content-wrapper">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0"><?= __('Edit Review') ?></h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><?= $this->Html->link(__('Home'), ['controller' => 'Dashboard', 'action' => 'index']) ?></li>
                        <li class="breadcrumb-item"><?= $this->Html->link(__('Reviews'), ['action' => 'index']) ?></li>
                        <li class="breadcrumb-item active"><?= __('Edit') ?></li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-8">
                    <!-- Edit Form -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title"><?= __('Edit Review Information') ?></h3>
                        </div>
                        <?= $this->Form->create($review, ['class' => 'form-horizontal']) ?>
                        <div class="card-body">
                            <div class="form-group row">
                                <label class="col-sm-3 col-form-label"><?= __('Rating') ?></label>
                                <div class="col-sm-9">
                                    <div class="rating-input">
                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                            <label class="star-label">
                                                <?= $this->Form->radio('rating', [
                                                    ['value' => $i, 'text' => '']
                                                ], [
                                                    'hiddenField' => false,
                                                    'class' => 'star-radio',
                                                    'checked' => $review->rating == $i
                                                ]) ?>
                                                <i class="<?= $i <= $review->rating ? 'fas' : 'far' ?> fa-star star-icon" data-rating="<?= $i ?>"></i>
                                            </label>
                                        <?php endfor; ?>
                                    </div>
                                    <small class="form-text text-muted"><?= __('Click on stars to change rating') ?></small>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-sm-3 col-form-label"><?= __('Review Text') ?></label>
                                <div class="col-sm-9">
                                    <?= $this->Form->control('review', [
                                        'type' => 'textarea',
                                        'class' => 'form-control',
                                        'rows' => 5,
                                        'label' => false,
                                        'placeholder' => __('Enter review text...')
                                    ]) ?>
                                    <small class="form-text text-muted"><?= __('Maximum 1000 characters') ?></small>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-sm-3 col-form-label"><?= __('Status') ?></label>
                                <div class="col-sm-9">
                                    <?= $this->Form->control('status', [
                                        'type' => 'select',
                                        'options' => $statusOptions,
                                        'class' => 'form-control',
                                        'label' => false
                                    ]) ?>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-sm-3 col-form-label"><?= __('Publish Status') ?></label>
                                <div class="col-sm-9">
                                    <?= $this->Form->control('publish_status', [
                                        'type' => 'select',
                                        'options' => $publishStatusOptions,
                                        'class' => 'form-control',
                                        'label' => false
                                    ]) ?>
                                    <small class="form-text text-muted">
                                        <strong><?= __('Pending:') ?></strong> <?= __('Review is waiting for approval') ?><br>
                                        <strong><?= __('Published:') ?></strong> <?= __('Review is visible to customers') ?><br>
                                        <strong><?= __('Rejected:') ?></strong> <?= __('Review is hidden from customers') ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <?= $this->Form->button(__('Update Review'), ['class' => 'btn btn-primary']) ?>
                            <?= $this->Html->link(__('Cancel'), ['action' => 'view', $review->id], ['class' => 'btn btn-secondary']) ?>
                        </div>
                        <?= $this->Form->end() ?>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- Review Information -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title"><?= __('Review Information') ?></h3>
                        </div>
                        <div class="card-body">
                            <strong><?= __('Review ID:') ?></strong>
                            <p class="text-muted">#<?= h($review->id) ?></p>
                            
                            <strong><?= __('Created:') ?></strong>
                            <p class="text-muted"><?= h($review->created->format('Y-m-d H:i:s')) ?></p>
                            
                            <strong><?= __('Last Modified:') ?></strong>
                            <p class="text-muted"><?= h($review->modified->format('Y-m-d H:i:s')) ?></p>
                        </div>
                    </div>

                    <!-- Customer Information -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title"><?= __('Customer') ?></h3>
                        </div>
                        <div class="card-body">
                            <strong><?= __('Name:') ?></strong>
                            <p class="text-muted"><?= h($review->customer->user->first_name . ' ' . $review->customer->user->last_name) ?></p>
                            
                            <strong><?= __('Email:') ?></strong>
                            <p class="text-muted"><?= h($review->customer->user->email) ?></p>
                        </div>
                    </div>

                    <!-- Product Information -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title"><?= __('Product') ?></h3>
                        </div>
                        <div class="card-body">
                            <strong><?= __('Product Name:') ?></strong>
                            <p class="text-muted"><?= h($review->product->name) ?></p>
                            
                            <strong><?= __('Product ID:') ?></strong>
                            <p class="text-muted">#<?= h($review->product_id) ?></p>
                        </div>
                    </div>

                    <!-- Order Information -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title"><?= __('Order') ?></h3>
                        </div>
                        <div class="card-body">
                            <strong><?= __('Order Number:') ?></strong>
                            <p class="text-muted">#<?= h($review->order->order_number ?? $review->order_id) ?></p>
                            
                            <strong><?= __('Order Status:') ?></strong>
                            <p>
                                <span class="badge badge-<?= $review->order->status === 'Delivered' ? 'success' : 'info' ?>">
                                    <?= h($review->order->status) ?>
                                </span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<style>
.rating-input {
    display: flex;
    align-items: center;
    gap: 5px;
}

.star-label {
    cursor: pointer;
    margin: 0;
    display: flex;
    align-items: center;
}

.star-radio {
    display: none;
}

.star-icon {
    font-size: 1.5rem;
    color: #ddd;
    transition: color 0.2s ease;
}

.star-icon:hover,
.star-icon.active {
    color: #ffc107;
}

.star-label:hover .star-icon,
.star-label:hover ~ .star-label .star-icon {
    color: #ffc107;
}
</style>

<script>
$(document).ready(function() {
    // Initialize star rating
    initializeStarRating();
    
    // Character counter for review text
    var reviewTextarea = $('textarea[name="review"]');
    var maxLength = 1000;
    
    reviewTextarea.on('input', function() {
        var currentLength = $(this).val().length;
        var remaining = maxLength - currentLength;
        
        var helpText = $(this).siblings('.form-text');
        if (remaining < 0) {
            helpText.text('<?= __('Review text is too long! Please reduce by {0} characters.') ?>'.replace('{0}', Math.abs(remaining)));
            helpText.removeClass('text-muted').addClass('text-danger');
        } else {
            helpText.text('<?= __('Maximum 1000 characters') ?> (' + remaining + ' <?= __('remaining') ?>)');
            helpText.removeClass('text-danger').addClass('text-muted');
        }
    });
});

function initializeStarRating() {
    var currentRating = <?= $review->rating ?>;
    updateStarDisplay(currentRating);
    
    $('.star-label').on('click', function() {
        var rating = $(this).find('.star-icon').data('rating');
        $('input[name="rating"][value="' + rating + '"]').prop('checked', true);
        updateStarDisplay(rating);
    });
    
    $('.star-label').on('mouseenter', function() {
        var rating = $(this).find('.star-icon').data('rating');
        updateStarDisplay(rating, true);
    });
    
    $('.rating-input').on('mouseleave', function() {
        var checkedRating = $('input[name="rating"]:checked').val() || 0;
        updateStarDisplay(checkedRating);
    });
}

function updateStarDisplay(rating, isHover = false) {
    $('.star-icon').each(function() {
        var starRating = $(this).data('rating');
        if (starRating <= rating) {
            $(this).removeClass('far').addClass('fas');
            $(this).css('color', '#ffc107');
        } else {
            $(this).removeClass('fas').addClass('far');
            $(this).css('color', '#ddd');
        }
    });
}
</script>
