<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * OrderItemReviews Model
 *
 * @property \App\Model\Table\CustomersTable&\Cake\ORM\Association\BelongsTo $Customers
 * @property \App\Model\Table\OrdersTable&\Cake\ORM\Association\BelongsTo $Orders
 * @property \App\Model\Table\OrderItemsTable&\Cake\ORM\Association\BelongsTo $OrderItems
 * @property \App\Model\Table\ProductsTable&\Cake\ORM\Association\BelongsTo $Products
 *
 * @method \App\Model\Entity\OrderItemReview newEmptyEntity()
 * @method \App\Model\Entity\OrderItemReview newEntity(array $data, array $options = [])
 * @method \App\Model\Entity\OrderItemReview[] newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\OrderItemReview get($primaryKey, $options = [])
 * @method \App\Model\Entity\OrderItemReview findOrCreate($search, ?callable $callback = null, $options = [])
 * @method \App\Model\Entity\OrderItemReview patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method \App\Model\Entity\OrderItemReview[] patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\OrderItemReview|false save(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\OrderItemReview saveOrFail(\Cake\Datasource\EntityInterface $entity, $options = [])
 * @method \App\Model\Entity\OrderItemReview[]|\Cake\Datasource\ResultSetInterface|false saveMany(iterable $entities, $options = [])
 * @method \App\Model\Entity\OrderItemReview[]|\Cake\Datasource\ResultSetInterface saveManyOrFail(iterable $entities, $options = [])
 * @method \App\Model\Entity\OrderItemReview[]|\Cake\Datasource\ResultSetInterface|false deleteMany(iterable $entities, $options = [])
 * @method \App\Model\Entity\OrderItemReview[]|\Cake\Datasource\ResultSetInterface deleteManyOrFail(iterable $entities, $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class OrderItemReviewsTable extends Table
{
    /**
     * Initialize method
     *
     * @param array $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('order_item_reviews');
        $this->setDisplayField('id');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');

        $this->belongsTo('Customers', [
            'foreignKey' => 'customer_id',
            'joinType' => 'INNER',
        ]);
        $this->belongsTo('Orders', [
            'foreignKey' => 'order_id',
            'joinType' => 'LEFT',
        ]);
        $this->belongsTo('OrderItems', [
            'foreignKey' => 'order_item_id',
            'joinType' => 'LEFT',
        ]);
        $this->belongsTo('Products', [
            'foreignKey' => 'product_id',
            'joinType' => 'INNER',
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->integer('customer_id')
            ->notEmptyString('customer_id');

        $validator
            ->integer('order_id')
            ->allowEmptyString('order_id');

        $validator
            ->integer('order_item_id')
            ->allowEmptyString('order_item_id');

        $validator
            ->integer('product_id')
            ->notEmptyString('product_id');

        $validator
            ->integer('rating')
            ->requirePresence('rating', 'create')
            ->notEmptyString('rating')
            ->range('rating', [1, 5], __('Rating must be between 1 and 5 stars'));

        $validator
            ->scalar('review')
            ->allowEmptyString('review')
            ->maxLength('review', 1000, __('Review cannot exceed 1000 characters'));

        $validator
            ->inList('status', ['pending', 'approved', 'rejected'])
            ->notEmptyString('status');

        $validator
            ->inList('publish_status', ['pending', 'published', 'rejected'])
            ->notEmptyString('publish_status');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->existsIn('customer_id', 'Customers'), ['errorField' => 'customer_id']);
        // $rules->add($rules->existsIn('order_id', 'Orders'), ['errorField' => 'order_id']);
        // $rules->add($rules->existsIn('order_item_id', 'OrderItems'), ['errorField' => 'order_item_id']);
        $rules->add($rules->existsIn('product_id', 'Products'), ['errorField' => 'product_id']);

        // Ensure customer can only review their own orders
        // $rules->add(function ($entity, $options) {
        //     if ($entity->isNew()) {
        //         $orderItemReviewsTable = $options['repository'];
        //         $order = $orderItemReviewsTable->Orders->get($entity->order_id);
        //         return $order->customer_id === $entity->customer_id;
        //     }
        //     return true;
        // }, ['errorField' => 'customer_id', 'message' => __('You can only review your own orders')]);

        // Prevent duplicate reviews for the same order item
        // $rules->add(function ($entity, $options) {
        //     if ($entity->isNew()) {
        //         $orderItemReviewsTable = $options['repository'];
        //         $existingReview = $orderItemReviewsTable->find()
        //             ->where([
        //                 'customer_id' => $entity->customer_id,
        //                 'order_item_id' => $entity->order_item_id,
        //                 'status !=' => 'Deleted'
        //             ])
        //             ->first();
        //         return !$existingReview;
        //     }
        //     return true;
        // }, ['errorField' => 'order_item_id', 'message' => __('You have already reviewed this product')]);

        return $rules;
    }

    /**
     * Get reviews for a specific order
     *
     * @param int $orderId
     * @return \Cake\ORM\Query
     */
    public function findByOrder($orderId)
    {
        return $this->find()
            ->contain(['Products', 'OrderItems'])
            ->where(['OrderItemReviews.order_id' => $orderId])
            ->where(['OrderItemReviews.status' => 'Active']);
    }

    /**
     * Get average rating for a product
     *
     * @param int $productId
     * @return float
     */
    public function getProductAverageRating($productId)
    {
        $result = $this->find()
            ->select(['avg_rating' => $this->find()->func()->avg('rating')])
            ->where([
                'product_id' => $productId,
                'status' => 'Active',
                'publish_status' => 'published'
            ])
            ->first();

        return $result ? round($result->avg_rating, 1) : 0;
    }

    /**
     * Get total review count for a product
     *
     * @param int $productId
     * @return int
     */
    public function getProductReviewCount($productId)
    {
        return $this->find()
            ->where([
                'product_id' => $productId,
                'status' => 'Active',
                'publish_status' => 'published'
            ])
            ->count();
    }

    /**
     * Check if customer can review order items
     *
     * @param int $customerId
     * @param int $orderId
     * @return bool
     */
    public function canCustomerReview($customerId, $orderId)
    {
        // Check if order is delivered
        $order = $this->Orders->find()
            ->where([
                'id' => $orderId,
                'customer_id' => $customerId,
                'status' => 'Delivered'
            ])
            ->first();

        return (bool)$order;
    }

    /**
     * Get reviewable order items for customer
     *
     * @param int $customerId
     * @param int $orderId
     * @return \Cake\ORM\Query
     */
    public function getReviewableOrderItems($customerId, $orderId)
    {
        return $this->OrderItems->find()
            ->contain(['Products' => ['ProductImages']])
            ->leftJoinWith('OrderItemReviews', function ($q) use ($customerId) {
                return $q->where(['OrderItemReviews.customer_id' => $customerId]);
            })
            ->where([
                'OrderItems.order_id' => $orderId,
                'Orders.customer_id' => $customerId,
                'Orders.status' => 'Delivered',
                'OrderItemReviews.id IS' => null // Not yet reviewed
            ])
            ->innerJoinWith('Orders');
    }

    /**
     * Get product reviews for web display with pagination and sorting
     *
     * @param int $productId
     * @param string $sortBy
     * @param int $page
     * @param int $limit
     * @return array
     */
    public function webProductReviews($productId, $sortBy = 'newest', $page = 1, $limit = 2)
    {
        $query = $this->find()
            ->where([
                'OrderItemReviews.product_id' => $productId,
                'OrderItemReviews.status' => 'approved'
            ])
            ->contain([
                'Customers' => function ($q) {
                    return $q->select(['id', 'profile_photo'])
                        ->contain([
                            'Users' => function ($q2) {
                                return $q2->select(['id', 'first_name', 'last_name', 'role']);
                            }
                        ]);
                }
            ]);

        // Apply sorting
        switch ($sortBy) {
            case 'oldest':
                $query->order(['OrderItemReviews.created' => 'ASC']);
                break;
            case 'highest':
                $query->order(['OrderItemReviews.rating' => 'DESC', 'OrderItemReviews.created' => 'DESC']);
                break;
            case 'lowest':
                $query->order(['OrderItemReviews.rating' => 'ASC', 'OrderItemReviews.created' => 'DESC']);
                break;
            case 'newest':
            default:
                $query->order(['OrderItemReviews.created' => 'DESC']);
                break;
        }

        // Get total count for pagination
        $totalCount = $query->count();

        // Apply pagination
        $offset = ($page - 1) * $limit;
        $reviews = $query->limit($limit)->offset($offset)->toArray();

        // Calculate pagination info
        $totalPages = ceil($totalCount / $limit);
        $hasNextPage = $page < $totalPages;
        $hasPrevPage = $page > 1;

        return [
            'status' => 'success',
            'data' => [
                'items' => $reviews,
                'pagination' => [
                    'current_page' => $page,
                    'total_pages' => $totalPages,
                    'total_count' => $totalCount,
                    'has_next' => $hasNextPage,
                    'has_prev' => $hasPrevPage,
                    'limit' => $limit
                ]
            ]
        ];
    }
}
